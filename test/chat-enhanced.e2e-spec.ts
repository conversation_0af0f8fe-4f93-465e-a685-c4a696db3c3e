import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Enhanced Chat System (e2e)', () => {
  let app: INestApplication;
  let sessionId: string;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /chat - Enhanced Intent Classification', () => {
    it('should extract filters from natural language and provide intelligent responses', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need a beginner-friendly AI tool with API access for Python machine learning projects under $50',
          session_id: undefined, // Will create new session
        })
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('session_id');
      expect(response.body).toHaveProperty('conversation_stage');
      expect(response.body).toHaveProperty('discovered_entities');
      
      sessionId = response.body.session_id;
      
      // Should discover relevant entities based on extracted filters
      expect(response.body.discovered_entities).toBeDefined();
      expect(Array.isArray(response.body.discovered_entities)).toBe(true);
    });

    it('should build conversation context progressively', async () => {
      // First message - establish basic intent
      const response1 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I want to learn machine learning',
          session_id: sessionId,
        })
        .expect(200);

      expect(response1.body.conversation_stage).toBe('discovery');

      // Second message - add more specific requirements
      const response2 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I prefer online courses with certificates and I am a beginner',
          session_id: sessionId,
        })
        .expect(200);

      expect(response2.body.discovered_entities).toBeDefined();
      // Should have accumulated filters from both messages
    });

    it('should ask intelligent clarifying questions', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need help with AI',
          session_id: undefined, // New session
        })
        .expect(200);

      expect(response.body).toHaveProperty('suggested_actions');
      expect(response.body).toHaveProperty('follow_up_questions');
      
      // Should ask clarifying questions for vague requests
      if (response.body.follow_up_questions) {
        expect(response.body.follow_up_questions.length).toBeGreaterThan(0);
      }
    });

    it('should handle job-specific conversations', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I want a senior machine learning engineering job that pays well and allows remote work',
          session_id: undefined,
        })
        .expect(200);

      expect(response.body.discovered_entities).toBeDefined();
      expect(response.body.conversation_stage).toBeDefined();
      
      // Should extract job-specific filters and find relevant job entities
    });

    it('should handle course-specific conversations', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I want to learn AI through online courses with certificates, I am a beginner',
          session_id: undefined,
        })
        .expect(200);

      expect(response.body.discovered_entities).toBeDefined();
      // Should extract course-specific filters
    });

    it('should handle event-specific conversations', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I want to attend AI conferences online in 2024',
          session_id: undefined,
        })
        .expect(200);

      expect(response.body.discovered_entities).toBeDefined();
      // Should extract event-specific filters
    });

    it('should handle hardware-specific conversations', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need a powerful GPU for training machine learning models on a budget under $2000',
          session_id: undefined,
        })
        .expect(200);

      expect(response.body.discovered_entities).toBeDefined();
      // Should extract hardware-specific filters
    });

    it('should handle mixed entity type conversations', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need resources to learn AI - both tools and courses would be helpful',
          session_id: undefined,
        })
        .expect(200);

      expect(response.body.discovered_entities).toBeDefined();
      // Should handle multiple entity types
    });

    it('should provide contextual follow-up questions', async () => {
      // Start with a vague request
      const response1 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need something for AI',
          session_id: undefined,
        })
        .expect(200);

      const newSessionId = response1.body.session_id;

      // Should ask for clarification
      expect(response1.body.follow_up_questions || response1.body.suggested_actions).toBeDefined();

      // Provide more specific information
      const response2 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I want AI tools for my startup',
          session_id: newSessionId,
        })
        .expect(200);

      // Should ask more specific questions now
      expect(response2.body.discovered_entities).toBeDefined();
    });

    it('should transition to recommendations when ready', async () => {
      const newSessionId = `test-session-${Date.now()}`;

      // Build up conversation context
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need an AI tool',
          session_id: newSessionId,
        })
        .expect(200);

      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'For machine learning projects',
          session_id: newSessionId,
        })
        .expect(200);

      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I am a beginner and need API access, budget is under $50',
          session_id: newSessionId,
        })
        .expect(200);

      // Should have enough context for recommendations
      expect(response.body).toHaveProperty('should_transition_to_recommendations');
      expect(response.body.discovered_entities).toBeDefined();
      expect(response.body.discovered_entities.length).toBeGreaterThan(0);
    });

    it('should handle filter corrections intelligently', async () => {
      const newSessionId = `test-session-${Date.now()}`;

      // Initial request
      const response1 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need an advanced AI tool',
          session_id: newSessionId,
        })
        .expect(200);

      // Correction
      const response2 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'Actually, I am a beginner, not advanced',
          session_id: newSessionId,
        })
        .expect(200);

      // Should acknowledge the correction
      expect(response2.body.message).toMatch(/got it|updated|corrected|thanks/i);
    });

    it('should ask strategic clarifying questions', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need help with AI',
          session_id: undefined,
        })
        .expect(200);

      // Should ask strategic questions for vague requests
      expect(response.body.follow_up_questions || response.body.suggested_actions).toBeDefined();

      if (response.body.follow_up_questions) {
        expect(response.body.follow_up_questions.length).toBeGreaterThan(0);
      }
    });

    it('should progressively build conversation context', async () => {
      const newSessionId = `test-session-${Date.now()}`;

      // Message 1: Basic intent
      const response1 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I want to learn machine learning',
          session_id: newSessionId,
        })
        .expect(200);

      // Message 2: Add technical level
      const response2 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I am a beginner',
          session_id: newSessionId,
        })
        .expect(200);

      // Message 3: Add format preference
      const response3 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I prefer online courses with certificates',
          session_id: newSessionId,
        })
        .expect(200);

      // Should have accumulated filters and found relevant entities
      expect(response3.body.discovered_entities).toBeDefined();
      expect(response3.body.conversation_stage).toBeDefined();
    });

    it('should handle conversation flow optimization', async () => {
      const newSessionId = `test-session-${Date.now()}`;

      // Send multiple messages to test flow optimization
      const messages = [
        'I need something for AI',
        'Maybe tools or courses',
        'For my startup',
        'I am a beginner',
        'Budget is important'
      ];

      let lastResponse;
      for (const message of messages) {
        lastResponse = await request(app.getHttpServer())
          .post('/chat')
          .send({
            message,
            session_id: newSessionId,
          })
          .expect(200);
      }

      // Should have optimized the conversation and be ready for recommendations
      expect(lastResponse.body).toHaveProperty('discovered_entities');
      expect(lastResponse.body.discovered_entities.length).toBeGreaterThan(0);
    });
  });

  describe('Conversation Management', () => {
    it('should maintain conversation state across messages', async () => {
      const newSessionId = `test-session-${Date.now()}`;

      // First message
      const response1 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need AI tools',
          session_id: newSessionId,
        })
        .expect(200);

      expect(response1.body.session_id).toBe(newSessionId);

      // Second message in same session
      const response2 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'For computer vision projects',
          session_id: newSessionId,
        })
        .expect(200);

      expect(response2.body.session_id).toBe(newSessionId);
      // Should build upon previous context
    });

    it('should handle conversation history retrieval', async () => {
      const newSessionId = `test-session-${Date.now()}`;

      // Send a few messages
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need AI tools',
          session_id: newSessionId,
        })
        .expect(200);

      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'For machine learning',
          session_id: newSessionId,
        })
        .expect(200);

      // Retrieve history
      const historyResponse = await request(app.getHttpServer())
        .get(`/chat/${newSessionId}/history`)
        .expect(200);

      expect(historyResponse.body).toHaveProperty('messages');
      expect(Array.isArray(historyResponse.body.messages)).toBe(true);
      expect(historyResponse.body.messages.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty messages gracefully', async () => {
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: '',
          session_id: undefined,
        })
        .expect(400);
    });

    it('should handle very long messages', async () => {
      const longMessage = 'I need an AI tool '.repeat(100);
      
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: longMessage,
          session_id: undefined,
        })
        .expect(200);

      expect(response.body.message).toBeDefined();
    });

    it('should handle invalid session IDs gracefully', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'Hello',
          session_id: 'invalid-session-id',
        })
        .expect(200);

      // Should create new session or handle gracefully
      expect(response.body.session_id).toBeDefined();
    });
  });

  describe('Performance', () => {
    it('should respond within reasonable time limits', async () => {
      const startTime = Date.now();
      
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need an AI tool for data analysis',
          session_id: undefined,
        })
        .expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(15000); // Should respond within 15 seconds
    });
  });
});
