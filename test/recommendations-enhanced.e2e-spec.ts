import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Enhanced Recommendations System (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /recommendations - Enhanced Filtering', () => {
    it('should extract filters from natural language and apply comprehensive filtering', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need a beginner-friendly AI tool with API access for Python machine learning projects under $50',
          filters: {
            max_candidates: 15,
          },
        })
        .expect(200);

      expect(response.body).toHaveProperty('recommended_entities');
      expect(response.body).toHaveProperty('explanation');
      expect(response.body).toHaveProperty('candidates_analyzed');
      expect(response.body.candidates_analyzed).toBeGreaterThan(0);
    });

    it('should handle tool-specific filters comprehensively', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need an AI tool for computer vision',
          filters: {
            entityTypeIds: ['ai-tool'],
            technical_levels: ['BEGINNER', 'INTERMEDIATE'],
            has_api: true,
            has_free_tier: true,
            frameworks: ['TensorFlow', 'OpenCV'],
            platforms: ['Web', 'Linux'],
            use_cases_search: 'computer vision',
            key_features_search: 'image processing',
            max_candidates: 20,
          },
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
      expect(Array.isArray(response.body.recommended_entities)).toBe(true);
    });

    it('should handle course-specific filters comprehensively', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I want to learn machine learning with a certificate',
          filters: {
            entityTypeIds: ['course'],
            skill_levels: ['BEGINNER'],
            certificate_available: true,
            instructor_name: 'Andrew',
            duration_text: 'weeks',
            prerequisites: 'programming',
            max_candidates: 10,
          },
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
      expect(response.body.explanation).toContain('course');
    });

    it('should handle job-specific filters comprehensively', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I want a senior machine learning engineering job that pays well and allows remote work',
          filters: {
            entityTypeIds: ['job'],
            employment_types: ['FULL_TIME'],
            experience_levels: ['SENIOR', 'LEAD'],
            location_types: ['Remote'],
            salary_min: 120,
            salary_max: 250,
            company_name: 'tech',
            job_description: 'machine learning',
            max_candidates: 15,
          },
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
    });

    it('should handle event-specific filters comprehensively', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I want to attend AI conferences online in 2024',
          filters: {
            entityTypeIds: ['event'],
            event_types: ['Conference'],
            is_online: true,
            start_date_from: '2024-01-01',
            start_date_to: '2024-12-31',
            registration_required: true,
            location: 'online',
            max_candidates: 12,
          },
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
    });

    it('should handle hardware-specific filters comprehensively', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need a powerful GPU for training machine learning models on a budget',
          filters: {
            entityTypeIds: ['hardware'],
            hardware_types: ['GPU'],
            manufacturers: ['NVIDIA', 'AMD'],
            price_min: 500,
            price_max: 2000,
            memory_search: '16GB',
            processor_search: 'CUDA',
            max_candidates: 8,
          },
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
    });

    it('should merge extracted and explicit filters correctly', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need a free beginner-friendly Python tool for machine learning',
          filters: {
            // Explicit filters should take precedence
            technical_levels: ['INTERMEDIATE'], // Should override "beginner" from description
            has_api: true, // Additional explicit filter
            max_candidates: 10,
          },
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
      expect(response.body.candidates_analyzed).toBeGreaterThan(0);
    });

    it('should handle mixed entity type filtering', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need resources to learn AI - both tools and courses',
          filters: {
            entityTypeIds: ['ai-tool', 'course'],
            technical_levels: ['BEGINNER'],
            skill_levels: ['BEGINNER'],
            has_free_tier: true,
            certificate_available: true,
            max_candidates: 20,
          },
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
      expect(response.body.explanation).toBeDefined();
    });

    it('should return appropriate response when no entities match filters', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need something very specific that probably does not exist',
          filters: {
            entityTypeIds: ['ai-tool'],
            technical_levels: ['EXPERT'],
            has_api: true,
            has_free_tier: true,
            frameworks: ['NonExistentFramework'],
            salary_min: 999999, // Impossible salary
            max_candidates: 5,
          },
        })
        .expect(200);

      expect(response.body.recommended_entities).toEqual([]);
      expect(response.body.candidates_analyzed).toBe(0);
      expect(response.body.explanation).toContain('No relevant entities found');
    });

    it('should validate filter parameters correctly', async () => {
      await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Test query',
          filters: {
            max_candidates: 100, // Above maximum allowed
          },
        })
        .expect(400);
    });

    it('should handle complex natural language extraction', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I work in education and need an AI tool for creating course content. It must have API access, be beginner-friendly, work on Windows, support TensorFlow, and cost under $50 per month.',
          filters: {
            max_candidates: 15,
          },
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
      expect(response.body.candidates_analyzed).toBeGreaterThan(0);
      // The system should have extracted: entityTypeIds=['ai-tool'], technical_levels=['BEGINNER'], 
      // has_api=true, platforms=['Windows'], frameworks=['TensorFlow'], price constraints, etc.
    });
  });

  describe('Performance and Reliability', () => {
    it('should respond within reasonable time limits', async () => {
      const startTime = Date.now();
      
      await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need an AI tool for data analysis',
          filters: { max_candidates: 10 },
        })
        .expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(10000); // Should respond within 10 seconds
    });

    it('should handle concurrent requests properly', async () => {
      const requests = Array(5).fill(null).map(() =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: 'I need an AI tool for my project',
            filters: { max_candidates: 5 },
          })
      );

      const responses = await Promise.all(requests);
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.recommended_entities).toBeDefined();
      });
    });
  });
});
