import { Test, TestingModule } from '@nestjs/testing';
import { FilterExtractionService } from '../../src/recommendations/services/filter-extraction.service';
import { AdvancedEntityRankingService } from '../../src/common/ranking/advanced-entity-ranking.service';
import { PerformanceOptimizationService } from '../../src/common/performance/performance-optimization.service';
import { QueryOptimizationService } from '../../src/common/performance/query-optimization.service';
import { EnhancedIntentClassificationService } from '../../src/chat/services/enhanced-intent-classification.service';
import { ConversationFlowManagerService } from '../../src/chat/services/conversation-flow-manager.service';
import { IntelligentResponseGeneratorService } from '../../src/chat/services/intelligent-response-generator.service';
import { ConfigService } from '@nestjs/config';

describe('Core Services - Comprehensive Unit Tests', () => {
  let filterExtractionService: FilterExtractionService;
  let advancedRankingService: AdvancedEntityRankingService;
  let performanceOptimizationService: PerformanceOptimizationService;
  let queryOptimizationService: QueryOptimizationService;
  let enhancedIntentService: EnhancedIntentClassificationService;
  let conversationFlowManager: ConversationFlowManagerService;
  let intelligentResponseGenerator: IntelligentResponseGeneratorService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FilterExtractionService,
        AdvancedEntityRankingService,
        PerformanceOptimizationService,
        QueryOptimizationService,
        EnhancedIntentClassificationService,
        ConversationFlowManagerService,
        IntelligentResponseGeneratorService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test-value'),
          },
        },
      ],
    }).compile();

    filterExtractionService = module.get<FilterExtractionService>(FilterExtractionService);
    advancedRankingService = module.get<AdvancedEntityRankingService>(AdvancedEntityRankingService);
    performanceOptimizationService = module.get<PerformanceOptimizationService>(PerformanceOptimizationService);
    queryOptimizationService = module.get<QueryOptimizationService>(QueryOptimizationService);
    enhancedIntentService = module.get<EnhancedIntentClassificationService>(EnhancedIntentClassificationService);
    conversationFlowManager = module.get<ConversationFlowManagerService>(ConversationFlowManagerService);
    intelligentResponseGenerator = module.get<IntelligentResponseGeneratorService>(IntelligentResponseGeneratorService);
  });

  describe('FilterExtractionService', () => {
    it('should extract entity types correctly', async () => {
      const testCases = [
        {
          description: 'I need an AI tool for machine learning',
          expectedEntityTypes: ['ai-tool']
        },
        {
          description: 'Looking for machine learning courses',
          expectedEntityTypes: ['course']
        },
        {
          description: 'ML engineering job opportunities',
          expectedEntityTypes: ['job']
        },
        {
          description: 'AI conferences and events',
          expectedEntityTypes: ['event']
        },
        {
          description: 'GPU hardware for deep learning',
          expectedEntityTypes: ['hardware']
        }
      ];

      for (const testCase of testCases) {
        const result = await filterExtractionService.extractFiltersFromDescription(testCase.description);
        expect(result.entityTypeIds).toEqual(expect.arrayContaining(testCase.expectedEntityTypes));
      }
    });

    it('should extract technical levels correctly', async () => {
      const testCases = [
        {
          description: 'I am a beginner looking for simple tools',
          expectedLevels: ['BEGINNER']
        },
        {
          description: 'Need intermediate level resources',
          expectedLevels: ['INTERMEDIATE']
        },
        {
          description: 'Advanced machine learning tools for experts',
          expectedLevels: ['ADVANCED']
        }
      ];

      for (const testCase of testCases) {
        const result = await filterExtractionService.extractFiltersFromDescription(testCase.description);
        expect(result.technical_levels).toEqual(expect.arrayContaining(testCase.expectedLevels));
      }
    });

    it('should extract budget constraints correctly', async () => {
      const testCases = [
        {
          description: 'Free AI tools for students',
          expectedFreeTier: true,
          expectedPriceRanges: ['FREE']
        },
        {
          description: 'Budget-friendly tools under $50',
          expectedPriceRanges: ['FREE', 'LOW']
        },
        {
          description: 'Premium enterprise solutions',
          expectedPriceRanges: undefined // No specific constraint
        }
      ];

      for (const testCase of testCases) {
        const result = await filterExtractionService.extractFiltersFromDescription(testCase.description);
        
        if (testCase.expectedFreeTier !== undefined) {
          expect(result.has_free_tier).toBe(testCase.expectedFreeTier);
        }
        
        if (testCase.expectedPriceRanges) {
          expect(result.price_ranges).toEqual(expect.arrayContaining(testCase.expectedPriceRanges));
        }
      }
    });

    it('should extract platform preferences correctly', async () => {
      const testCases = [
        {
          description: 'Tools that work on Windows and Mac',
          expectedPlatforms: ['Windows', 'macOS']
        },
        {
          description: 'Web-based solutions for browser use',
          expectedPlatforms: ['Web']
        },
        {
          description: 'Linux-compatible machine learning tools',
          expectedPlatforms: ['Linux']
        }
      ];

      for (const testCase of testCases) {
        const result = await filterExtractionService.extractFiltersFromDescription(testCase.description);
        expect(result.platforms).toEqual(expect.arrayContaining(testCase.expectedPlatforms));
      }
    });

    it('should handle complex multi-criteria descriptions', async () => {
      const complexDescription = 'I need a beginner-friendly AI tool with API access for Python machine learning projects under $50 that works on Windows';
      
      const result = await filterExtractionService.extractFiltersFromDescription(complexDescription);
      
      expect(result.entityTypeIds).toContain('ai-tool');
      expect(result.technical_levels).toContain('BEGINNER');
      expect(result.has_api).toBe(true);
      expect(result.frameworks).toContain('Python');
      expect(result.use_cases_search).toContain('machine learning');
      expect(result.platforms).toContain('Windows');
      expect(result.price_ranges).toEqual(expect.arrayContaining(['FREE', 'LOW']));
    });
  });

  describe('AdvancedEntityRankingService', () => {
    const mockEntities = [
      {
        id: '1',
        name: 'Tool A',
        avgRating: 4.5,
        reviewCount: 100,
        similarity: 0.8,
        hasFreeTier: true,
        hasApi: true,
        technicalLevel: 'BEGINNER',
        entityType: { slug: 'ai-tool' }
      },
      {
        id: '2',
        name: 'Tool B',
        avgRating: 4.2,
        reviewCount: 200,
        similarity: 0.7,
        hasFreeTier: false,
        hasApi: true,
        technicalLevel: 'ADVANCED',
        entityType: { slug: 'ai-tool' }
      },
      {
        id: '3',
        name: 'Tool C',
        avgRating: 4.8,
        reviewCount: 50,
        similarity: 0.9,
        hasFreeTier: true,
        hasApi: false,
        technicalLevel: 'INTERMEDIATE',
        entityType: { slug: 'ai-tool' }
      }
    ];

    it('should rank entities based on multiple factors', () => {
      const context = {
        appliedFilters: {
          entityTypeIds: ['ai-tool'],
          has_api: true,
          technical_levels: ['BEGINNER']
        },
        filterConfidence: {
          entityTypeIds: 0.9,
          has_api: 0.8,
          technical_levels: 0.7
        }
      };

      const rankedEntities = advancedRankingService.rankEntities(mockEntities, context);
      
      expect(rankedEntities).toHaveLength(3);
      expect(rankedEntities[0].rankingScore).toBeGreaterThan(0);
      expect(rankedEntities[0].rankingBreakdown).toBeDefined();
      expect(rankedEntities[0].rankingReason).toBeDefined();
      
      // Should be sorted by ranking score (highest first)
      for (let i = 0; i < rankedEntities.length - 1; i++) {
        expect(rankedEntities[i].rankingScore).toBeGreaterThanOrEqual(rankedEntities[i + 1].rankingScore);
      }
    });

    it('should consider filter matches in ranking', () => {
      const context = {
        appliedFilters: {
          has_api: true,
          has_free_tier: true
        },
        filterConfidence: {
          has_api: 0.9,
          has_free_tier: 0.8
        }
      };

      const rankedEntities = advancedRankingService.rankEntities(mockEntities, context);
      
      // Entities matching more filters should rank higher
      const topEntity = rankedEntities[0];
      expect(topEntity.rankingBreakdown.filterMatch).toBeGreaterThan(0.5);
    });

    it('should apply quality scoring correctly', () => {
      const context = { appliedFilters: {}, filterConfidence: {} };
      const rankedEntities = advancedRankingService.rankEntities(mockEntities, context);
      
      rankedEntities.forEach(entity => {
        expect(entity.rankingBreakdown.entityQuality).toBeGreaterThan(0);
        expect(entity.rankingBreakdown.entityQuality).toBeLessThanOrEqual(1);
      });
    });
  });

  describe('PerformanceOptimizationService', () => {
    it('should cache and retrieve filter extraction results', async () => {
      const testDescription = 'Test description for caching';
      let callCount = 0;
      
      const mockExtractionFn = jest.fn().mockImplementation(async () => {
        callCount++;
        return { entityTypeIds: ['ai-tool'] };
      });

      // First call (cache miss)
      const result1 = await performanceOptimizationService.optimizedFilterExtraction(
        testDescription,
        mockExtractionFn
      );

      // Second call (cache hit)
      const result2 = await performanceOptimizationService.optimizedFilterExtraction(
        testDescription,
        mockExtractionFn
      );

      expect(result1).toEqual(result2);
      expect(mockExtractionFn).toHaveBeenCalledTimes(1); // Should only be called once due to caching
    });

    it('should provide performance metrics', () => {
      const metrics = performanceOptimizationService.getPerformanceMetrics();
      
      expect(metrics).toHaveProperty('cacheHits');
      expect(metrics).toHaveProperty('cacheMisses');
      expect(metrics).toHaveProperty('avgResponseTime');
      expect(metrics).toHaveProperty('cacheHitRate');
      expect(metrics).toHaveProperty('systemHealth');
    });

    it('should handle batch processing efficiently', async () => {
      const items = [1, 2, 3, 4, 5];
      const processFn = jest.fn().mockImplementation(async (item) => item * 2);

      const results = await performanceOptimizationService.optimizedBatchProcessing(
        items,
        processFn,
        2 // Batch size of 2
      );

      expect(results).toEqual([2, 4, 6, 8, 10]);
      expect(processFn).toHaveBeenCalledTimes(5);
    });

    it('should manage concurrent requests', async () => {
      const requestFn = jest.fn().mockResolvedValue('success');
      
      const promises = Array(5).fill(null).map(() =>
        performanceOptimizationService.manageConcurrentRequests(requestFn, 3)
      );

      const results = await Promise.all(promises);
      
      expect(results).toEqual(['success', 'success', 'success', 'success', 'success']);
      expect(requestFn).toHaveBeenCalledTimes(5);
    });
  });

  describe('QueryOptimizationService', () => {
    it('should optimize filter order based on selectivity', () => {
      const filters = {
        entityTypeIds: ['ai-tool'],
        searchTerm: 'machine learning',
        technical_levels: ['BEGINNER'],
        has_free_tier: true,
        platforms: ['Web', 'Linux']
      };

      const optimizedFilters = queryOptimizationService.optimizeFilterOrder(filters);
      
      expect(optimizedFilters).toHaveProperty('_optimizationHints');
      expect(optimizedFilters._optimizationHints.selectivity).toBeDefined();
      expect(optimizedFilters._optimizationHints.recommendedOrder).toBeDefined();
    });

    it('should generate appropriate query hints', () => {
      const filters = {
        entityTypeIds: ['ai-tool'],
        searchTerm: 'test query',
        has_api: true,
        technical_levels: ['ADVANCED']
      };

      const hints = queryOptimizationService.generateQueryHints(filters);
      
      expect(hints).toHaveProperty('useIndex');
      expect(hints).toHaveProperty('joinOrder');
      expect(hints).toHaveProperty('filterStrategy');
      expect(hints).toHaveProperty('estimatedComplexity');
      
      expect(Array.isArray(hints.useIndex)).toBe(true);
      expect(Array.isArray(hints.joinOrder)).toBe(true);
    });

    it('should record and analyze query performance', () => {
      const querySignature = 'test-query-signature';
      const executionTime = 500;

      queryOptimizationService.recordQueryPerformance(querySignature, executionTime);
      
      const insights = queryOptimizationService.getQueryPerformanceInsights();
      
      expect(insights).toHaveProperty('totalQueries');
      expect(insights).toHaveProperty('avgResponseTime');
      expect(insights).toHaveProperty('slowestQueries');
      expect(insights).toHaveProperty('mostFrequentQueries');
      expect(insights).toHaveProperty('recommendations');
      
      expect(insights.totalQueries).toBeGreaterThan(0);
    });
  });

  describe('EnhancedIntentClassificationService', () => {
    const mockContext = {
      sessionId: 'test-session',
      userId: 'test-user',
      messages: [],
      discoveredEntities: [],
      userPreferences: {},
      conversationStage: 'discovery' as const,
      metadata: {
        startedAt: new Date(),
        lastActiveAt: new Date(),
        totalMessages: 0,
        entitiesShown: []
      }
    };

    it('should classify intent and extract filters', async () => {
      const userMessage = 'I need a beginner-friendly AI tool with API access';
      
      const result = await enhancedIntentService.classifyIntentWithFilters(userMessage, mockContext);
      
      expect(result).toHaveProperty('type');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('extractedFilters');
      expect(result).toHaveProperty('filterConfidence');
      expect(result).toHaveProperty('missingCriteria');
      expect(result).toHaveProperty('clarifyingQuestions');
      
      expect(result.extractedFilters.entityTypeIds).toContain('ai-tool');
      expect(result.extractedFilters.technical_levels).toContain('BEGINNER');
      expect(result.extractedFilters.has_api).toBe(true);
    });

    it('should update conversation filters correctly', () => {
      const enhancedIntent = {
        type: 'discovery' as const,
        confidence: 0.8,
        entities: [],
        categories: [],
        features: [],
        constraints: {},
        extractedFilters: {
          entityTypeIds: ['ai-tool'],
          technical_levels: ['BEGINNER']
        },
        filterConfidence: {
          entityTypeIds: 0.9,
          technical_levels: 0.8
        },
        missingCriteria: {},
        clarifyingQuestions: []
      };

      const updatedContext = enhancedIntentService.updateConversationFilters(
        mockContext,
        enhancedIntent,
        1
      );

      expect(updatedContext.accumulatedFilters).toBeDefined();
      expect(updatedContext.accumulatedFilters.filters.entityTypeIds).toContain('ai-tool');
      expect(updatedContext.accumulatedFilters.confidence.entityTypeIds).toBe(0.9);
      expect(updatedContext.enhancedMetadata.filtersExtracted).toBeGreaterThan(0);
    });
  });

  describe('ConversationFlowManagerService', () => {
    const mockEnhancedContext = {
      sessionId: 'test-session',
      userId: 'test-user',
      messages: [],
      discoveredEntities: [],
      userPreferences: {},
      conversationStage: 'discovery' as const,
      metadata: {
        startedAt: new Date(),
        lastActiveAt: new Date(),
        totalMessages: 0,
        entitiesShown: []
      },
      accumulatedFilters: {
        filters: { entityTypeIds: ['ai-tool'] },
        confidence: { entityTypeIds: 0.8 },
        history: { entityTypeIds: 1 },
        source: { entityTypeIds: 'extracted' as const }
      },
      enhancedMetadata: {
        filtersExtracted: 1,
        clarificationQuestions: 0,
        conversationQuality: 0.7,
        readyForRecommendations: false
      }
    };

    const mockIntent = {
      type: 'discovery' as const,
      confidence: 0.8,
      entities: [],
      categories: [],
      features: [],
      constraints: {},
      extractedFilters: { technical_levels: ['BEGINNER'] },
      filterConfidence: { technical_levels: 0.7 },
      missingCriteria: { budget: true },
      clarifyingQuestions: []
    };

    it('should determine appropriate next action', () => {
      const action = conversationFlowManager.determineNextAction(mockEnhancedContext, mockIntent);
      
      expect(action).toHaveProperty('type');
      expect(action).toHaveProperty('priority');
      expect(action).toHaveProperty('reason');
      
      expect(['provide_recommendations', 'ask_entity_type', 'gather_more_info', 'clarify_uncertain_filters', 'continue_discovery'])
        .toContain(action.type);
    });

    it('should generate strategic questions', () => {
      const questions = conversationFlowManager.generateStrategicQuestions(mockEnhancedContext, mockIntent);
      
      expect(Array.isArray(questions)).toBe(true);
      
      questions.forEach(question => {
        expect(question).toHaveProperty('question');
        expect(question).toHaveProperty('purpose');
        expect(question).toHaveProperty('expectedFilterKeys');
        expect(question).toHaveProperty('priority');
      });
    });

    it('should handle filter corrections', () => {
      const correctionResult = conversationFlowManager.handleFilterCorrection(
        mockEnhancedContext,
        mockIntent,
        'Actually, I am a beginner, not advanced'
      );
      
      expect(correctionResult).toHaveProperty('isCorrection');
      expect(correctionResult).toHaveProperty('corrections');
      expect(correctionResult).toHaveProperty('conflicts');
      expect(correctionResult).toHaveProperty('recommendedAction');
    });

    it('should optimize conversation flow', () => {
      const optimization = conversationFlowManager.optimizeConversationFlow(mockEnhancedContext, mockIntent);
      
      expect(optimization).toHaveProperty('efficiency');
      expect(optimization).toHaveProperty('messageCount');
      expect(optimization).toHaveProperty('filtersCount');
      expect(optimization).toHaveProperty('avgConfidence');
      expect(optimization).toHaveProperty('optimizations');
      expect(optimization).toHaveProperty('readyForRecommendations');
      
      expect(typeof optimization.efficiency).toBe('number');
      expect(Array.isArray(optimization.optimizations)).toBe(true);
    });
  });

  describe('IntelligentResponseGeneratorService', () => {
    const mockContext = {
      sessionId: 'test-session',
      userId: 'test-user',
      messages: [],
      discoveredEntities: [],
      userPreferences: {},
      conversationStage: 'discovery' as const,
      metadata: {
        startedAt: new Date(),
        lastActiveAt: new Date(),
        totalMessages: 0,
        entitiesShown: []
      },
      accumulatedFilters: {
        filters: {},
        confidence: {},
        history: {},
        source: {}
      },
      enhancedMetadata: {
        filtersExtracted: 0,
        clarificationQuestions: 0,
        conversationQuality: 0.5,
        readyForRecommendations: false
      }
    };

    const mockIntent = {
      type: 'discovery' as const,
      confidence: 0.8,
      entities: [],
      categories: [],
      features: [],
      constraints: {},
      extractedFilters: {},
      filterConfidence: {},
      missingCriteria: {},
      clarifyingQuestions: []
    };

    const mockAction = {
      type: 'gather_more_info' as const,
      priority: 8,
      reason: 'Need more criteria for precise recommendations'
    };

    it('should generate intelligent responses', () => {
      const response = intelligentResponseGenerator.generateIntelligentResponse(
        mockContext,
        mockIntent,
        mockAction,
        [], // entities
        [], // questions
        undefined, // correction result
        undefined  // optimization
      );
      
      expect(response).toHaveProperty('message');
      expect(response).toHaveProperty('conversationStage');
      expect(response).toHaveProperty('metadata');
      expect(response).toHaveProperty('discoveredEntities');
      expect(response).toHaveProperty('strategicQuestions');
      expect(response).toHaveProperty('suggestedActions');
      expect(response).toHaveProperty('readyForRecommendations');
      
      expect(typeof response.message).toBe('string');
      expect(response.message.length).toBeGreaterThan(0);
    });

    it('should format discovered entities correctly', () => {
      const mockEntities = [
        {
          id: '1',
          name: 'Test Tool',
          description: 'A test AI tool',
          entityType: { name: 'AI Tool' },
          avgRating: 4.5,
          reviewCount: 100
        }
      ];

      const response = intelligentResponseGenerator.generateIntelligentResponse(
        mockContext,
        mockIntent,
        mockAction,
        mockEntities,
        [],
        undefined,
        undefined
      );
      
      expect(response.discoveredEntities).toHaveLength(1);
      expect(response.discoveredEntities[0]).toHaveProperty('id');
      expect(response.discoveredEntities[0]).toHaveProperty('name');
      expect(response.discoveredEntities[0]).toHaveProperty('relevanceScore');
      expect(response.discoveredEntities[0]).toHaveProperty('matchedFilters');
    });
  });
});
