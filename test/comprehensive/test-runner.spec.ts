import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';

/**
 * Comprehensive Test Runner
 * 
 * This test suite orchestrates all comprehensive tests and provides
 * detailed reporting on system performance, reliability, and functionality.
 */
describe('Comprehensive Test Suite - Full System Validation', () => {
  let app: INestApplication;
  let testResults: TestResults;

  beforeAll(async () => {
    console.log('🚀 Starting Comprehensive Test Suite...');
    console.log('📊 This will validate all enhanced features and performance optimizations');
    
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    testResults = {
      startTime: new Date(),
      endTime: null,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      performanceMetrics: {
        avgRecommendationTime: 0,
        avgChatTime: 0,
        cacheHitRate: 0,
        concurrentRequestsHandled: 0,
        memoryUsageIncrease: 0,
      },
      featureValidation: {
        enhancedFiltering: false,
        advancedRanking: false,
        conversationalAI: false,
        performanceOptimization: false,
        errorHandling: false,
      },
      detailedResults: []
    };
  });

  afterAll(async () => {
    testResults.endTime = new Date();
    await app.close();
    
    console.log('\n📋 COMPREHENSIVE TEST SUITE RESULTS');
    console.log('=====================================');
    printTestSummary(testResults);
  });

  describe('🎯 Enhanced Filtering System Validation', () => {
    it('should validate all 80+ filter parameters work correctly', async () => {
      const testStart = Date.now();
      
      // Test comprehensive filter combinations for each entity type
      const entityTypeTests = [
        {
          type: 'ai-tool',
          filters: {
            entityTypeIds: ['ai-tool'],
            technical_levels: ['BEGINNER', 'INTERMEDIATE'],
            learning_curves: ['EASY', 'MODERATE'],
            has_api: true,
            has_free_tier: true,
            open_source: true,
            platforms: ['Web', 'Linux'],
            frameworks: ['TensorFlow', 'PyTorch'],
            use_cases_search: 'machine learning',
            key_features_search: 'real-time',
            integrations: ['REST API', 'Python SDK'],
            target_audience: ['Developers', 'Researchers'],
            max_candidates: 15,
          }
        },
        {
          type: 'course',
          filters: {
            entityTypeIds: ['course'],
            skill_levels: ['BEGINNER', 'INTERMEDIATE'],
            certificate_available: true,
            duration_text: 'weeks',
            course_format: 'Online',
            language: 'English',
            max_candidates: 12,
          }
        },
        {
          type: 'job',
          filters: {
            entityTypeIds: ['job'],
            employment_types: ['FULL_TIME', 'CONTRACT'],
            experience_levels: ['JUNIOR', 'SENIOR'],
            location_types: ['Remote', 'Hybrid'],
            salary_min: 80,
            salary_max: 200,
            max_candidates: 10,
          }
        }
      ];

      let allTestsPassed = true;
      const results = [];

      for (const test of entityTypeTests) {
        try {
          const response = await request(app.getHttpServer())
            .post('/recommendations')
            .send({
              problem_description: `Comprehensive ${test.type} search with all filters`,
              filters: test.filters
            })
            .expect(200);

          const testPassed = response.body.recommended_entities !== undefined &&
                            response.body.candidates_analyzed >= 0;
          
          results.push({
            entityType: test.type,
            passed: testPassed,
            entitiesFound: response.body.recommended_entities?.length || 0,
            candidatesAnalyzed: response.body.candidates_analyzed
          });

          if (!testPassed) allTestsPassed = false;
        } catch (error) {
          allTestsPassed = false;
          results.push({
            entityType: test.type,
            passed: false,
            error: error.message
          });
        }
      }

      const testTime = Date.now() - testStart;
      testResults.performanceMetrics.avgRecommendationTime = testTime / entityTypeTests.length;
      testResults.featureValidation.enhancedFiltering = allTestsPassed;

      expect(allTestsPassed).toBe(true);
      console.log(`✅ Enhanced Filtering: ${results.length} entity types tested in ${testTime}ms`);
    });
  });

  describe('🏆 Advanced Ranking System Validation', () => {
    it('should validate multi-factor ranking produces high-quality results', async () => {
      const testStart = Date.now();
      
      const rankingTests = [
        {
          description: 'High-quality AI tools with excellent ratings',
          expectedTopEntityQuality: 4.0 // Expect top results to have good ratings
        },
        {
          description: 'Beginner-friendly tools with good documentation',
          expectedRelevance: 0.8 // Expect high relevance scores
        },
        {
          description: 'Enterprise-grade solutions with API access',
          expectedFeatureMatch: true // Should match API requirement
        }
      ];

      let rankingQualityScore = 0;
      const results = [];

      for (const test of rankingTests) {
        try {
          const response = await request(app.getHttpServer())
            .post('/recommendations')
            .send({
              problem_description: test.description,
              filters: {
                entityTypeIds: ['ai-tool'],
                max_candidates: 10
              }
            })
            .expect(200);

          const entities = response.body.recommended_entities;
          let testScore = 0;

          if (entities && entities.length > 0) {
            // Check if ranking produces quality results
            const topEntity = entities[0];
            
            if (test.expectedTopEntityQuality && topEntity.avgRating >= test.expectedTopEntityQuality) {
              testScore += 0.4;
            }
            
            if (test.expectedRelevance && entities.every(e => e.relevanceScore >= test.expectedRelevance)) {
              testScore += 0.3;
            }
            
            if (test.expectedFeatureMatch && topEntity.hasApi) {
              testScore += 0.3;
            }
            
            // Check ranking order (scores should be descending)
            let properOrder = true;
            for (let i = 0; i < entities.length - 1; i++) {
              if (entities[i].rankingScore < entities[i + 1].rankingScore) {
                properOrder = false;
                break;
              }
            }
            if (properOrder) testScore += 0.3;
          }

          rankingQualityScore += testScore;
          results.push({
            test: test.description,
            score: testScore,
            entitiesReturned: entities?.length || 0
          });
        } catch (error) {
          results.push({
            test: test.description,
            score: 0,
            error: error.message
          });
        }
      }

      const avgQualityScore = rankingQualityScore / rankingTests.length;
      const testTime = Date.now() - testStart;
      testResults.featureValidation.advancedRanking = avgQualityScore >= 0.7;

      expect(avgQualityScore).toBeGreaterThan(0.6); // 60% quality threshold
      console.log(`🏆 Advanced Ranking: ${avgQualityScore.toFixed(2)} quality score in ${testTime}ms`);
    });
  });

  describe('🤖 Conversational AI System Validation', () => {
    it('should validate intelligent conversation flow and filter refinement', async () => {
      const testStart = Date.now();
      const sessionId = `comprehensive-test-${Date.now()}`;
      
      const conversationFlow = [
        {
          message: 'I need help with AI for my startup',
          expectedStage: 'discovery',
          shouldAskQuestions: true
        },
        {
          message: 'We are building content creation tools',
          expectedStage: 'refinement',
          shouldExtractFilters: true
        },
        {
          message: 'I am a beginner programmer with limited budget',
          expectedStage: 'refinement',
          shouldExtractFilters: true
        },
        {
          message: 'Must work on Windows and have API access',
          expectedStage: 'ready',
          shouldProvideRecommendations: true
        }
      ];

      let conversationQuality = 0;
      const results = [];

      for (const [index, step] of conversationFlow.entries()) {
        try {
          const response = await request(app.getHttpServer())
            .post('/chat')
            .send({
              message: step.message,
              session_id: sessionId
            })
            .expect(200);

          let stepScore = 0;

          // Validate session consistency
          if (response.body.session_id === sessionId) stepScore += 0.2;

          // Validate conversation stage progression
          if (response.body.conversation_stage) stepScore += 0.2;

          // Validate question asking when appropriate
          if (step.shouldAskQuestions && (
            response.body.follow_up_questions?.length > 0 ||
            response.body.message.includes('?')
          )) {
            stepScore += 0.2;
          }

          // Validate filter extraction
          if (step.shouldExtractFilters && index >= 1) {
            stepScore += 0.2;
          }

          // Validate recommendations when ready
          if (step.shouldProvideRecommendations && response.body.discovered_entities?.length > 0) {
            stepScore += 0.2;
          }

          conversationQuality += stepScore;
          results.push({
            step: index + 1,
            message: step.message.substring(0, 50) + '...',
            score: stepScore,
            stage: response.body.conversation_stage
          });
        } catch (error) {
          results.push({
            step: index + 1,
            score: 0,
            error: error.message
          });
        }
      }

      const avgConversationQuality = conversationQuality / conversationFlow.length;
      const testTime = Date.now() - testStart;
      testResults.performanceMetrics.avgChatTime = testTime / conversationFlow.length;
      testResults.featureValidation.conversationalAI = avgConversationQuality >= 0.7;

      expect(avgConversationQuality).toBeGreaterThan(0.6);
      console.log(`🤖 Conversational AI: ${avgConversationQuality.toFixed(2)} quality score in ${testTime}ms`);
    });
  });

  describe('⚡ Performance Optimization Validation', () => {
    it('should validate caching, query optimization, and response times', async () => {
      const testStart = Date.now();
      
      // Test caching effectiveness
      const cacheTestQuery = {
        problem_description: 'AI tools for performance testing',
        filters: {
          entityTypeIds: ['ai-tool'],
          use_cases_search: 'data analysis',
          max_candidates: 15
        }
      };

      // First request (cache miss)
      const uncachedStart = Date.now();
      await request(app.getHttpServer())
        .post('/recommendations')
        .send(cacheTestQuery)
        .expect(200);
      const uncachedTime = Date.now() - uncachedStart;

      // Second request (cache hit)
      const cachedStart = Date.now();
      await request(app.getHttpServer())
        .post('/recommendations')
        .send(cacheTestQuery)
        .expect(200);
      const cachedTime = Date.now() - cachedStart;

      const cacheImprovement = (uncachedTime - cachedTime) / uncachedTime;
      testResults.performanceMetrics.cacheHitRate = cacheImprovement * 100;

      // Test concurrent request handling
      const concurrentRequests = 10;
      const concurrentStart = Date.now();
      const requests = Array(concurrentRequests).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `Concurrent test ${index}`,
            filters: { max_candidates: 8 }
          })
      );

      const responses = await Promise.allSettled(requests);
      const concurrentTime = Date.now() - concurrentStart;
      const successfulRequests = responses.filter(r => 
        r.status === 'fulfilled' && r.value.status === 200
      ).length;

      testResults.performanceMetrics.concurrentRequestsHandled = successfulRequests;

      const performanceScore = (
        (cacheImprovement > 0.3 ? 0.3 : 0) + // 30% cache improvement
        (successfulRequests >= 8 ? 0.3 : 0) + // 80% concurrent success
        (concurrentTime < 5000 ? 0.4 : 0) // Under 5 seconds
      );

      const testTime = Date.now() - testStart;
      testResults.featureValidation.performanceOptimization = performanceScore >= 0.7;

      expect(performanceScore).toBeGreaterThan(0.6);
      console.log(`⚡ Performance: ${performanceScore.toFixed(2)} score, ${cacheImprovement.toFixed(1)}% cache improvement`);
    });
  });

  describe('🛡️ Error Handling and Resilience Validation', () => {
    it('should validate comprehensive error handling and graceful degradation', async () => {
      const testStart = Date.now();
      
      const errorTests = [
        {
          name: 'Empty input handling',
          request: { problem_description: '', filters: { max_candidates: 10 } },
          expectedStatus: 400
        },
        {
          name: 'Invalid filter values',
          request: { 
            problem_description: 'Test', 
            filters: { max_candidates: -1, technical_levels: ['INVALID'] }
          },
          expectedStatus: 400
        },
        {
          name: 'Malicious input sanitization',
          request: { 
            problem_description: '<script>alert("xss")</script>AI tools', 
            filters: { max_candidates: 10 }
          },
          expectedStatus: [200, 400] // Either sanitized or rejected
        },
        {
          name: 'Extreme load handling',
          concurrent: true,
          count: 25
        }
      ];

      let errorHandlingScore = 0;
      const results = [];

      for (const test of errorTests) {
        try {
          if (test.concurrent) {
            // Test concurrent load
            const requests = Array(test.count).fill(null).map(() =>
              request(app.getHttpServer())
                .post('/recommendations')
                .send({
                  problem_description: 'Load test',
                  filters: { max_candidates: 5 }
                })
            );

            const responses = await Promise.allSettled(requests);
            const successRate = responses.filter(r => 
              r.status === 'fulfilled' && r.value.status === 200
            ).length / test.count;

            if (successRate >= 0.6) errorHandlingScore += 0.3; // 60% success under load
            results.push({
              test: test.name,
              successRate: successRate,
              passed: successRate >= 0.6
            });
          } else {
            // Test individual error cases
            const response = await request(app.getHttpServer())
              .post('/recommendations')
              .send(test.request);

            const expectedStatuses = Array.isArray(test.expectedStatus) 
              ? test.expectedStatus 
              : [test.expectedStatus];
            
            const passed = expectedStatuses.includes(response.status);
            if (passed) errorHandlingScore += 0.25;

            results.push({
              test: test.name,
              expectedStatus: test.expectedStatus,
              actualStatus: response.status,
              passed
            });
          }
        } catch (error) {
          results.push({
            test: test.name,
            passed: false,
            error: error.message
          });
        }
      }

      const testTime = Date.now() - testStart;
      testResults.featureValidation.errorHandling = errorHandlingScore >= 0.8;

      expect(errorHandlingScore).toBeGreaterThan(0.7);
      console.log(`🛡️ Error Handling: ${errorHandlingScore.toFixed(2)} resilience score in ${testTime}ms`);
    });
  });

  describe('📊 System Health and Monitoring', () => {
    it('should validate system health metrics and monitoring capabilities', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform intensive operations to test system stability
      const intensiveRequests = Array(15).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `System health test ${index} with complex requirements`,
            filters: {
              entityTypeIds: ['ai-tool', 'course'],
              technical_levels: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'],
              has_api: true,
              platforms: ['Web', 'Linux', 'Windows'],
              max_candidates: 20
            }
          })
      );

      await Promise.all(intensiveRequests);

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024; // MB
      testResults.performanceMetrics.memoryUsageIncrease = memoryIncrease;

      // System should maintain reasonable memory usage
      expect(memoryIncrease).toBeLessThan(200); // Less than 200MB increase

      console.log(`📊 System Health: ${memoryIncrease.toFixed(1)}MB memory increase`);
    });
  });
});

// Helper interfaces and functions
interface TestResults {
  startTime: Date;
  endTime: Date | null;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  performanceMetrics: {
    avgRecommendationTime: number;
    avgChatTime: number;
    cacheHitRate: number;
    concurrentRequestsHandled: number;
    memoryUsageIncrease: number;
  };
  featureValidation: {
    enhancedFiltering: boolean;
    advancedRanking: boolean;
    conversationalAI: boolean;
    performanceOptimization: boolean;
    errorHandling: boolean;
  };
  detailedResults: any[];
}

function printTestSummary(results: TestResults): void {
  const duration = results.endTime 
    ? (results.endTime.getTime() - results.startTime.getTime()) / 1000 
    : 0;

  console.log(`⏱️  Total Duration: ${duration.toFixed(1)} seconds`);
  console.log(`📈 Performance Metrics:`);
  console.log(`   • Avg Recommendation Time: ${results.performanceMetrics.avgRecommendationTime.toFixed(0)}ms`);
  console.log(`   • Avg Chat Response Time: ${results.performanceMetrics.avgChatTime.toFixed(0)}ms`);
  console.log(`   • Cache Hit Improvement: ${results.performanceMetrics.cacheHitRate.toFixed(1)}%`);
  console.log(`   • Concurrent Requests Handled: ${results.performanceMetrics.concurrentRequestsHandled}`);
  console.log(`   • Memory Usage Increase: ${results.performanceMetrics.memoryUsageIncrease.toFixed(1)}MB`);
  
  console.log(`\n🎯 Feature Validation:`);
  Object.entries(results.featureValidation).forEach(([feature, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`   • ${feature.replace(/([A-Z])/g, ' $1').toLowerCase()}: ${status}`);
  });

  const allFeaturesPassed = Object.values(results.featureValidation).every(Boolean);
  const overallStatus = allFeaturesPassed ? '🎉 ALL SYSTEMS OPERATIONAL' : '⚠️  SOME ISSUES DETECTED';
  
  console.log(`\n${overallStatus}`);
  console.log('=====================================\n');
}
