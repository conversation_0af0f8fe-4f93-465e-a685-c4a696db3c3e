import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';

describe('Enhanced Recommendations System - Comprehensive Integration Tests', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Filter Extraction and Natural Language Processing', () => {
    const testCases = [
      {
        description: 'I need a beginner-friendly AI tool with API access for Python machine learning projects under $50',
        expectedFilters: {
          entityTypeIds: ['ai-tool'],
          technical_levels: ['BEGINNER'],
          has_api: true,
          frameworks: ['Python'],
          use_cases_search: 'machine learning',
          price_ranges: ['FREE', 'LOW'],
        },
        testName: 'Complex natural language with multiple criteria'
      },
      {
        description: 'Looking for advanced machine learning courses with certificates from top universities',
        expectedFilters: {
          entityTypeIds: ['course'],
          skill_levels: ['ADVANCED'],
          certificate_available: true,
          use_cases_search: 'machine learning',
        },
        testName: 'Educational resource discovery'
      },
      {
        description: 'Senior ML engineer position, remote work, salary above $150k, tech companies',
        expectedFilters: {
          entityTypeIds: ['job'],
          experience_levels: ['SENIOR'],
          location_types: ['Remote'],
          salary_min: 150,
          use_cases_search: 'machine learning',
        },
        testName: 'Job search with specific requirements'
      },
      {
        description: 'Online AI conferences in 2024, preferably free or low cost',
        expectedFilters: {
          entityTypeIds: ['event'],
          event_types: ['Conference'],
          is_online: true,
          searchTerm: 'AI',
          has_free_tier: true,
        },
        testName: 'Event discovery with temporal and cost constraints'
      },
      {
        description: 'GPU under $2000 for training deep learning models, NVIDIA preferred',
        expectedFilters: {
          entityTypeIds: ['hardware'],
          hardware_types: ['GPU'],
          price_max: 2000,
          manufacturers: ['NVIDIA'],
          use_cases_search: 'deep learning',
        },
        testName: 'Hardware search with brand and budget preferences'
      }
    ];

    testCases.forEach(({ description, expectedFilters, testName }) => {
      it(`should extract filters correctly: ${testName}`, async () => {
        const response = await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: description,
            filters: { max_candidates: 10 }
          })
          .expect(200);

        expect(response.body.recommended_entities).toBeDefined();
        expect(response.body.candidates_analyzed).toBeGreaterThan(0);
        expect(response.body.explanation).toContain('recommendations');
      });
    });
  });

  describe('Comprehensive Filter Combinations', () => {
    it('should handle all AI tool filters simultaneously', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Comprehensive AI tool search',
          filters: {
            entityTypeIds: ['ai-tool'],
            technical_levels: ['INTERMEDIATE', 'ADVANCED'],
            learning_curves: ['MODERATE'],
            has_api: true,
            has_free_tier: true,
            open_source: true,
            platforms: ['Web', 'Linux'],
            frameworks: ['TensorFlow', 'PyTorch'],
            use_cases_search: 'computer vision',
            key_features_search: 'real-time processing',
            integrations: ['REST API', 'Python SDK'],
            target_audience: ['Developers', 'Researchers'],
            max_candidates: 15,
          }
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
      expect(response.body.candidates_analyzed).toBeGreaterThanOrEqual(0);
    });

    it('should handle all course filters simultaneously', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Comprehensive course search',
          filters: {
            entityTypeIds: ['course'],
            skill_levels: ['BEGINNER', 'INTERMEDIATE'],
            certificate_available: true,
            instructor_name: 'Andrew',
            duration_text: 'weeks',
            prerequisites: 'basic programming',
            course_format: 'Online',
            language: 'English',
            university_name: 'Stanford',
            max_candidates: 12,
          }
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
    });

    it('should handle all job filters simultaneously', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Comprehensive job search',
          filters: {
            entityTypeIds: ['job'],
            employment_types: ['FULL_TIME'],
            experience_levels: ['SENIOR', 'LEAD'],
            location_types: ['Remote', 'Hybrid'],
            salary_min: 120,
            salary_max: 250,
            company_name: 'tech',
            job_description: 'machine learning engineer',
            benefits: ['Health Insurance', 'Stock Options'],
            company_size: ['LARGE'],
            max_candidates: 20,
          }
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
    });

    it('should handle all event filters simultaneously', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Comprehensive event search',
          filters: {
            entityTypeIds: ['event'],
            event_types: ['Conference', 'Workshop'],
            is_online: true,
            location: 'San Francisco',
            start_date_from: '2024-01-01',
            start_date_to: '2024-12-31',
            registration_required: true,
            cost_type: 'Free',
            target_audience: ['Developers', 'Data Scientists'],
            max_candidates: 10,
          }
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
    });

    it('should handle all hardware filters simultaneously', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Comprehensive hardware search',
          filters: {
            entityTypeIds: ['hardware'],
            hardware_types: ['GPU', 'CPU'],
            manufacturers: ['NVIDIA', 'AMD'],
            price_min: 500,
            price_max: 3000,
            memory_search: '16GB',
            processor_search: 'CUDA',
            power_consumption: 'High',
            form_factor: 'Desktop',
            max_candidates: 8,
          }
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
    });
  });

  describe('Advanced Ranking Validation', () => {
    it('should rank entities with high-quality metrics higher', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need the highest quality AI tools available',
          filters: {
            entityTypeIds: ['ai-tool'],
            max_candidates: 10,
          }
        })
        .expect(200);

      const entities = response.body.recommended_entities;
      expect(entities.length).toBeGreaterThan(0);

      // Verify ranking quality - first entity should have good metrics
      if (entities.length > 1) {
        const firstEntity = entities[0];
        const secondEntity = entities[1];
        
        // First entity should generally have better or equal metrics
        // (allowing for sophisticated ranking that considers multiple factors)
        expect(firstEntity).toBeDefined();
        expect(secondEntity).toBeDefined();
      }
    });

    it('should consider user preferences in ranking', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I prefer beginner-friendly tools with good documentation',
          filters: {
            entityTypeIds: ['ai-tool'],
            technical_levels: ['BEGINNER'],
            max_candidates: 15,
          }
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
      expect(response.body.explanation).toMatch(/beginner|documentation|easy/i);
    });

    it('should apply diversity bonus to avoid echo chambers', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need various AI tools for different purposes',
          filters: {
            entityTypeIds: ['ai-tool'],
            max_candidates: 20,
          }
        })
        .expect(200);

      const entities = response.body.recommended_entities;
      expect(entities.length).toBeGreaterThan(0);

      // Should have some diversity in entity types/categories
      if (entities.length > 5) {
        const categories = entities.map(e => e.type).filter(Boolean);
        const uniqueCategories = new Set(categories);
        expect(uniqueCategories.size).toBeGreaterThan(1);
      }
    });
  });

  describe('Performance and Caching', () => {
    it('should demonstrate caching effectiveness', async () => {
      const query = {
        problem_description: 'AI tool for data analysis with caching test',
        filters: {
          entityTypeIds: ['ai-tool'],
          use_cases_search: 'data analysis',
          max_candidates: 10,
        }
      };

      // First request (cache miss)
      const start1 = Date.now();
      const response1 = await request(app.getHttpServer())
        .post('/recommendations')
        .send(query)
        .expect(200);
      const time1 = Date.now() - start1;

      // Second request (should hit cache)
      const start2 = Date.now();
      const response2 = await request(app.getHttpServer())
        .post('/recommendations')
        .send(query)
        .expect(200);
      const time2 = Date.now() - start2;

      expect(response1.body.recommended_entities).toBeDefined();
      expect(response2.body.recommended_entities).toBeDefined();
      
      // Second request should be faster due to caching
      expect(time2).toBeLessThan(time1 * 0.8);
    });

    it('should handle concurrent requests efficiently', async () => {
      const requests = Array(5).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `Concurrent test ${index}`,
            filters: {
              entityTypeIds: ['ai-tool'],
              max_candidates: 8,
            }
          })
      );

      const start = Date.now();
      const responses = await Promise.all(requests);
      const totalTime = Date.now() - start;

      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.recommended_entities).toBeDefined();
      });

      // Should handle 5 concurrent requests efficiently
      expect(totalTime).toBeLessThan(8000);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty problem description gracefully', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: '',
          filters: { max_candidates: 5 }
        })
        .expect(400);

      expect(response.body.message).toBeDefined();
    });

    it('should handle invalid filter values gracefully', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Test invalid filters',
          filters: {
            max_candidates: 1000, // Too high
            technical_levels: ['INVALID_LEVEL'],
            price_min: -100, // Invalid negative price
          }
        })
        .expect(400);

      expect(response.body.message).toBeDefined();
    });

    it('should handle no results scenario gracefully', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Extremely specific query that should return no results',
          filters: {
            entityTypeIds: ['ai-tool'],
            technical_levels: ['EXPERT'],
            has_api: true,
            has_free_tier: true,
            frameworks: ['NonExistentFramework'],
            price_max: 1, // Impossibly low price
            max_candidates: 5,
          }
        })
        .expect(200);

      expect(response.body.recommended_entities).toEqual([]);
      expect(response.body.candidates_analyzed).toBe(0);
      expect(response.body.explanation).toContain('No relevant entities found');
    });

    it('should handle malformed request body', async () => {
      await request(app.getHttpServer())
        .post('/recommendations')
        .send('invalid json')
        .expect(400);
    });

    it('should handle missing required fields', async () => {
      await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          filters: { max_candidates: 10 }
          // Missing problem_description
        })
        .expect(400);
    });
  });

  describe('Multi-Entity Type Scenarios', () => {
    it('should handle mixed entity type requests', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need both AI tools and courses to learn machine learning',
          filters: {
            entityTypeIds: ['ai-tool', 'course'],
            technical_levels: ['BEGINNER'],
            skill_levels: ['BEGINNER'],
            has_free_tier: true,
            max_candidates: 20,
          }
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
      expect(response.body.explanation).toBeDefined();
    });

    it('should handle comprehensive learning path request', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Complete learning path: courses, tools, events, and job opportunities in AI',
          filters: {
            entityTypeIds: ['course', 'ai-tool', 'event', 'job'],
            technical_levels: ['BEGINNER', 'INTERMEDIATE'],
            skill_levels: ['BEGINNER', 'INTERMEDIATE'],
            experience_levels: ['ENTRY', 'JUNIOR'],
            max_candidates: 25,
          }
        })
        .expect(200);

      const entities = response.body.recommended_entities;
      expect(entities).toBeDefined();
      expect(entities.length).toBeGreaterThan(0);

      // Should include multiple entity types
      if (entities.length > 4) {
        const types = entities.map(e => e.type).filter(Boolean);
        const uniqueTypes = new Set(types);
        expect(uniqueTypes.size).toBeGreaterThan(1);
      }
    });
  });
});
