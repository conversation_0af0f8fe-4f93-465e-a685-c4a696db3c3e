import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';

describe('Performance and Load Testing - Comprehensive Suite', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Response Time Benchmarks', () => {
    const performanceTargets = {
      simple_recommendation: 500,    // 500ms
      complex_recommendation: 1000,  // 1 second
      chat_response: 1500,          // 1.5 seconds
      cached_query: 100,            // 100ms
    };

    it('should meet simple recommendation performance targets', async () => {
      const iterations = 5;
      const responseTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        
        await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: 'I need an AI tool for data analysis',
            filters: {
              entityTypeIds: ['ai-tool'],
              max_candidates: 10,
            }
          })
          .expect(200);

        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
      }

      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);

      expect(avgResponseTime).toBeLessThan(performanceTargets.simple_recommendation);
      expect(maxResponseTime).toBeLessThan(performanceTargets.simple_recommendation * 1.5);

      console.log(`Simple recommendations - Avg: ${avgResponseTime.toFixed(0)}ms, Max: ${maxResponseTime}ms`);
    });

    it('should meet complex recommendation performance targets', async () => {
      const iterations = 3;
      const responseTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        
        await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: 'I need a comprehensive AI solution for enterprise machine learning with advanced features, API access, multiple framework support, and extensive documentation',
            filters: {
              entityTypeIds: ['ai-tool'],
              technical_levels: ['ADVANCED', 'EXPERT'],
              has_api: true,
              has_free_tier: false,
              platforms: ['Web', 'Linux', 'Windows'],
              frameworks: ['TensorFlow', 'PyTorch', 'Scikit-learn'],
              use_cases_search: 'machine learning',
              key_features_search: 'enterprise',
              price_ranges: ['MEDIUM', 'HIGH'],
              integrations: ['REST API', 'Python SDK', 'Docker'],
              target_audience: ['Developers', 'Data Scientists', 'Enterprises'],
              max_candidates: 25,
            }
          })
          .expect(200);

        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
      }

      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);

      expect(avgResponseTime).toBeLessThan(performanceTargets.complex_recommendation);
      expect(maxResponseTime).toBeLessThan(performanceTargets.complex_recommendation * 1.5);

      console.log(`Complex recommendations - Avg: ${avgResponseTime.toFixed(0)}ms, Max: ${maxResponseTime}ms`);
    });

    it('should meet chat response performance targets', async () => {
      const iterations = 5;
      const responseTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        
        await request(app.getHttpServer())
          .post('/chat')
          .send({
            message: `I need an AI tool for my project ${i} with specific requirements`,
            session_id: undefined,
          })
          .expect(200);

        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
      }

      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);

      expect(avgResponseTime).toBeLessThan(performanceTargets.chat_response);
      expect(maxResponseTime).toBeLessThan(performanceTargets.chat_response * 1.5);

      console.log(`Chat responses - Avg: ${avgResponseTime.toFixed(0)}ms, Max: ${maxResponseTime}ms`);
    });

    it('should demonstrate caching performance improvements', async () => {
      const query = {
        problem_description: 'AI tool for caching performance test',
        filters: {
          entityTypeIds: ['ai-tool'],
          use_cases_search: 'data analysis',
          max_candidates: 15,
        }
      };

      // First request (cache miss)
      const start1 = Date.now();
      await request(app.getHttpServer())
        .post('/recommendations')
        .send(query)
        .expect(200);
      const uncachedTime = Date.now() - start1;

      // Second request (cache hit)
      const start2 = Date.now();
      await request(app.getHttpServer())
        .post('/recommendations')
        .send(query)
        .expect(200);
      const cachedTime = Date.now() - start2;

      // Third request (should also hit cache)
      const start3 = Date.now();
      await request(app.getHttpServer())
        .post('/recommendations')
        .send(query)
        .expect(200);
      const cachedTime2 = Date.now() - start3;

      expect(cachedTime).toBeLessThan(performanceTargets.cached_query);
      expect(cachedTime2).toBeLessThan(performanceTargets.cached_query);
      expect(cachedTime).toBeLessThan(uncachedTime * 0.5); // At least 50% improvement

      console.log(`Caching performance - Uncached: ${uncachedTime}ms, Cached: ${cachedTime}ms, Improvement: ${((uncachedTime - cachedTime) / uncachedTime * 100).toFixed(1)}%`);
    });
  });

  describe('Concurrent Load Testing', () => {
    it('should handle moderate concurrent load (10 requests)', async () => {
      const concurrentRequests = 10;
      const requests = Array(concurrentRequests).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `Concurrent load test ${index}`,
            filters: {
              entityTypeIds: ['ai-tool'],
              max_candidates: 10,
            }
          })
      );

      const startTime = Date.now();
      const responses = await Promise.all(requests);
      const totalTime = Date.now() - startTime;

      // All requests should succeed
      responses.forEach((response, index) => {
        expect(response.status).toBe(200);
        expect(response.body.recommended_entities).toBeDefined();
      });

      // Should handle concurrent load efficiently
      expect(totalTime).toBeLessThan(5000); // 10 requests within 5 seconds
      
      console.log(`Concurrent load (${concurrentRequests} requests) completed in ${totalTime}ms`);
    });

    it('should handle high concurrent load (25 requests)', async () => {
      const concurrentRequests = 25;
      const requests = Array(concurrentRequests).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `High load test ${index}`,
            filters: {
              entityTypeIds: ['ai-tool'],
              max_candidates: 8,
            }
          })
      );

      const startTime = Date.now();
      const responses = await Promise.allSettled(requests);
      const totalTime = Date.now() - startTime;

      // Count successful responses
      const successfulResponses = responses.filter(r => 
        r.status === 'fulfilled' && r.value.status === 200
      ).length;

      // At least 80% should succeed under high load
      expect(successfulResponses).toBeGreaterThan(concurrentRequests * 0.8);
      
      // Should complete within reasonable time
      expect(totalTime).toBeLessThan(15000); // 25 requests within 15 seconds

      console.log(`High concurrent load (${concurrentRequests} requests) - ${successfulResponses} successful in ${totalTime}ms`);
    });

    it('should handle mixed workload (recommendations + chat)', async () => {
      const recommendationRequests = Array(8).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `Mixed workload recommendation ${index}`,
            filters: {
              entityTypeIds: ['ai-tool'],
              max_candidates: 10,
            }
          })
      );

      const chatRequests = Array(7).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/chat')
          .send({
            message: `Mixed workload chat ${index}`,
            session_id: undefined,
          })
      );

      const startTime = Date.now();
      const allResponses = await Promise.allSettled([
        ...recommendationRequests,
        ...chatRequests
      ]);
      const totalTime = Date.now() - startTime;

      const successfulResponses = allResponses.filter(r => 
        r.status === 'fulfilled' && r.value.status === 200
      ).length;

      // Most requests should succeed
      expect(successfulResponses).toBeGreaterThan(12); // At least 80% of 15 requests
      expect(totalTime).toBeLessThan(10000); // Mixed load within 10 seconds

      console.log(`Mixed workload (15 requests) - ${successfulResponses} successful in ${totalTime}ms`);
    });
  });

  describe('Memory and Resource Management', () => {
    it('should maintain stable memory usage under load', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Perform intensive operations
      const intensiveRequests = Array(20).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `Memory test ${index} with complex filtering and ranking requirements`,
            filters: {
              entityTypeIds: ['ai-tool', 'course', 'job'],
              technical_levels: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'],
              has_api: true,
              has_free_tier: true,
              platforms: ['Web', 'Linux', 'Windows', 'macOS'],
              frameworks: ['TensorFlow', 'PyTorch', 'Scikit-learn'],
              max_candidates: 20,
            }
          })
      );

      await Promise.all(intensiveRequests);

      // Allow garbage collection
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024; // MB

      // Memory increase should be reasonable
      expect(memoryIncrease).toBeLessThan(150); // Less than 150MB increase

      console.log(`Memory usage - Initial: ${(initialMemory / 1024 / 1024).toFixed(1)}MB, Final: ${(finalMemory / 1024 / 1024).toFixed(1)}MB, Increase: ${memoryIncrease.toFixed(1)}MB`);
    });

    it('should handle sustained load over time', async () => {
      const duration = 30000; // 30 seconds
      const requestInterval = 500; // Every 500ms
      const startTime = Date.now();
      const responseTimes: number[] = [];
      let requestCount = 0;
      let successCount = 0;

      while (Date.now() - startTime < duration) {
        const reqStart = Date.now();
        
        try {
          const response = await request(app.getHttpServer())
            .post('/recommendations')
            .send({
              problem_description: `Sustained load test ${requestCount}`,
              filters: {
                entityTypeIds: ['ai-tool'],
                max_candidates: 8,
              }
            });

          if (response.status === 200) {
            successCount++;
            responseTimes.push(Date.now() - reqStart);
          }
        } catch (error) {
          // Count failed requests
        }

        requestCount++;
        
        // Wait before next request
        await new Promise(resolve => setTimeout(resolve, requestInterval));
      }

      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const successRate = (successCount / requestCount) * 100;

      // Should maintain good performance under sustained load
      expect(successRate).toBeGreaterThan(90); // 90% success rate
      expect(avgResponseTime).toBeLessThan(2000); // Average under 2 seconds

      console.log(`Sustained load - ${requestCount} requests over ${duration/1000}s, ${successRate.toFixed(1)}% success rate, ${avgResponseTime.toFixed(0)}ms avg response time`);
    });
  });

  describe('Stress Testing and Breaking Points', () => {
    it('should gracefully handle extreme concurrent load', async () => {
      const extremeLoad = 50;
      const requests = Array(extremeLoad).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `Stress test ${index}`,
            filters: {
              entityTypeIds: ['ai-tool'],
              max_candidates: 5,
            }
          })
      );

      const startTime = Date.now();
      const responses = await Promise.allSettled(requests);
      const totalTime = Date.now() - startTime;

      const successfulResponses = responses.filter(r => 
        r.status === 'fulfilled' && r.value.status === 200
      ).length;

      const failedResponses = responses.filter(r => 
        r.status === 'rejected' || (r.status === 'fulfilled' && r.value.status !== 200)
      ).length;

      // Should handle some load gracefully, even if not all requests succeed
      expect(successfulResponses).toBeGreaterThan(extremeLoad * 0.5); // At least 50%
      
      console.log(`Stress test (${extremeLoad} requests) - ${successfulResponses} successful, ${failedResponses} failed in ${totalTime}ms`);
    });

    it('should handle complex queries under load', async () => {
      const complexRequests = Array(10).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `Complex stress test ${index} with comprehensive requirements and multiple filtering criteria`,
            filters: {
              entityTypeIds: ['ai-tool', 'course'],
              technical_levels: ['INTERMEDIATE', 'ADVANCED'],
              skill_levels: ['INTERMEDIATE', 'ADVANCED'],
              has_api: true,
              has_free_tier: false,
              platforms: ['Web', 'Linux', 'Windows'],
              frameworks: ['TensorFlow', 'PyTorch', 'Keras'],
              use_cases_search: 'machine learning',
              key_features_search: 'enterprise scalability',
              price_ranges: ['MEDIUM', 'HIGH'],
              integrations: ['REST API', 'Python SDK', 'Docker', 'Kubernetes'],
              target_audience: ['Developers', 'Data Scientists', 'Enterprises'],
              max_candidates: 30,
            }
          })
      );

      const startTime = Date.now();
      const responses = await Promise.allSettled(complexRequests);
      const totalTime = Date.now() - startTime;

      const successfulResponses = responses.filter(r => 
        r.status === 'fulfilled' && r.value.status === 200
      ).length;

      // Complex queries should still succeed under load
      expect(successfulResponses).toBeGreaterThan(7); // At least 70%
      expect(totalTime).toBeLessThan(20000); // Within 20 seconds

      console.log(`Complex stress test - ${successfulResponses}/10 successful in ${totalTime}ms`);
    });
  });

  describe('Performance Regression Detection', () => {
    it('should maintain consistent performance across multiple runs', async () => {
      const runs = 3;
      const requestsPerRun = 5;
      const runResults: number[] = [];

      for (let run = 0; run < runs; run++) {
        const runStart = Date.now();
        
        const requests = Array(requestsPerRun).fill(null).map((_, index) =>
          request(app.getHttpServer())
            .post('/recommendations')
            .send({
              problem_description: `Performance consistency test run ${run} request ${index}`,
              filters: {
                entityTypeIds: ['ai-tool'],
                max_candidates: 10,
              }
            })
        );

        await Promise.all(requests);
        const runTime = Date.now() - runStart;
        runResults.push(runTime);

        // Small delay between runs
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      const avgRunTime = runResults.reduce((a, b) => a + b, 0) / runResults.length;
      const maxRunTime = Math.max(...runResults);
      const minRunTime = Math.min(...runResults);
      const variance = maxRunTime - minRunTime;

      // Performance should be consistent (variance < 50% of average)
      expect(variance).toBeLessThan(avgRunTime * 0.5);

      console.log(`Performance consistency - Avg: ${avgRunTime.toFixed(0)}ms, Min: ${minRunTime}ms, Max: ${maxRunTime}ms, Variance: ${variance}ms`);
    });
  });
});
