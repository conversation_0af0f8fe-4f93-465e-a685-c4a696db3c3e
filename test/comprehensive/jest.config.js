module.exports = {
  displayName: 'Comprehensive Test Suite',
  testEnvironment: 'node',
  rootDir: '../../',
  testMatch: [
    '<rootDir>/test/comprehensive/**/*.spec.ts'
  ],
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/**/*.(t|j)s',
    '!src/**/*.spec.ts',
    '!src/**/*.interface.ts',
    '!src/**/*.dto.ts',
    '!src/main.ts',
  ],
  coverageDirectory: '<rootDir>/coverage/comprehensive',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/test/comprehensive/setup.ts'],
  testTimeout: 30000, // 30 seconds for comprehensive tests
  maxWorkers: 1, // Run tests sequentially for accurate performance measurements
  verbose: true,
  detectOpenHandles: true,
  forceExit: true,
  reporters: [
    'default',
    [
      'jest-html-reporters',
      {
        publicPath: '<rootDir>/test-reports/comprehensive',
        filename: 'comprehensive-test-report.html',
        expand: true,
        hideIcon: false,
        pageTitle: 'AI Nav Backend - Comprehensive Test Report',
        logoImgPath: undefined,
        includeFailureMsg: true,
        includeSuiteFailure: true,
      },
    ],
    [
      'jest-junit',
      {
        outputDirectory: '<rootDir>/test-reports/comprehensive',
        outputName: 'junit.xml',
        suiteName: 'Comprehensive Test Suite',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: true,
      },
    ],
  ],
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/tsconfig.json',
    },
  },
  moduleNameMapping: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
};
