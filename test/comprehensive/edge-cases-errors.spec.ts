import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';

describe('Edge Cases and Error Handling - Comprehensive Tests', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Input Validation and Sanitization', () => {
    describe('Recommendations Endpoint', () => {
      it('should reject empty problem description', async () => {
        await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: '',
            filters: { max_candidates: 10 }
          })
          .expect(400);
      });

      it('should reject null problem description', async () => {
        await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: null,
            filters: { max_candidates: 10 }
          })
          .expect(400);
      });

      it('should reject missing problem description', async () => {
        await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            filters: { max_candidates: 10 }
          })
          .expect(400);
      });

      it('should reject invalid max_candidates values', async () => {
        const invalidValues = [-1, 0, 101, 'invalid', null, undefined];
        
        for (const value of invalidValues) {
          await request(app.getHttpServer())
            .post('/recommendations')
            .send({
              problem_description: 'Test query',
              filters: { max_candidates: value }
            })
            .expect(400);
        }
      });

      it('should reject invalid technical_levels', async () => {
        await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: 'Test query',
            filters: {
              technical_levels: ['INVALID_LEVEL', 'ANOTHER_INVALID'],
              max_candidates: 10
            }
          })
          .expect(400);
      });

      it('should reject invalid price ranges', async () => {
        await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: 'Test query',
            filters: {
              price_min: -100,
              price_max: -50,
              max_candidates: 10
            }
          })
          .expect(400);
      });

      it('should handle extremely long problem descriptions', async () => {
        const longDescription = 'I need an AI tool '.repeat(1000); // Very long description
        
        const response = await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: longDescription,
            filters: { max_candidates: 10 }
          });

        // Should either succeed or fail gracefully with 400
        expect([200, 400]).toContain(response.status);
      });

      it('should sanitize special characters in descriptions', async () => {
        const maliciousDescription = '<script>alert("xss")</script> I need AI tools';
        
        const response = await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: maliciousDescription,
            filters: { max_candidates: 10 }
          })
          .expect(200);

        // Response should not contain the script tag
        expect(response.body.explanation).not.toContain('<script>');
      });

      it('should handle unicode and special characters', async () => {
        const unicodeDescription = 'I need AI tools for 机器学习 and データサイエンス with émojis 🤖🔬';
        
        await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: unicodeDescription,
            filters: { max_candidates: 10 }
          })
          .expect(200);
      });
    });

    describe('Chat Endpoint', () => {
      it('should reject empty messages', async () => {
        await request(app.getHttpServer())
          .post('/chat')
          .send({
            message: '',
            session_id: undefined
          })
          .expect(400);
      });

      it('should reject null messages', async () => {
        await request(app.getHttpServer())
          .post('/chat')
          .send({
            message: null,
            session_id: undefined
          })
          .expect(400);
      });

      it('should handle invalid session IDs gracefully', async () => {
        const invalidSessionIds = [
          'invalid-format',
          '123',
          'session-with-special-chars!@#$%',
          'x'.repeat(200), // Very long session ID
        ];

        for (const sessionId of invalidSessionIds) {
          const response = await request(app.getHttpServer())
            .post('/chat')
            .send({
              message: 'Test message',
              session_id: sessionId
            });

          // Should either succeed with new session or handle gracefully
          expect([200, 400]).toContain(response.status);
        }
      });

      it('should handle extremely long messages', async () => {
        const longMessage = 'This is a very long message '.repeat(500);
        
        const response = await request(app.getHttpServer())
          .post('/chat')
          .send({
            message: longMessage,
            session_id: undefined
          });

        expect([200, 400]).toContain(response.status);
      });

      it('should sanitize malicious input in chat messages', async () => {
        const maliciousMessage = '<script>alert("xss")</script> Tell me about AI tools';
        
        const response = await request(app.getHttpServer())
          .post('/chat')
          .send({
            message: maliciousMessage,
            session_id: undefined
          })
          .expect(200);

        expect(response.body.message).not.toContain('<script>');
      });
    });
  });

  describe('Boundary Conditions', () => {
    it('should handle zero results scenario gracefully', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Extremely specific query that should return no results',
          filters: {
            entityTypeIds: ['ai-tool'],
            technical_levels: ['EXPERT'],
            has_api: true,
            has_free_tier: true,
            frameworks: ['NonExistentFramework'],
            price_max: 0.01, // Impossibly low price
            max_candidates: 10
          }
        })
        .expect(200);

      expect(response.body.recommended_entities).toEqual([]);
      expect(response.body.candidates_analyzed).toBe(0);
      expect(response.body.explanation).toContain('No relevant entities found');
    });

    it('should handle minimum candidates request', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'AI tools',
          filters: { max_candidates: 1 }
        })
        .expect(200);

      expect(response.body.recommended_entities.length).toBeLessThanOrEqual(1);
    });

    it('should handle maximum candidates request', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'AI tools',
          filters: { max_candidates: 50 }
        })
        .expect(200);

      expect(response.body.recommended_entities.length).toBeLessThanOrEqual(50);
    });

    it('should handle conflicting filter combinations', async () => {
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'AI tools with conflicting requirements',
          filters: {
            entityTypeIds: ['ai-tool'],
            has_free_tier: true,
            price_min: 1000, // Conflicts with free tier
            technical_levels: ['BEGINNER', 'EXPERT'], // Conflicting levels
            max_candidates: 10
          }
        })
        .expect(200);

      // Should handle gracefully, possibly returning fewer results
      expect(response.body.recommended_entities).toBeDefined();
    });
  });

  describe('Network and Timeout Handling', () => {
    it('should handle slow responses gracefully', async () => {
      // This test simulates a slow query that might timeout
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Complex query that might be slow to process with many filters and requirements',
          filters: {
            entityTypeIds: ['ai-tool', 'course', 'job', 'event', 'hardware'],
            technical_levels: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'],
            has_api: true,
            has_free_tier: true,
            platforms: ['Web', 'Linux', 'Windows', 'macOS', 'Mobile'],
            frameworks: ['TensorFlow', 'PyTorch', 'Scikit-learn', 'Keras'],
            max_candidates: 50
          }
        })
        .timeout(15000); // 15 second timeout

      expect([200, 408, 500]).toContain(response.status);
    });

    it('should handle malformed JSON gracefully', async () => {
      await request(app.getHttpServer())
        .post('/recommendations')
        .send('{"invalid": json,}')
        .expect(400);
    });

    it('should handle missing Content-Type header', async () => {
      await request(app.getHttpServer())
        .post('/recommendations')
        .set('Content-Type', '')
        .send('some data')
        .expect(400);
    });
  });

  describe('Resource Exhaustion Scenarios', () => {
    it('should handle memory pressure gracefully', async () => {
      // Simulate memory pressure with many concurrent requests
      const requests = Array(20).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `Memory pressure test ${index} with complex requirements`,
            filters: {
              entityTypeIds: ['ai-tool', 'course'],
              max_candidates: 30
            }
          })
      );

      const responses = await Promise.allSettled(requests);
      
      // Most requests should succeed, but some might fail under pressure
      const successfulResponses = responses.filter(r => 
        r.status === 'fulfilled' && r.value.status === 200
      ).length;

      expect(successfulResponses).toBeGreaterThan(10); // At least 50% should succeed
    });

    it('should handle database connection issues gracefully', async () => {
      // This test would ideally simulate database connection issues
      // For now, we test that the system handles errors gracefully
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Test database resilience',
          filters: { max_candidates: 10 }
        });

      // Should either succeed or fail gracefully
      expect([200, 500, 503]).toContain(response.status);
    });
  });

  describe('Data Consistency and Integrity', () => {
    it('should maintain session consistency across requests', async () => {
      const sessionId = `consistency-test-${Date.now()}`;
      
      // Send multiple messages to the same session
      const messages = [
        'I need AI tools',
        'For machine learning',
        'I am a beginner'
      ];

      for (const message of messages) {
        const response = await request(app.getHttpServer())
          .post('/chat')
          .send({
            message,
            session_id: sessionId
          })
          .expect(200);

        expect(response.body.session_id).toBe(sessionId);
      }

      // Retrieve conversation history
      const historyResponse = await request(app.getHttpServer())
        .get(`/chat/${sessionId}/history`)
        .expect(200);

      expect(historyResponse.body.messages.length).toBeGreaterThanOrEqual(messages.length);
    });

    it('should handle concurrent modifications to the same session', async () => {
      const sessionId = `concurrent-test-${Date.now()}`;
      
      // Send concurrent messages to the same session
      const requests = Array(5).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/chat')
          .send({
            message: `Concurrent message ${index}`,
            session_id: sessionId
          })
      );

      const responses = await Promise.allSettled(requests);
      
      // All should have the same session ID
      responses.forEach(response => {
        if (response.status === 'fulfilled' && response.value.status === 200) {
          expect(response.value.body.session_id).toBe(sessionId);
        }
      });
    });
  });

  describe('Security and Injection Prevention', () => {
    it('should prevent SQL injection attempts', async () => {
      const sqlInjectionAttempts = [
        "'; DROP TABLE entities; --",
        "' OR '1'='1",
        "'; INSERT INTO entities VALUES ('malicious'); --",
        "' UNION SELECT * FROM users; --"
      ];

      for (const injection of sqlInjectionAttempts) {
        const response = await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `AI tools ${injection}`,
            filters: { max_candidates: 10 }
          });

        // Should either succeed safely or reject the input
        expect([200, 400]).toContain(response.status);
        
        if (response.status === 200) {
          // Response should not contain evidence of SQL injection
          expect(response.body.explanation).not.toContain('DROP TABLE');
          expect(response.body.explanation).not.toContain('INSERT INTO');
        }
      }
    });

    it('should prevent NoSQL injection attempts', async () => {
      const noSqlInjectionAttempts = [
        '{"$ne": null}',
        '{"$gt": ""}',
        '{"$where": "this.password.length > 0"}',
        '{"$regex": ".*"}'
      ];

      for (const injection of noSqlInjectionAttempts) {
        await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `AI tools ${injection}`,
            filters: { max_candidates: 10 }
          })
          .expect(200); // Should handle safely
      }
    });

    it('should prevent command injection attempts', async () => {
      const commandInjectionAttempts = [
        '; ls -la',
        '&& cat /etc/passwd',
        '| rm -rf /',
        '`whoami`',
        '$(id)'
      ];

      for (const injection of commandInjectionAttempts) {
        const response = await request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `AI tools ${injection}`,
            filters: { max_candidates: 10 }
          });

        expect([200, 400]).toContain(response.status);
      }
    });
  });

  describe('Rate Limiting and Abuse Prevention', () => {
    it('should handle rapid successive requests', async () => {
      const rapidRequests = Array(10).fill(null).map(() =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: 'Rapid request test',
            filters: { max_candidates: 5 }
          })
      );

      const responses = await Promise.allSettled(rapidRequests);
      
      // Some requests might be rate limited, but system should remain stable
      const statusCodes = responses
        .filter(r => r.status === 'fulfilled')
        .map(r => r.value.status);

      // Should see mostly 200s, possibly some 429s (rate limited)
      statusCodes.forEach(status => {
        expect([200, 429]).toContain(status);
      });
    });

    it('should handle requests with excessive payload size', async () => {
      const largePayload = {
        problem_description: 'Test with large payload',
        filters: {
          max_candidates: 10,
          // Add many filter properties to increase payload size
          ...Object.fromEntries(
            Array(100).fill(null).map((_, i) => [`custom_field_${i}`, `value_${i}`])
          )
        }
      };

      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send(largePayload);

      // Should either handle gracefully or reject with 413 (Payload Too Large)
      expect([200, 400, 413]).toContain(response.status);
    });
  });

  describe('Graceful Degradation', () => {
    it('should provide basic functionality when advanced features fail', async () => {
      // Test that basic recommendations work even if advanced ranking fails
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Simple AI tool request for graceful degradation test',
          filters: { max_candidates: 5 }
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
      expect(response.body.explanation).toBeDefined();
    });

    it('should handle partial service failures in chat', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'Test graceful degradation in chat',
          session_id: undefined
        })
        .expect(200);

      expect(response.body.message).toBeDefined();
      expect(response.body.session_id).toBeDefined();
    });
  });
});
