import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Global test setup
beforeAll(async () => {
  console.log('🧪 Setting up comprehensive test environment...');
  
  // Set test-specific environment variables
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests
  
  // Increase timeout for comprehensive tests
  jest.setTimeout(30000);
  
  console.log('✅ Test environment ready');
});

afterAll(async () => {
  console.log('🧹 Cleaning up test environment...');
  
  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
  
  console.log('✅ Cleanup complete');
});

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Extend Jest matchers for better assertions
expect.extend({
  toBeWithinRange(received: number, floor: number, ceiling: number) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () =>
          `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },
  
  toHavePerformanceWithin(received: number, expectedMs: number, tolerancePercent: number = 20) {
    const tolerance = expectedMs * (tolerancePercent / 100);
    const pass = received <= expectedMs + tolerance;
    
    if (pass) {
      return {
        message: () =>
          `expected ${received}ms not to be within performance target of ${expectedMs}ms (±${tolerancePercent}%)`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected ${received}ms to be within performance target of ${expectedMs}ms (±${tolerancePercent}%), but it exceeded by ${received - expectedMs}ms`,
        pass: false,
      };
    }
  },
  
  toHaveValidEntityStructure(received: any) {
    const requiredFields = ['id', 'name', 'type'];
    const hasRequiredFields = requiredFields.every(field => 
      received.hasOwnProperty(field) && received[field] !== null && received[field] !== undefined
    );
    
    if (hasRequiredFields) {
      return {
        message: () => `expected entity not to have valid structure`,
        pass: true,
      };
    } else {
      const missingFields = requiredFields.filter(field => 
        !received.hasOwnProperty(field) || received[field] === null || received[field] === undefined
      );
      return {
        message: () => `expected entity to have valid structure, missing fields: ${missingFields.join(', ')}`,
        pass: false,
      };
    }
  },
  
  toHaveValidRankingStructure(received: any) {
    const requiredFields = ['rankingScore', 'rankingBreakdown', 'rankingReason'];
    const hasRequiredFields = requiredFields.every(field => 
      received.hasOwnProperty(field) && received[field] !== null && received[field] !== undefined
    );
    
    const hasValidScore = typeof received.rankingScore === 'number' && 
                         received.rankingScore >= 0 && 
                         received.rankingScore <= 1;
    
    if (hasRequiredFields && hasValidScore) {
      return {
        message: () => `expected entity not to have valid ranking structure`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected entity to have valid ranking structure with score 0-1`,
        pass: false,
      };
    }
  }
});

// Declare custom matchers for TypeScript
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeWithinRange(floor: number, ceiling: number): R;
      toHavePerformanceWithin(expectedMs: number, tolerancePercent?: number): R;
      toHaveValidEntityStructure(): R;
      toHaveValidRankingStructure(): R;
    }
  }
}

// Performance monitoring utilities
export class TestPerformanceMonitor {
  private static measurements: Map<string, number[]> = new Map();
  
  static startMeasurement(testName: string): () => number {
    const startTime = Date.now();
    return () => {
      const duration = Date.now() - startTime;
      
      if (!this.measurements.has(testName)) {
        this.measurements.set(testName, []);
      }
      this.measurements.get(testName)!.push(duration);
      
      return duration;
    };
  }
  
  static getAverageTime(testName: string): number {
    const times = this.measurements.get(testName) || [];
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
  }
  
  static getPerformanceReport(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const report: Record<string, { avg: number; min: number; max: number; count: number }> = {};
    
    this.measurements.forEach((times, testName) => {
      if (times.length > 0) {
        report[testName] = {
          avg: times.reduce((a, b) => a + b, 0) / times.length,
          min: Math.min(...times),
          max: Math.max(...times),
          count: times.length
        };
      }
    });
    
    return report;
  }
  
  static reset(): void {
    this.measurements.clear();
  }
}

// Test data generators
export class TestDataGenerator {
  static generateRandomString(length: number = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
  
  static generateTestFilters(entityType: string = 'ai-tool'): any {
    const baseFilters = {
      entityTypeIds: [entityType],
      max_candidates: Math.floor(Math.random() * 20) + 5, // 5-25
    };
    
    // Add random filters based on entity type
    if (entityType === 'ai-tool') {
      return {
        ...baseFilters,
        technical_levels: ['BEGINNER', 'INTERMEDIATE'][Math.floor(Math.random() * 2)],
        has_api: Math.random() > 0.5,
        has_free_tier: Math.random() > 0.5,
        platforms: ['Web', 'Linux', 'Windows'][Math.floor(Math.random() * 3)],
      };
    }
    
    return baseFilters;
  }
  
  static generateTestConversation(): Array<{ message: string; expectedStage: string }> {
    return [
      { message: 'I need help with AI tools', expectedStage: 'discovery' },
      { message: 'For machine learning projects', expectedStage: 'refinement' },
      { message: 'I am a beginner', expectedStage: 'refinement' },
      { message: 'Budget is under $50', expectedStage: 'ready' },
    ];
  }
}

// Memory monitoring utilities
export class MemoryMonitor {
  private static initialMemory: number = process.memoryUsage().heapUsed;
  
  static getMemoryUsage(): { current: number; increase: number; percentage: number } {
    const current = process.memoryUsage().heapUsed;
    const increase = current - this.initialMemory;
    const percentage = (increase / this.initialMemory) * 100;
    
    return {
      current: current / 1024 / 1024, // MB
      increase: increase / 1024 / 1024, // MB
      percentage
    };
  }
  
  static reset(): void {
    this.initialMemory = process.memoryUsage().heapUsed;
  }
  
  static forceGarbageCollection(): void {
    if (global.gc) {
      global.gc();
    }
  }
}

// Export utilities for use in tests
export { TestPerformanceMonitor as PerfMonitor, MemoryMonitor };

console.log('🔧 Comprehensive test utilities loaded');
