import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';

describe('Enhanced Chat System - Comprehensive Integration Tests', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Conversational Filter Refinement', () => {
    it('should build comprehensive filters through natural conversation', async () => {
      const sessionId = `test-conversation-${Date.now()}`;
      const conversationFlow = [
        {
          message: 'I need help with AI for my startup',
          expectedResponse: /what type|which kind|specific/i,
          expectedFilters: ['entityTypeIds', 'use_cases_search']
        },
        {
          message: 'We are working on content creation tools',
          expectedResponse: /experience|technical|skill/i,
          expectedFilters: ['use_cases_search']
        },
        {
          message: 'I am a beginner programmer',
          expectedResponse: /budget|cost|price/i,
          expectedFilters: ['technical_levels', 'skill_levels']
        },
        {
          message: 'Budget is under $50 per month',
          expectedResponse: /platform|environment|api/i,
          expectedFilters: ['price_ranges', 'has_free_tier']
        },
        {
          message: 'Must work on Windows and have API access',
          expectedResponse: /found|recommend|perfect/i,
          expectedFilters: ['platforms', 'has_api']
        }
      ];

      for (const [index, step] of conversationFlow.entries()) {
        const response = await request(app.getHttpServer())
          .post('/chat')
          .send({
            message: step.message,
            session_id: sessionId,
          })
          .expect(200);

        expect(response.body.message).toMatch(step.expectedResponse);
        expect(response.body.session_id).toBe(sessionId);
        expect(response.body.conversation_stage).toBeDefined();

        // Later messages should have more discovered entities
        if (index >= 2) {
          expect(response.body.discovered_entities).toBeDefined();
        }

        // Final message should indicate readiness for recommendations
        if (index === conversationFlow.length - 1) {
          expect(response.body.discovered_entities.length).toBeGreaterThan(0);
        }
      }
    });

    it('should handle filter corrections intelligently', async () => {
      const sessionId = `test-correction-${Date.now()}`;

      // Initial request with incorrect information
      const response1 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need an advanced AI tool for machine learning',
          session_id: sessionId,
        })
        .expect(200);

      expect(response1.body.message).toBeDefined();

      // Correction
      const response2 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'Actually, I am a beginner, not advanced',
          session_id: sessionId,
        })
        .expect(200);

      // Should acknowledge the correction
      expect(response2.body.message).toMatch(/got it|updated|corrected|thanks|understand/i);
      expect(response2.body.session_id).toBe(sessionId);
    });

    it('should ask strategic clarifying questions', async () => {
      const vagueCases = [
        'I need help with AI',
        'Looking for something AI-related',
        'AI tools for my business',
        'Machine learning stuff',
        'Help me find AI resources'
      ];

      for (const message of vagueCases) {
        const response = await request(app.getHttpServer())
          .post('/chat')
          .send({
            message,
            session_id: undefined, // New session each time
          })
          .expect(200);

        // Should ask clarifying questions for vague requests
        expect(
          response.body.follow_up_questions?.length > 0 ||
          response.body.suggested_actions?.length > 0 ||
          response.body.message.includes('?')
        ).toBe(true);
      }
    });

    it('should handle complex multi-entity conversations', async () => {
      const sessionId = `test-multi-entity-${Date.now()}`;

      const response1 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I want to learn AI - need both courses and tools',
          session_id: sessionId,
        })
        .expect(200);

      const response2 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'Also interested in AI conferences and job opportunities',
          session_id: sessionId,
        })
        .expect(200);

      const response3 = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I am a beginner, prefer online/remote options, budget flexible',
          session_id: sessionId,
        })
        .expect(200);

      // Should handle multiple entity types
      expect(response3.body.discovered_entities).toBeDefined();
      expect(response3.body.conversation_stage).toBeDefined();
    });
  });

  describe('Advanced Intent Classification', () => {
    const intentTestCases = [
      {
        message: 'I need an AI tool for computer vision projects',
        expectedIntent: 'discovery',
        expectedEntityTypes: ['ai-tool'],
        expectedFilters: ['use_cases_search']
      },
      {
        message: 'Compare TensorFlow vs PyTorch for deep learning',
        expectedIntent: 'comparison',
        expectedEntityTypes: ['ai-tool'],
        expectedFilters: ['frameworks']
      },
      {
        message: 'Tell me more about GPT-4',
        expectedIntent: 'specific_tool',
        expectedEntityTypes: ['ai-tool'],
        expectedFilters: ['searchTerm']
      },
      {
        message: 'What are the latest trends in AI?',
        expectedIntent: 'general_question',
        expectedEntityTypes: [],
        expectedFilters: []
      },
      {
        message: 'Actually, I prefer something more advanced',
        expectedIntent: 'refinement',
        expectedEntityTypes: [],
        expectedFilters: ['technical_levels']
      }
    ];

    intentTestCases.forEach(({ message, expectedIntent, expectedEntityTypes, expectedFilters }) => {
      it(`should classify intent correctly: ${expectedIntent}`, async () => {
        const response = await request(app.getHttpServer())
          .post('/chat')
          .send({
            message,
            session_id: undefined,
          })
          .expect(200);

        expect(response.body.message).toBeDefined();
        expect(response.body.conversation_stage).toBeDefined();

        // For discovery intents, should find entities
        if (expectedIntent === 'discovery' && expectedEntityTypes.length > 0) {
          expect(response.body.discovered_entities).toBeDefined();
        }
      });
    });
  });

  describe('Conversation State Management', () => {
    it('should maintain conversation context across multiple sessions', async () => {
      const sessionId = `test-context-${Date.now()}`;

      // Send several messages
      const messages = [
        'I need AI tools for my project',
        'Specifically for natural language processing',
        'I am an intermediate developer',
        'Budget is around $100 per month',
        'Must have Python SDK'
      ];

      const responses = [];
      for (const message of messages) {
        const response = await request(app.getHttpServer())
          .post('/chat')
          .send({
            message,
            session_id: sessionId,
          })
          .expect(200);
        responses.push(response.body);
      }

      // Each response should maintain the same session
      responses.forEach(response => {
        expect(response.session_id).toBe(sessionId);
      });

      // Later responses should have more context
      expect(responses[responses.length - 1].discovered_entities).toBeDefined();
    });

    it('should retrieve conversation history correctly', async () => {
      const sessionId = `test-history-${Date.now()}`;

      // Send a few messages
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need AI tools',
          session_id: sessionId,
        })
        .expect(200);

      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'For machine learning projects',
          session_id: sessionId,
        })
        .expect(200);

      // Retrieve history
      const historyResponse = await request(app.getHttpServer())
        .get(`/chat/${sessionId}/history`)
        .expect(200);

      expect(historyResponse.body.messages).toBeDefined();
      expect(Array.isArray(historyResponse.body.messages)).toBe(true);
      expect(historyResponse.body.messages.length).toBeGreaterThan(0);
    });

    it('should handle conversation flow optimization', async () => {
      const sessionId = `test-optimization-${Date.now()}`;

      // Simulate an inefficient conversation
      const inefficientMessages = [
        'I need something',
        'For AI',
        'Maybe tools',
        'Or courses',
        'I am not sure'
      ];

      for (const message of inefficientMessages) {
        await request(app.getHttpServer())
          .post('/chat')
          .send({
            message,
            session_id: sessionId,
          })
          .expect(200);
      }

      // System should provide guidance
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'Can you help me be more specific?',
          session_id: sessionId,
        })
        .expect(200);

      expect(response.body.message).toBeDefined();
      expect(
        response.body.follow_up_questions?.length > 0 ||
        response.body.suggested_actions?.length > 0
      ).toBe(true);
    });
  });

  describe('Entity Discovery and Ranking', () => {
    it('should discover entities with comprehensive filtering', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need a beginner-friendly AI tool with API access for Python machine learning projects under $50',
          session_id: undefined,
        })
        .expect(200);

      expect(response.body.discovered_entities).toBeDefined();
      expect(Array.isArray(response.body.discovered_entities)).toBe(true);

      if (response.body.discovered_entities.length > 0) {
        const entity = response.body.discovered_entities[0];
        expect(entity.id).toBeDefined();
        expect(entity.name).toBeDefined();
        expect(entity.type).toBeDefined();
      }
    });

    it('should rank entities intelligently based on conversation context', async () => {
      const sessionId = `test-ranking-${Date.now()}`;

      // Build context
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need high-quality AI tools',
          session_id: sessionId,
        })
        .expect(200);

      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'For enterprise machine learning projects',
          session_id: sessionId,
        })
        .expect(200);

      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'Must be reliable and well-documented',
          session_id: sessionId,
        })
        .expect(200);

      if (response.body.discovered_entities?.length > 1) {
        const entities = response.body.discovered_entities;
        
        // Should be ranked (first entity should be relevant)
        expect(entities[0]).toBeDefined();
        expect(entities[0].name).toBeDefined();
      }
    });

    it('should handle entity discovery for different types', async () => {
      const entityTypeTests = [
        {
          message: 'I want to learn machine learning through online courses',
          expectedType: 'course'
        },
        {
          message: 'Looking for machine learning engineering jobs',
          expectedType: 'job'
        },
        {
          message: 'AI conferences happening this year',
          expectedType: 'event'
        },
        {
          message: 'GPU for training neural networks',
          expectedType: 'hardware'
        }
      ];

      for (const test of entityTypeTests) {
        const response = await request(app.getHttpServer())
          .post('/chat')
          .send({
            message: test.message,
            session_id: undefined,
          })
          .expect(200);

        expect(response.body.discovered_entities).toBeDefined();
        // Should discover entities of the expected type
      }
    });
  });

  describe('Performance and Reliability', () => {
    it('should respond within acceptable time limits', async () => {
      const testMessages = [
        'Simple AI tool request',
        'Complex multi-criteria search with specific requirements and constraints',
        'I need an AI tool for computer vision with real-time processing capabilities, API access, beginner-friendly documentation, under $100 monthly, supporting TensorFlow and PyTorch frameworks'
      ];

      for (const message of testMessages) {
        const startTime = Date.now();
        
        await request(app.getHttpServer())
          .post('/chat')
          .send({
            message,
            session_id: undefined,
          })
          .expect(200);

        const responseTime = Date.now() - startTime;
        expect(responseTime).toBeLessThan(5000); // 5 seconds max
      }
    });

    it('should handle concurrent chat sessions', async () => {
      const concurrentSessions = 3;
      const requests = Array(concurrentSessions).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/chat')
          .send({
            message: `Concurrent session ${index} looking for AI tools`,
            session_id: `concurrent-${index}-${Date.now()}`,
          })
      );

      const responses = await Promise.all(requests);

      responses.forEach((response, index) => {
        expect(response.status).toBe(200);
        expect(response.body.session_id).toContain(`concurrent-${index}`);
        expect(response.body.message).toBeDefined();
      });
    });

    it('should demonstrate caching effectiveness in chat', async () => {
      const message = 'I need a beginner-friendly AI tool with API access for Python projects';

      // First request
      const start1 = Date.now();
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message,
          session_id: undefined,
        })
        .expect(200);
      const time1 = Date.now() - start1;

      // Second request with same message
      const start2 = Date.now();
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message,
          session_id: undefined,
        })
        .expect(200);
      const time2 = Date.now() - start2;

      // Second request should benefit from caching
      expect(time2).toBeLessThan(time1 * 0.9);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty messages gracefully', async () => {
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: '',
          session_id: undefined,
        })
        .expect(400);
    });

    it('should handle very long messages', async () => {
      const longMessage = 'I need an AI tool '.repeat(200);
      
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: longMessage,
          session_id: undefined,
        })
        .expect(200);

      expect(response.body.message).toBeDefined();
    });

    it('should handle invalid session IDs gracefully', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'Test message',
          session_id: 'invalid-session-id-format',
        })
        .expect(200);

      // Should either create new session or handle gracefully
      expect(response.body.session_id).toBeDefined();
      expect(response.body.message).toBeDefined();
    });

    it('should handle malformed requests', async () => {
      await request(app.getHttpServer())
        .post('/chat')
        .send('invalid json')
        .expect(400);
    });

    it('should handle missing required fields', async () => {
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          session_id: 'test-session'
          // Missing message field
        })
        .expect(400);
    });
  });
});
