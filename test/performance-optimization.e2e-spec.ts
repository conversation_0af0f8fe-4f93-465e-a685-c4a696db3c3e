import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Performance Optimization System (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Recommendations Performance', () => {
    it('should respond within performance thresholds for simple queries', async () => {
      const startTime = Date.now();
      
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need an AI tool for data analysis',
          filters: {
            entityTypeIds: ['ai-tool'],
            max_candidates: 10,
          },
        })
        .expect(200);

      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(2000); // Should respond within 2 seconds
      expect(response.body.recommended_entities).toBeDefined();
      expect(response.body.candidates_analyzed).toBeGreaterThan(0);
    });

    it('should handle complex filter queries efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need a beginner-friendly AI tool with API access for Python machine learning projects under $50',
          filters: {
            entityTypeIds: ['ai-tool'],
            technical_levels: ['BEGINNER'],
            has_api: true,
            has_free_tier: true,
            frameworks: ['Python'],
            platforms: ['Web', 'Linux'],
            use_cases_search: 'machine learning',
            price_ranges: ['FREE', 'LOW'],
            max_candidates: 20,
          },
        })
        .expect(200);

      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(3000); // Complex queries within 3 seconds
      expect(response.body.recommended_entities).toBeDefined();
    });

    it('should demonstrate caching effectiveness with repeated queries', async () => {
      const query = {
        problem_description: 'I need an AI tool for content creation',
        filters: {
          entityTypeIds: ['ai-tool'],
          use_cases_search: 'content creation',
          max_candidates: 15,
        },
      };

      // First request (cache miss)
      const startTime1 = Date.now();
      await request(app.getHttpServer())
        .post('/recommendations')
        .send(query)
        .expect(200);
      const firstRequestTime = Date.now() - startTime1;

      // Second request (should hit cache)
      const startTime2 = Date.now();
      await request(app.getHttpServer())
        .post('/recommendations')
        .send(query)
        .expect(200);
      const secondRequestTime = Date.now() - startTime2;

      // Second request should be significantly faster due to caching
      expect(secondRequestTime).toBeLessThan(firstRequestTime * 0.8);
    });

    it('should handle concurrent requests efficiently', async () => {
      const concurrentRequests = 5;
      const requests = Array(concurrentRequests).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `I need an AI tool for project ${index}`,
            filters: {
              entityTypeIds: ['ai-tool'],
              max_candidates: 10,
            },
          })
      );

      const startTime = Date.now();
      const responses = await Promise.all(requests);
      const totalTime = Date.now() - startTime;

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.recommended_entities).toBeDefined();
      });

      // Concurrent processing should be efficient
      expect(totalTime).toBeLessThan(5000); // 5 concurrent requests within 5 seconds
    });
  });

  describe('Chat Performance', () => {
    it('should respond within performance thresholds for chat messages', async () => {
      const startTime = Date.now();
      
      const response = await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'I need an AI tool for my startup',
          session_id: undefined,
        })
        .expect(200);

      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(3000); // Chat within 3 seconds
      expect(response.body.message).toBeDefined();
      expect(response.body.session_id).toBeDefined();
    });

    it('should handle conversation context efficiently', async () => {
      const sessionId = `perf-test-${Date.now()}`;
      const messages = [
        'I need an AI tool',
        'For machine learning projects',
        'I am a beginner',
        'Budget is under $50',
        'Must have API access'
      ];

      const responseTimes: number[] = [];

      for (const message of messages) {
        const startTime = Date.now();
        
        await request(app.getHttpServer())
          .post('/chat')
          .send({
            message,
            session_id: sessionId,
          })
          .expect(200);

        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
      }

      // All responses should be within reasonable time
      responseTimes.forEach(time => {
        expect(time).toBeLessThan(4000);
      });

      // Later messages should not be significantly slower (good context management)
      const avgEarlyTime = (responseTimes[0] + responseTimes[1]) / 2;
      const avgLateTime = (responseTimes[3] + responseTimes[4]) / 2;
      expect(avgLateTime).toBeLessThan(avgEarlyTime * 1.5);
    });

    it('should demonstrate filter extraction caching', async () => {
      const message = 'I need a beginner-friendly AI tool with API access for Python projects';

      // First extraction (cache miss)
      const startTime1 = Date.now();
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message,
          session_id: undefined,
        })
        .expect(200);
      const firstTime = Date.now() - startTime1;

      // Second extraction with same message (should hit cache)
      const startTime2 = Date.now();
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message,
          session_id: undefined,
        })
        .expect(200);
      const secondTime = Date.now() - startTime2;

      // Second request should benefit from caching
      expect(secondTime).toBeLessThan(firstTime * 0.9);
    });
  });

  describe('Advanced Ranking Performance', () => {
    it('should rank entities efficiently with complex criteria', async () => {
      const startTime = Date.now();
      
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'I need the best AI tool for enterprise machine learning with comprehensive features',
          filters: {
            entityTypeIds: ['ai-tool'],
            technical_levels: ['ADVANCED', 'EXPERT'],
            has_api: true,
            platforms: ['Web', 'Linux', 'Windows'],
            frameworks: ['TensorFlow', 'PyTorch'],
            use_cases_search: 'machine learning',
            key_features_search: 'enterprise',
            price_ranges: ['MEDIUM', 'HIGH'],
            max_candidates: 25,
          },
        })
        .expect(200);

      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(4000); // Complex ranking within 4 seconds
      expect(response.body.recommended_entities).toBeDefined();
      expect(response.body.recommended_entities.length).toBeGreaterThan(0);
    });

    it('should maintain ranking quality under load', async () => {
      const requests = Array(3).fill(null).map(() =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: 'I need AI tools for different use cases',
            filters: {
              entityTypeIds: ['ai-tool'],
              max_candidates: 15,
            },
          })
      );

      const responses = await Promise.all(requests);

      // All requests should return ranked results
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.recommended_entities).toBeDefined();
        expect(response.body.recommended_entities.length).toBeGreaterThan(0);
      });
    });
  });

  describe('System Resource Management', () => {
    it('should handle memory efficiently during intensive operations', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Perform multiple intensive operations
      const intensiveRequests = Array(10).fill(null).map((_, index) =>
        request(app.getHttpServer())
          .post('/recommendations')
          .send({
            problem_description: `Complex query ${index} with multiple filters and ranking requirements`,
            filters: {
              entityTypeIds: ['ai-tool', 'course', 'job'],
              technical_levels: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'],
              has_api: true,
              has_free_tier: true,
              platforms: ['Web', 'Linux', 'Windows', 'macOS'],
              max_candidates: 20,
            },
          })
      );

      await Promise.all(intensiveRequests);

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024; // MB

      // Memory increase should be reasonable (less than 100MB for 10 requests)
      expect(memoryIncrease).toBeLessThan(100);
    });

    it('should demonstrate query optimization effectiveness', async () => {
      // Simple query (should be fast)
      const startTime1 = Date.now();
      await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'AI tool',
          filters: {
            entityTypeIds: ['ai-tool'],
            max_candidates: 5,
          },
        })
        .expect(200);
      const simpleQueryTime = Date.now() - startTime1;

      // Complex query (should still be reasonable)
      const startTime2 = Date.now();
      await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Advanced AI tool with comprehensive features',
          filters: {
            entityTypeIds: ['ai-tool'],
            technical_levels: ['ADVANCED'],
            has_api: true,
            has_free_tier: false,
            platforms: ['Web', 'Linux'],
            frameworks: ['TensorFlow'],
            use_cases_search: 'machine learning',
            price_ranges: ['MEDIUM'],
            max_candidates: 20,
          },
        })
        .expect(200);
      const complexQueryTime = Date.now() - startTime2;

      // Complex query should not be more than 3x slower than simple query
      expect(complexQueryTime).toBeLessThan(simpleQueryTime * 3);
    });
  });

  describe('Error Handling Performance', () => {
    it('should handle invalid requests efficiently', async () => {
      const startTime = Date.now();
      
      await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: '', // Invalid empty description
          filters: {
            max_candidates: 1000, // Invalid high number
          },
        })
        .expect(400);

      const responseTime = Date.now() - startTime;
      
      // Error handling should be fast
      expect(responseTime).toBeLessThan(500);
    });

    it('should recover gracefully from service failures', async () => {
      // This would test failover mechanisms in a real scenario
      const response = await request(app.getHttpServer())
        .post('/recommendations')
        .send({
          problem_description: 'Test resilience',
          filters: {
            entityTypeIds: ['ai-tool'],
            max_candidates: 10,
          },
        })
        .expect(200);

      expect(response.body.recommended_entities).toBeDefined();
    });
  });
});
