# AI Navigator Chat Service Documentation

## Overview

The AI Navigator Chat Service is a sophisticated conversational AI system designed to help users discover the perfect AI tools through natural language conversations. It combines vector search, LLM-powered responses, intelligent question generation, and comprehensive conversation management to provide personalized AI tool recommendations.

## Architecture Overview

```mermaid
graph TB
    A[Chat Controller] --> B[Chat Service]
    B --> C[Conversation Manager]
    B --> D[LLM Failover Service]
    B --> E[Question Suggestion Service]
    B --> F[Chat Cache Service]
    B --> G[Performance Monitor]
    B --> H[Error Handler]
    B --> I[Entities Service]
    
    C --> J[Conversation State Service]
    D --> K[LLM Factory Service]
    K --> L[OpenAI Service]
    K --> M[Anthropic Service]
    K --> N[Google Gemini Service]
    
    E --> O[Smart Question Generation]
    F --> P[LRU Caches]
    I --> Q[Vector Search]
    I --> R[Entity Database]
```

## Core Components

### 1. Chat Service (`ChatService`)

**Location**: `src/chat/services/chat.service.ts`

The main orchestrator that coordinates all chat functionality.

**Key Responsibilities**:
- Process incoming chat messages
- Coordinate conversation state management
- Integrate with LLM services for response generation
- Manage entity discovery through vector search
- Generate intelligent follow-up questions
- Handle error scenarios gracefully

**Main Methods**:
```typescript
async sendMessage(userId: string, sendChatMessageDto: SendChatMessageDto): Promise<ChatResponseDto>
async getConversationHistory(userId: string, sessionId: string, getHistoryDto: GetConversationHistoryDto): Promise<ConversationHistoryResponseDto>
async endConversation(userId: string, sessionId: string): Promise<void>
async getUserActiveSessions(userId: string): Promise<string[]>
```

### 2. Conversation Manager Service (`ConversationManagerService`)

**Location**: `src/chat/services/conversation-manager.service.ts`

Manages conversation state, context, and message history.

**Key Features**:
- Session initialization and management
- Message history tracking
- User preference management
- Conversation stage progression
- Entity discovery tracking

**Core Methods**:
```typescript
async getOrCreateConversation(userId: string, sessionId?: string): Promise<ConversationContext>
async addMessage(sessionId: string, message: Omit<ChatMessage, 'id' | 'timestamp'>, intent?: UserIntent): Promise<ConversationContext>
async updateUserPreferences(sessionId: string, preferences: Partial<UserPreferences>): Promise<ConversationContext>
async updateConversationStage(sessionId: string, stage: ConversationStage): Promise<ConversationContext>
```

### 3. LLM Failover Service (`LlmFailoverService`)

**Location**: `src/chat/services/llm-failover.service.ts`

Provides robust LLM integration with automatic failover between providers.

**Features**:
- Multi-provider support (OpenAI, Anthropic, Google Gemini)
- Automatic failover on provider failures
- Health monitoring and circuit breaker patterns
- Timeout handling
- Provider performance tracking

**Methods**:
```typescript
async getChatResponseWithFailover(userMessage: string, context: ConversationContext, candidateEntities?: CandidateEntity[]): Promise<ChatResponse>
async classifyIntentWithFailover(userMessage: string, context: ConversationContext): Promise<UserIntent>
```

### 4. Question Suggestion Service (`QuestionSuggestionService`)

**Location**: `src/chat/services/question-suggestion.service.ts`

Generates intelligent, context-aware follow-up questions to guide conversations.

**Enhanced Features**:
- **Context-aware question generation** based on user's actual message content
- **Anti-repetition logic** to avoid asking the same topics repeatedly
- **Progressive questioning** that adapts to conversation stage
- **Topic detection** for coding, industry, specifics, etc.
- **Conversation history analysis** to prevent redundant questions

**Key Methods**:
```typescript
generateFollowUpQuestions(context: ConversationContext, userMessage: string, maxQuestions: number = 2): string[]
```

**Smart Question Logic**:
```typescript
// Detects specific topics in user messages
if (userMessage.toLowerCase().includes('code') || userMessage.toLowerCase().includes('coding')) {
  // Generates coding-specific questions
  questions.push("What programming language are you primarily working with?");
  questions.push("Are you looking for tools to help with debugging, testing, or code generation?");
}
```

### 5. Chat Cache Service (`ChatCacheService`)

**Location**: `src/chat/services/chat-cache.service.ts`

Implements intelligent caching to improve performance and reduce LLM API calls.

**Cache Types**:
- **Intent Classification Cache**: Caches user intent analysis
- **Follow-up Questions Cache**: Caches generated questions
- **Entity Search Cache**: Caches vector search results
- **Response Cache**: Caches full chat responses for identical contexts

**Configuration**:
```typescript
private readonly config = {
  maxCacheSize: 1000,
  intentCacheTtl: 5 * 60 * 1000,      // 5 minutes
  followUpCacheTtl: 10 * 60 * 1000,   // 10 minutes
  entitySearchCacheTtl: 15 * 60 * 1000, // 15 minutes
  responseCacheTtl: 2 * 60 * 1000,    // 2 minutes
};
```

### 6. Performance Monitor Service (`ChatPerformanceMonitorService`)

**Location**: `src/chat/services/chat-performance-monitor.service.ts`

Tracks and monitors chat service performance metrics.

**Metrics Tracked**:
- Request/response times
- LLM provider performance
- Cache hit/miss rates
- Error rates and types
- Token usage statistics

### 7. Error Handler Service (`ChatErrorHandlerService`)

**Location**: `src/chat/services/chat-error-handler.service.ts`

Provides graceful error handling with intelligent fallback responses.

**Error Types Handled**:
- LLM service failures
- Rate limiting errors
- API key issues
- Network timeouts
- Conversation state errors
- Entity discovery failures

## Data Transfer Objects (DTOs)

### SendChatMessageDto
```typescript
{
  message: string;                    // User's message
  session_id?: string;               // Optional session ID
  user_preferences?: UserPreferencesDto; // User preferences
  context?: Record<string, any>;     // Additional context
}
```

### ChatResponseDto
```typescript
{
  message: string;                   // AI response message
  session_id: string;               // Session identifier
  conversation_stage: ConversationStage; // Current conversation stage
  suggested_actions?: SuggestedActionDto[]; // Suggested user actions
  discovered_entities?: DiscoveredEntityDto[]; // Found AI tools
  follow_up_questions?: string[];   // Smart follow-up questions
  should_transition_to_recommendations: boolean; // Transition flag
  metadata: ChatResponseMetadataDto; // Performance metadata
  generated_at: Date;               // Response timestamp
}
```

### ConversationHistoryResponseDto
```typescript
{
  session_id: string;               // Session identifier
  conversation_stage: ConversationStage; // Current stage
  messages: ChatMessageDto[];       // Message history
  total_messages: number;           // Total message count
  discovered_entities_count: number; // Entities found
  started_at: Date;                // Conversation start time
  last_active_at: Date;            // Last activity time
}
```

## Core Interfaces

### ConversationContext
```typescript
interface ConversationContext {
  sessionId: string;
  userId: string;
  messages: ChatMessage[];
  currentIntent?: UserIntent;
  discoveredEntities: string[];     // Entity IDs discovered
  userPreferences: {
    budget?: 'free' | 'low' | 'medium' | 'high';
    technical_level?: 'beginner' | 'intermediate' | 'advanced';
    preferred_categories?: string[];
    excluded_categories?: string[];
  };
  conversationStage: 'greeting' | 'discovery' | 'refinement' | 'recommendation' | 'comparison';
  metadata: {
    startedAt: Date;
    lastActiveAt: Date;
    totalMessages: number;
    entitiesShown: string[];
  };
}
```

### UserIntent
```typescript
interface UserIntent {
  type: 'discovery' | 'comparison' | 'specific_tool' | 'general_question' | 'refinement';
  confidence: number;
  entities?: string[];              // Mentioned entities
  categories?: string[];            // Categories of interest
  features?: string[];              // Specific features mentioned
  constraints?: {
    budget?: 'free' | 'low' | 'medium' | 'high';
    technical_level?: 'beginner' | 'intermediate' | 'advanced';
    use_case?: string;
  };
}
```

### ChatResponse
```typescript
interface ChatResponse {
  message: string;
  intent: UserIntent;
  suggestedActions?: Array<{
    type: 'ask_question' | 'show_entities' | 'refine_search' | 'get_recommendations';
    label: string;
    data?: any;
  }>;
  discoveredEntities?: Array<{
    id: string;
    name: string;
    relevanceScore: number;
    reason: string;
  }>;
  followUpQuestions?: string[];
  shouldTransitionToRecommendations: boolean;
  conversationStage: ConversationStage;
  metadata: {
    responseTime: number;
    llmProvider: string;
    tokensUsed?: number;
  };
}
```

## API Endpoints

### POST /chat
Send a message to the AI chatbot.

**Rate Limit**: 20 requests per minute
**Authentication**: Required (JWT Bearer token)

**Request Body**:
```json
{
  "message": "I need help finding an AI tool for automated code documentation",
  "session_id": "chat_123e4567-e89b-12d3-a456-426614174000",
  "user_preferences": {
    "budget": "medium",
    "technical_level": "intermediate",
    "preferred_categories": ["Developer Tools"]
  },
  "context": {
    "source": "web",
    "page": "dashboard"
  }
}
```

**Response**:
```json
{
  "message": "I'd be happy to help you find the perfect AI tool for code documentation! Based on what you've mentioned, there are several excellent options that could work well for your needs.",
  "session_id": "chat_123e4567-e89b-12d3-a456-426614174000",
  "conversation_stage": "discovery",
  "suggested_actions": [
    {
      "type": "ask_question",
      "label": "Tell me about your programming language",
      "data": null
    }
  ],
  "discovered_entities": [
    {
      "id": "entity_123",
      "name": "GitHub Copilot",
      "relevance_score": 0.95,
      "reason": "Excellent for code documentation and supports multiple languages"
    }
  ],
  "follow_up_questions": [
    "What programming language are you primarily working with?",
    "Are you looking for tools to help with debugging, testing, or code generation?"
  ],
  "should_transition_to_recommendations": false,
  "metadata": {
    "response_time": 1250,
    "llm_provider": "OPENAI",
    "tokens_used": 150
  },
  "generated_at": "2024-06-20T01:00:00.000Z"
}
```

### GET /chat/:sessionId/history
Get conversation history for a specific session.

**Rate Limit**: 30 requests per minute
**Authentication**: Required (JWT Bearer token)

**Query Parameters**:
- `limit` (optional): Maximum number of messages to return (default: 10, max: 50)
- `offset` (optional): Number of messages to skip (default: 0)

**Response**:
```json
{
  "session_id": "chat_123e4567-e89b-12d3-a456-426614174000",
  "conversation_stage": "discovery",
  "messages": [
    {
      "id": "msg_123",
      "role": "user",
      "content": "I need help finding an AI tool for code documentation",
      "timestamp": "2024-06-20T01:00:00.000Z",
      "metadata": {}
    },
    {
      "id": "msg_124",
      "role": "assistant",
      "content": "I'd be happy to help you find the perfect AI tool for code documentation!",
      "timestamp": "2024-06-20T01:00:05.000Z",
      "metadata": {
        "llm_provider": "OPENAI",
        "response_time": 1250
      }
    }
  ],
  "total_messages": 8,
  "discovered_entities_count": 5,
  "started_at": "2024-06-20T01:00:00.000Z",
  "last_active_at": "2024-06-20T01:15:00.000Z"
}
```

### DELETE /chat/:sessionId
End a conversation session.

**Rate Limit**: 10 requests per minute
**Authentication**: Required (JWT Bearer token)

**Response**: 204 No Content

### GET /chat/sessions
Get all active sessions for the authenticated user.

**Rate Limit**: 10 requests per minute
**Authentication**: Required (JWT Bearer token)

**Response**:
```json
{
  "sessions": [
    "chat_123e4567-e89b-12d3-a456-426614174000",
    "chat_456e7890-e12b-34c5-d678-901234567890"
  ]
}
```

### GET /chat/suggestions/:sessionId
Get smart follow-up question suggestions for a session.

**Rate Limit**: 30 requests per minute
**Authentication**: Required (JWT Bearer token)

**Response**:
```json
{
  "suggestions": [
    "What programming language are you primarily working with?",
    "Are you looking for tools to help with debugging, testing, or code generation?"
  ],
  "session_id": "chat_123e4567-e89b-12d3-a456-426614174000",
  "generated_at": "2024-06-20T01:00:00.000Z"
}
```

## Conversation Flow

### 1. Conversation Stages

The chat service manages conversations through distinct stages:

**Greeting** → **Discovery** → **Refinement** → **Recommendation** → **Comparison**

1. **Greeting**: Initial user interaction, understanding basic needs
2. **Discovery**: Exploring user requirements, preferences, and constraints
3. **Refinement**: Narrowing down specific needs and use cases
4. **Recommendation**: Providing specific AI tool recommendations
5. **Comparison**: Helping users compare and choose between options

### 2. Message Processing Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Chat Controller
    participant CS as Chat Service
    participant CM as Conversation Manager
    participant LF as LLM Failover
    participant ES as Entities Service
    participant QS as Question Suggestion

    U->>C: POST /chat {message}
    C->>CS: sendMessage()
    CS->>CM: getOrCreateConversation()
    CM-->>CS: ConversationContext

    CS->>ES: vectorSearch()
    ES-->>CS: candidateEntities[]

    CS->>LF: getChatResponseWithFailover()
    LF-->>CS: ChatResponse

    CS->>QS: generateFollowUpQuestions()
    QS-->>CS: questions[]

    CS->>CM: addMessage()
    CS->>CM: updateConversationStage()

    CS-->>C: ChatResponseDto
    C-->>U: JSON Response
```

### 3. Smart Question Generation Flow

The enhanced question generation system works as follows:

1. **Analyze User Message**: Extract keywords and topics from the user's actual message
2. **Check Conversation History**: Determine what topics have already been discussed
3. **Generate Context-Aware Questions**: Create questions based on:
   - What the user specifically mentioned (e.g., "coding", "help", etc.)
   - What hasn't been discussed yet
   - Current conversation stage
   - User preferences and constraints

4. **Anti-Repetition Logic**: Avoid asking about topics already covered:
   - Coding/programming topics
   - Industry/work context
   - Specific requirements
   - Budget and technical level

**Example Question Generation**:
```typescript
// User says: "help me find a tool to help me code"
// System detects: coding context + help request
// Generates:
[
  "What programming language are you primarily working with?",
  "Are you looking for tools to help with debugging, testing, or code generation?"
]

// User says: "I work in education and need AI tools"
// System detects: industry context + general need
// Generates:
[
  "What specific educational tasks are you looking to enhance with AI?",
  "Are you looking for tools for content creation, student assessment, or administrative tasks?"
]
```

## Error Handling

### Error Types and Responses

1. **LLM Service Errors**:
   - Rate limiting: Graceful fallback with retry suggestions
   - API key issues: Generic error without exposing details
   - Network timeouts: Fallback response with cached suggestions

2. **Conversation State Errors**:
   - Session not found: Create new session automatically
   - State corruption: Reset to safe state with user notification

3. **Entity Discovery Errors**:
   - Vector search failures: Fallback to category-based recommendations
   - Database connectivity: Use cached results when available

4. **Validation Errors**:
   - Empty messages: Clear error message
   - Invalid session IDs: Create new session
   - Malformed requests: Detailed validation errors

### Fallback Strategies

1. **LLM Failover**: Automatic switching between OpenAI → Anthropic → Google Gemini
2. **Cached Responses**: Use cached results for similar queries
3. **Generic Responses**: Predefined helpful responses for critical failures
4. **Graceful Degradation**: Reduce functionality rather than complete failure

## Performance Optimization

### Caching Strategy

1. **Multi-Level Caching**:
   - Intent classification (5 min TTL)
   - Follow-up questions (10 min TTL)
   - Entity search results (15 min TTL)
   - Full responses (2 min TTL)

2. **Cache Keys**:
   - Intent: `intent:${messageHash}:${contextHash}`
   - Questions: `questions:${contextHash}:${messageHash}`
   - Entities: `entities:${queryHash}`
   - Responses: `response:${fullContextHash}`

### Performance Monitoring

Real-time tracking of:
- Response times per component
- Cache hit/miss ratios
- LLM provider performance
- Error rates and types
- Token usage and costs

### Optimization Techniques

1. **Parallel Processing**: Simultaneous entity search and intent classification
2. **Smart Caching**: Context-aware cache invalidation
3. **Connection Pooling**: Efficient database connections
4. **Request Batching**: Group similar operations
5. **Lazy Loading**: Load conversation history on demand

## Configuration

### Environment Variables

```bash
# LLM Provider Configuration
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_GEMINI_API_KEY=your_gemini_key

# Chat Service Configuration
CHAT_DEFAULT_LLM_PROVIDER=OPENAI
CHAT_MAX_CONVERSATION_TTL=3600
CHAT_MAX_SESSIONS_PER_USER=10
CHAT_ENABLE_CACHING=true

# Performance Configuration
CHAT_CACHE_MAX_SIZE=1000
CHAT_RESPONSE_TIMEOUT=30000
CHAT_MAX_RETRIES=3
```

### Module Configuration

```typescript
@Module({
  imports: [
    ConfigModule,
    EntitiesModule,
    LlmModule,
    RecommendationsModule,
  ],
  controllers: [ChatController],
  providers: [
    {
      provide: 'IConversationStateService',
      useClass: MemoryConversationStateService,
    },
    ConversationManagerService,
    ChatErrorHandlerService,
    LlmFailoverService,
    QuestionSuggestionService,
    ChatPerformanceMonitorService,
    ChatCacheService,
    ChatService,
  ],
  exports: [
    'IConversationStateService',
    ConversationManagerService,
    ChatService,
  ],
})
export class ChatModule {}
```

## Testing

### Unit Tests

Key test files:
- `src/chat/services/chat.service.spec.ts`
- `src/chat/services/conversation-manager.service.spec.ts`
- `src/chat/services/question-suggestion.service.spec.ts`

### Integration Tests

Test scenarios:
- End-to-end conversation flows
- LLM provider failover
- Cache behavior
- Error handling
- Performance under load

### Testing Commands

```bash
# Run all chat service tests
npm test -- --testPathPattern=chat

# Run specific service tests
npm test -- chat.service.spec.ts

# Run with coverage
npm test -- --coverage --testPathPattern=chat
```

## Deployment Considerations

### Scaling

1. **Horizontal Scaling**: Multiple service instances with shared state
2. **Database Optimization**: Proper indexing for conversation queries
3. **Cache Distribution**: Redis for shared caching across instances
4. **Load Balancing**: Session affinity for conversation continuity

### Monitoring

1. **Health Checks**: Service availability and LLM provider status
2. **Metrics Collection**: Response times, error rates, cache performance
3. **Alerting**: Critical error thresholds and performance degradation
4. **Logging**: Structured logging with correlation IDs

### Security

1. **Authentication**: JWT token validation
2. **Rate Limiting**: Per-user and per-endpoint limits
3. **Input Validation**: Comprehensive DTO validation
4. **Data Privacy**: Conversation data encryption and retention policies

## Troubleshooting

### Common Issues

1. **Slow Response Times**:
   - Check LLM provider status
   - Verify cache hit rates
   - Monitor database query performance

2. **Repetitive Questions**:
   - Verify QuestionSuggestionService logic
   - Check conversation history tracking
   - Review anti-repetition algorithms

3. **LLM Failures**:
   - Check API key validity
   - Verify rate limit status
   - Review failover configuration

4. **Memory Issues**:
   - Monitor conversation state storage
   - Check cache size limits
   - Review cleanup intervals

### Debug Commands

```bash
# Check service health
curl -H "Authorization: Bearer $TOKEN" http://localhost:3000/healthz

# Get conversation metrics
curl -H "Authorization: Bearer $TOKEN" http://localhost:3000/chat/admin/metrics

# Clear cache
curl -X POST -H "Authorization: Bearer $TOKEN" http://localhost:3000/chat/admin/cache/clear
```

## Summary

This comprehensive documentation covers all aspects of the AI Navigator Chat Service, from architecture and implementation details to deployment and troubleshooting guidance. The service provides:

- **Intelligent Conversational AI** for AI tool discovery
- **Multi-Provider LLM Integration** with automatic failover
- **Smart Question Generation** with anti-repetition logic
- **Comprehensive Caching** for optimal performance
- **Robust Error Handling** with graceful degradation
- **Real-time Performance Monitoring** and metrics
- **Scalable Architecture** ready for production deployment

The enhanced QuestionSuggestionService now provides context-aware, non-repetitive questions that guide users through meaningful conversations to discover the perfect AI tools for their needs.
