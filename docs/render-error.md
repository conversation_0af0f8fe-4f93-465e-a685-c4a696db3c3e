prisma:error 
Invalid `prisma.entity.create()` invocation:
{
  data: {
    name: "<PERSON> AI",
    slug: "jasper-ai",
    websiteUrl: "https://www.jasper.ai/",
    shortDescription: "The AI Content Platform that helps your team create high-quality content 10x faster.",
    description: "Jasper is a generative AI platform designed for business teams to scale their content marketing. It provides features for brainstorming, writing, repurposing content, and maintaining a consistent brand voice across all marketing channels.",
    logoUrl: "https://assets-global.website-files.com/63359966422237835146b38c/63359966422237626946b397_Jasper%20logo.svg",
    documentationUrl: "https://support.jasper.ai/category/1-getting-started",
    contactUrl: "https://www.jasper.ai/contact-sales",
    privacyPolicyUrl: "https://www.jasper.ai/privacy",
    foundedYear: 2021,
    socialLinks: {
      twitter: "heyjasperai",
      linkedin: "company/jasper-ai",
      youtube: "@JasperAI"
    },
    metaTitle: "Jasper | The AI Content Platform for Business",
    metaDescription: "Create amazing, original content 10X faster with <PERSON>. Best for writing blog posts, social media content, and marketing copy.",
    employeeCountRange: undefined,
    fundingStage: undefined,
    locationSummary: "Austin, Texas",
    refLink: undefined,
    affiliateStatus: undefined,
    scrapedReviewSentimentLabel: undefined,
    scrapedReviewSentimentScore: undefined,
    scrapedReviewCount: undefined,
    entityType: {
      connect: {
        id: "fd181400-c9e6-431c-a8bd-c068d0491aba"
      }
    },
    submitter: {
      connect: {
        id: "758268a4-a99c-4413-a83f-34e5f70c848c"
      }
    },
    status: "PENDING",
    entityDetailsTool: {
      create: {
        keyFeatures: [
          "Brand Voice & Knowledge Base",
          "Jasper Chat",
          "Art Generation",
          "Team Collaboration Workflows",
          "Browser Extension",
          "Surfer SEO Integration"
        ],
        apiAccess: true,
        technicalLevel: "BEGINNER",
        platforms: [
          "Web",
          "Chrome Extension"
        ],
        apiDocumentationUrl: "https://www.jasper.ai/api",
        ~~~~~~~~~~~~~~~~~~~
        hasFreeTier: false,
        useCases: [
          "Writing blog articles and SEO content",
          "Generating marketing copy for ads and social media",
          "Creating art and images from text descriptions",
          "Repurposing existing content into different formats",
          "Maintaining a consistent brand voice across teams"
        ],
        integrations: [
          "Surfer SEO",
          "Google Docs",
          "Grammarly"
        ],
        pricingModel: "SUBSCRIPTION",
        priceRange: "MEDIUM",
        pricingDetails: "Creator plan starts at $39/user/month (billed annually). Pro and Business plans available with more features.",
        pricingUrl: "https://www.jasper.ai/pricing",
        supportEmail: "<EMAIL>",
        hasLiveChat: true,
        communityUrl: "https://www.facebook.com/groups/jasper.ai.official/",
?       learningCurve?: LearningCurve | Null,
?       targetAudience?: NullableJsonNullValueInput | Json,
?       customizationLevel?: String | Null,
?       demoAvailable?: Boolean | Null,
?       deploymentOptions?: NullableJsonNullValueInput | Json,
?       frameworks?: NullableJsonNullValueInput | Json,
?       libraries?: NullableJsonNullValueInput | Json,
?       mobileSupport?: Boolean | Null,
?       openSource?: Boolean | Null,
?       programmingLanguages?: NullableJsonNullValueInput | Json,
?       supportChannels?: NullableJsonNullValueInput | Json,
?       supportedOs?: NullableJsonNullValueInput | Json,
?       trialAvailable?: Boolean | Null,
?       hasApi?: Boolean | Null,
?       id?: String
      }
    },
    entityCategories: undefined,
    entityTags: undefined,
    entityFeatures: undefined
  },
  include: {
    entityType: true,
    submitter: {
      select: {
        id: true,
        authUserId: true,
        email: true,
        createdAt: true,
        lastLogin: true,
        username: true,
        displayName: true,
        profilePictureUrl: true
      }
    },
    entityCategories: {
      include: {
        category: true
      }
    },
    entityTags: {
      include: {
        tag: true
      }
    },
    entityFeatures: {
      include: {
        feature: true
      }
    },
    entityDetailsTool: true,
    entityDetailsCourse: true,
    entityDetailsAgency: true,
    entityDetailsContentCreator: true,
    entityDetailsCommunity: true,
    entityDetailsNewsletter: true,
    entityDetailsDataset: true,
    entityDetailsResearchPaper: true,
    entityDetailsSoftware: true,
    entityDetailsModel: true,
    entityDetailsProjectReference: true,
    entityDetailsServiceProvider: true,
    entityDetailsInvestor: true,
    entityDetailsEvent: true,
    entityDetailsJob: true,
    entityDetailsGrant: true,
    entityDetailsBounty: true,
    entityDetailsHardware: true,
    entityDetailsNews: true,
    entityDetailsBook: true,
    entityDetailsPodcast: true,
    entityDetailsPlatform: true
  }
}
Unknown argument `apiDocumentationUrl`. Available options are marked with ?.
{"timestamp":"2025-06-29T01:16:06.352Z","level":"ERROR","message":"--- GLOBAL EXCEPTION FILTER CAUGHT AN ERROR ---","correlationId":"unknown"}
{"timestamp":"2025-06-29T01:16:06.352Z","level":"ERROR","message":"Exception Type:","correlationId":"unknown","trace":"PrismaClientValidationError"}
{"timestamp":"2025-06-29T01:16:06.352Z","level":"ERROR","message":"Exception Message:","correlationId":"unknown","trace":"\nInvalid `prisma.entity.create()` invocation:\n\n{\n  data: {\n    name: \"Jasper AI\",\n    slug: \"jasper-ai\",\n    websiteUrl: \"https://www.jasper.ai/\",\n    shortDescription: \"The AI Content Platform that helps your team create high-quality content 10x faster.\",\n    description: \"Jasper is a generative AI platform designed for business teams to scale their content marketing. It provides features for brainstorming, writing, repurposing content, and maintaining a consistent brand voice across all marketing channels.\",\n    logoUrl: \"https://assets-global.website-files.com/63359966422237835146b38c/63359966422237626946b397_Jasper%20logo.svg\",\n    documentationUrl: \"https://support.jasper.ai/category/1-getting-started\",\n    contactUrl: \"https://www.jasper.ai/contact-sales\",\n    privacyPolicyUrl: \"https://www.jasper.ai/privacy\",\n    foundedYear: 2021,\n    socialLinks: {\n      twitter: \"heyjasperai\",\n      linkedin: \"company/jasper-ai\",\n      youtube: \"@JasperAI\"\n    },\n    metaTitle: \"Jasper | The AI Content Platform for Business\",\n    metaDescription: \"Create amazing, original content 10X faster with Jasper. Best for writing blog posts, social media content, and marketing copy.\",\n    employeeCountRange: undefined,\n    fundingStage: undefined,\n    locationSummary: \"Austin, Texas\",\n    refLink: undefined,\n    affiliateStatus: undefined,\n    scrapedReviewSentimentLabel: undefined,\n    scrapedReviewSentimentScore: undefined,\n    scrapedReviewCount: undefined,\n    entityType: {\n      connect: {\n        id: \"fd181400-c9e6-431c-a8bd-c068d0491aba\"\n      }\n    },\n    submitter: {\n      connect: {\n        id: \"758268a4-a99c-4413-a83f-34e5f70c848c\"\n      }\n    },\n    status: \"PENDING\",\n    entityDetailsTool: {\n      create: {\n        keyFeatures: [\n          \"Brand Voice & Knowledge Base\",\n          \"Jasper Chat\",\n          \"Art Generation\",\n          \"Team Collaboration Workflows\",\n          \"Browser Extension\",\n          \"Surfer SEO Integration\"\n        ],\n        apiAccess: true,\n        technicalLevel: \"BEGINNER\",\n        platforms: [\n          \"Web\",\n          \"Chrome Extension\"\n        ],\n        apiDocumentationUrl: \"https://www.jasper.ai/api\",\n        ~~~~~~~~~~~~~~~~~~~\n        hasFreeTier: false,\n        useCases: [\n          \"Writing blog articles and SEO content\",\n          \"Generating marketing copy for ads and social media\",\n          \"Creating art and images from text descriptions\",\n          \"Repurposing existing content into different formats\",\n          \"Maintaining a consistent brand voice across teams\"\n        ],\n        integrations: [\n          \"Surfer SEO\",\n          \"Google Docs\",\n          \"Grammarly\"\n        ],\n        pricingModel: \"SUBSCRIPTION\",\n        priceRange: \"MEDIUM\",\n        pricingDetails: \"Creator plan starts at $39/user/month (billed annually). Pro and Business plans available with more features.\",\n        pricingUrl: \"https://www.jasper.ai/pricing\",\n        supportEmail: \"<EMAIL>\",\n        hasLiveChat: true,\n        communityUrl: \"https://www.facebook.com/groups/jasper.ai.official/\",\n?       learningCurve?: LearningCurve | Null,\n?       targetAudience?: NullableJsonNullValueInput | Json,\n?       customizationLevel?: String | Null,\n?       demoAvailable?: Boolean | Null,\n?       deploymentOptions?: NullableJsonNullValueInput | Json,\n?       frameworks?: NullableJsonNullValueInput | Json,\n?       libraries?: NullableJsonNullValueInput | Json,\n?       mobileSupport?: Boolean | Null,\n?       openSource?: Boolean | Null,\n?       programmingLanguages?: NullableJsonNullValueInput | Json,\n?       supportChannels?: NullableJsonNullValueInput | Json,\n?       supportedOs?: NullableJsonNullValueInput | Json,\n?       trialAvailable?: Boolean | Null,\n?       hasApi?: Boolean | Null,\n?       id?: String\n      }\n    },\n    entityCategories: undefined,\n    entityTags: undefined,\n    entityFeatures: undefined\n  },\n  include: {\n    entityType: true,\n    submitter: {\n      select: {\n        id: true,\n        authUserId: true,\n        email: true,\n        createdAt: true,\n        lastLogin: true,\n        username: true,\n        displayName: true,\n        profilePictureUrl: true\n      }\n    },\n    entityCategories: {\n      include: {\n        category: true\n      }\n    },\n    entityTags: {\n      include: {\n        tag: true\n      }\n    },\n    entityFeatures: {\n      include: {\n        feature: true\n      }\n    },\n    entityDetailsTool: true,\n    entityDetailsCourse: true,\n    entityDetailsAgency: true,\n    entityDetailsContentCreator: true,\n    entityDetailsCommunity: true,\n    entityDetailsNewsletter: true,\n    entityDetailsDataset: true,\n    entityDetailsResearchPaper: true,\n    entityDetailsSoftware: true,\n    entityDetailsModel: true,\n    entityDetailsProjectReference: true,\n    entityDetailsServiceProvider: true,\n    entityDetailsInvestor: true,\n    entityDetailsEvent: true,\n    entityDetailsJob: true,\n    entityDetailsGrant: true,\n    entityDetailsBounty: true,\n    entityDetailsHardware: true,\n    entityDetailsNews: true,\n    entityDetailsBook: true,\n    entityDetailsPodcast: true,\n    entityDetailsPlatform: true\n  }\n}\n\nUnknown argument `apiDocumentationUrl`. Available options are marked with ?."}
{"timestamp":"2025-06-29T01:16:06.353Z","level":"ERROR","message":"Exception Details:","correlationId":"unknown","trace":"{\n  \"name\": \"PrismaClientValidationError\",\n  \"clientVersion\": \"6.10.1\"\n}"}
{"timestamp":"2025-06-29T01:16:06.353Z","level":"ERROR","message":"Exception Stack:","correlationId":"unknown","trace":"PrismaClientValidationError: \nInvalid `prisma.entity.create()` invocation:\n\n{\n  data: {\n    name: \"Jasper AI\",\n    slug: \"jasper-ai\",\n    websiteUrl: \"https://www.jasper.ai/\",\n    shortDescription: \"The AI Content Platform that helps your team create high-quality content 10x faster.\",\n    description: \"Jasper is a generative AI platform designed for business teams to scale their content marketing. It provides features for brainstorming, writing, repurposing content, and maintaining a consistent brand voice across all marketing channels.\",\n    logoUrl: \"https://assets-global.website-files.com/63359966422237835146b38c/63359966422237626946b397_Jasper%20logo.svg\",\n    documentationUrl: \"https://support.jasper.ai/category/1-getting-started\",\n    contactUrl: \"https://www.jasper.ai/contact-sales\",\n    privacyPolicyUrl: \"https://www.jasper.ai/privacy\",\n    foundedYear: 2021,\n    socialLinks: {\n      twitter: \"heyjasperai\",\n      linkedin: \"company/jasper-ai\",\n      youtube: \"@JasperAI\"\n    },\n    metaTitle: \"Jasper | The AI Content Platform for Business\",\n    metaDescription: \"Create amazing, original content 10X faster with Jasper. Best for writing blog posts, social media content, and marketing copy.\",\n    employeeCountRange: undefined,\n    fundingStage: undefined,\n    locationSummary: \"Austin, Texas\",\n    refLink: undefined,\n    affiliateStatus: undefined,\n    scrapedReviewSentimentLabel: undefined,\n    scrapedReviewSentimentScore: undefined,\n    scrapedReviewCount: undefined,\n    entityType: {\n      connect: {\n        id: \"fd181400-c9e6-431c-a8bd-c068d0491aba\"\n      }\n    },\n    submitter: {\n      connect: {\n        id: \"758268a4-a99c-4413-a83f-34e5f70c848c\"\n      }\n    },\n    status: \"PENDING\",\n    entityDetailsTool: {\n      create: {\n        keyFeatures: [\n          \"Brand Voice & Knowledge Base\",\n          \"Jasper Chat\",\n          \"Art Generation\",\n          \"Team Collaboration Workflows\",\n          \"Browser Extension\",\n          \"Surfer SEO Integration\"\n        ],\n        apiAccess: true,\n        technicalLevel: \"BEGINNER\",\n        platforms: [\n          \"Web\",\n          \"Chrome Extension\"\n        ],\n        apiDocumentationUrl: \"https://www.jasper.ai/api\",\n        ~~~~~~~~~~~~~~~~~~~\n        hasFreeTier: false,\n        useCases: [\n          \"Writing blog articles and SEO content\",\n          \"Generating marketing copy for ads and social media\",\n          \"Creating art and images from text descriptions\",\n          \"Repurposing existing content into different formats\",\n          \"Maintaining a consistent brand voice across teams\"\n        ],\n        integrations: [\n          \"Surfer SEO\",\n          \"Google Docs\",\n          \"Grammarly\"\n        ],\n        pricingModel: \"SUBSCRIPTION\",\n        priceRange: \"MEDIUM\",\n        pricingDetails: \"Creator plan starts at $39/user/month (billed annually). Pro and Business plans available with more features.\",\n        pricingUrl: \"https://www.jasper.ai/pricing\",\n        supportEmail: \"<EMAIL>\",\n        hasLiveChat: true,\n        communityUrl: \"https://www.facebook.com/groups/jasper.ai.official/\",\n?       learningCurve?: LearningCurve | Null,\n?       targetAudience?: NullableJsonNullValueInput | Json,\n?       customizationLevel?: String | Null,\n?       demoAvailable?: Boolean | Null,\n?       deploymentOptions?: NullableJsonNullValueInput | Json,\n?       frameworks?: NullableJsonNullValueInput | Json,\n?       libraries?: NullableJsonNullValueInput | Json,\n?       mobileSupport?: Boolean | Null,\n?       openSource?: Boolean | Null,\n?       programmingLanguages?: NullableJsonNullValueInput | Json,\n?       supportChannels?: NullableJsonNullValueInput | Json,\n?       supportedOs?: NullableJsonNullValueInput | Json,\n?       trialAvailable?: Boolean | Null,\n?       hasApi?: Boolean | Null,\n?       id?: String\n      }\n    },\n    entityCategories: undefined,\n    entityTags: undefined,\n    entityFeatures: undefined\n  },\n  include: {\n    entityType: true,\n    submitter: {\n      select: {\n        id: true,\n        authUserId: true,\n        email: true,\n        createdAt: true,\n        lastLogin: true,\n        username: true,\n        displayName: true,\n        profilePictureUrl: true\n      }\n    },\n    entityCategories: {\n      include: {\n        category: true\n      }\n    },\n    entityTags: {\n      include: {\n        tag: true\n      }\n    },\n    entityFeatures: {\n      include: {\n        feature: true\n      }\n    },\n    entityDetailsTool: true,\n    entityDetailsCourse: true,\n    entityDetailsAgency: true,\n    entityDetailsContentCreator: true,\n    entityDetailsCommunity: true,\n    entityDetailsNewsletter: true,\n    entityDetailsDataset: true,\n    entityDetailsResearchPaper: true,\n    entityDetailsSoftware: true,\n    entityDetailsModel: true,\n    entityDetailsProjectReference: true,\n    entityDetailsServiceProvider: true,\n    entityDetailsInvestor: true,\n    entityDetailsEvent: true,\n    entityDetailsJob: true,\n    entityDetailsGrant: true,\n    entityDetailsBounty: true,\n    entityDetailsHardware: true,\n    entityDetailsNews: true,\n    entityDetailsBook: true,\n    entityDetailsPodcast: true,\n    entityDetailsPlatform: true\n  }\n}\n\nUnknown argument `apiDocumentationUrl`. Available options are marked with ?.\n    at kn (/opt/render/project/src/generated/prisma/runtime/library.js:32:1363)\n    at Zn.handleRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:7102)\n    at Zn.handleAndLogRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:6784)\n    at Zn.request (/opt/render/project/src/generated/prisma/runtime/library.js:124:6491)\n    at async l (/opt/render/project/src/generated/prisma/runtime/library.js:133:9778)\n    at async newEntity.prisma.$transaction.maxWait (/opt/render/project/src/dist/entities/entities.service.js:653:33)\n    at async Proxy._transactionWithCallback (/opt/render/project/src/generated/prisma/runtime/library.js:133:8139)\n    at async EntitiesService.create (/opt/render/project/src/dist/entities/entities.service.js:612:27)\n    at async EntitiesController.create (/opt/render/project/src/dist/entities/entities.controller.js:665:24)\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:46:28"}
{"timestamp":"2025-06-29T01:16:06.353Z","level":"ERROR","message":"[GlobalExceptionFilter] Full exception object: PrismaClientValidationError: \nInvalid `prisma.entity.create()` invocation:\n\n{\n  data: {\n    name: \"Jasper AI\",\n    slug: \"jasper-ai\",\n    websiteUrl: \"https://www.jasper.ai/\",\n    shortDescription: \"The AI Content Platform that helps your team create high-quality content 10x faster.\",\n    description: \"Jasper is a generative AI platform designed for business teams to scale their content marketing. It provides features for brainstorming, writing, repurposing content, and maintaining a consistent brand voice across all marketing channels.\",\n    logoUrl: \"https://assets-global.website-files.com/63359966422237835146b38c/63359966422237626946b397_Jasper%20logo.svg\",\n    documentationUrl: \"https://support.jasper.ai/category/1-getting-started\",\n    contactUrl: \"https://www.jasper.ai/contact-sales\",\n    privacyPolicyUrl: \"https://www.jasper.ai/privacy\",\n    foundedYear: 2021,\n    socialLinks: {\n      twitter: \"heyjasperai\",\n      linkedin: \"company/jasper-ai\",\n      youtube: \"@JasperAI\"\n    },\n    metaTitle: \"Jasper | The AI Content Platform for Business\",\n    metaDescription: \"Create amazing, original content 10X faster with Jasper. Best for writing blog posts, social media content, and marketing copy.\",\n    employeeCountRange: undefined,\n    fundingStage: undefined,\n    locationSummary: \"Austin, Texas\",\n    refLink: undefined,\n    affiliateStatus: undefined,\n    scrapedReviewSentimentLabel: undefined,\n    scrapedReviewSentimentScore: undefined,\n    scrapedReviewCount: undefined,\n    entityType: {\n      connect: {\n        id: \"fd181400-c9e6-431c-a8bd-c068d0491aba\"\n      }\n    },\n    submitter: {\n      connect: {\n        id: \"758268a4-a99c-4413-a83f-34e5f70c848c\"\n      }\n    },\n    status: \"PENDING\",\n    entityDetailsTool: {\n      create: {\n        keyFeatures: [\n          \"Brand Voice & Knowledge Base\",\n          \"Jasper Chat\",\n          \"Art Generation\",\n          \"Team Collaboration Workflows\",\n          \"Browser Extension\",\n          \"Surfer SEO Integration\"\n        ],\n        apiAccess: true,\n        technicalLevel: \"BEGINNER\",\n        platforms: [\n          \"Web\",\n          \"Chrome Extension\"\n        ],\n        apiDocumentationUrl: \"https://www.jasper.ai/api\",\n        ~~~~~~~~~~~~~~~~~~~\n        hasFreeTier: false,\n        useCases: [\n          \"Writing blog articles and SEO content\",\n          \"Generating marketing copy for ads and social media\",\n          \"Creating art and images from text descriptions\",\n          \"Repurposing existing content into different formats\",\n          \"Maintaining a consistent brand voice across teams\"\n        ],\n        integrations: [\n          \"Surfer SEO\",\n          \"Google Docs\",\n          \"Grammarly\"\n        ],\n        pricingModel: \"SUBSCRIPTION\",\n        priceRange: \"MEDIUM\",\n        pricingDetails: \"Creator plan starts at $39/user/month (billed annually). Pro and Business plans available with more features.\",\n        pricingUrl: \"https://www.jasper.ai/pricing\",\n        supportEmail: \"<EMAIL>\",\n        hasLiveChat: true,\n        communityUrl: \"https://www.facebook.com/groups/jasper.ai.official/\",\n?       learningCurve?: LearningCurve | Null,\n?       targetAudience?: NullableJsonNullValueInput | Json,\n?       customizationLevel?: String | Null,\n?       demoAvailable?: Boolean | Null,\n?       deploymentOptions?: NullableJsonNullValueInput | Json,\n?       frameworks?: NullableJsonNullValueInput | Json,\n?       libraries?: NullableJsonNullValueInput | Json,\n?       mobileSupport?: Boolean | Null,\n?       openSource?: Boolean | Null,\n?       programmingLanguages?: NullableJsonNullValueInput | Json,\n?       supportChannels?: NullableJsonNullValueInput | Json,\n?       supportedOs?: NullableJsonNullValueInput | Json,\n?       trialAvailable?: Boolean | Null,\n?       hasApi?: Boolean | Null,\n?       id?: String\n      }\n    },\n    entityCategories: undefined,\n    entityTags: undefined,\n    entityFeatures: undefined\n  },\n  include: {\n    entityType: true,\n    submitter: {\n      select: {\n        id: true,\n        authUserId: true,\n        email: true,\n        createdAt: true,\n        lastLogin: true,\n        username: true,\n        displayName: true,\n        profilePictureUrl: true\n      }\n    },\n    entityCategories: {\n      include: {\n        category: true\n      }\n    },\n    entityTags: {\n      include: {\n        tag: true\n      }\n    },\n    entityFeatures: {\n      include: {\n        feature: true\n      }\n    },\n    entityDetailsTool: true,\n    entityDetailsCourse: true,\n    entityDetailsAgency: true,\n    entityDetailsContentCreator: true,\n    entityDetailsCommunity: true,\n    entityDetailsNewsletter: true,\n    entityDetailsDataset: true,\n    entityDetailsResearchPaper: true,\n    entityDetailsSoftware: true,\n    entityDetailsModel: true,\n    entityDetailsProjectReference: true,\n    entityDetailsServiceProvider: true,\n    entityDetailsInvestor: true,\n    entityDetailsEvent: true,\n    entityDetailsJob: true,\n    entityDetailsGrant: true,\n    entityDetailsBounty: true,\n    entityDetailsHardware: true,\n    entityDetailsNews: true,\n    entityDetailsBook: true,\n    entityDetailsPodcast: true,\n    entityDetailsPlatform: true\n  }\n}\n\nUnknown argument `apiDocumentationUrl`. Available options are marked with ?.","correlationId":"unknown"}
{"timestamp":"2025-06-29T01:16:06.354Z","level":"INFO","message":"Request completed: POST /entities","correlationId":"93282c32-1389-4a6f-9433-41ea187c160c","ip":"::ffff:**************","userAgent":"PostmanRuntime/7.44.1","method":"POST","url":"/entities","statusCode":500,"responseTime":286,"type":"response"}
prisma:query ROLLBACK
{"timestamp":"2025-06-29T01:16:06.353Z","level":"ERROR","message":"Unexpected exception caught:","correlationId":"unknown"}
{"timestamp":"2025-06-29T01:16:06.353Z","level":"ERROR","message":"CorrelationId: 93282c32-1389-4a6f-9433-41ea187c160c, ExceptionType: PrismaClientValidationError, Message: \nInvalid `prisma.entity.create()` invocation:\n\n{\n  data: {\n    name: \"Jasper AI\",\n    slug: \"jasper-ai\",\n    websiteUrl: \"https://www.jasper.ai/\",\n    shortDescription: \"The AI Content Platform that helps your team create high-quality content 10x faster.\",\n    description: \"Jasper is a generative AI platform designed for business teams to scale their content marketing. It provides features for brainstorming, writing, repurposing content, and maintaining a consistent brand voice across all marketing channels.\",\n    logoUrl: \"https://assets-global.website-files.com/63359966422237835146b38c/63359966422237626946b397_Jasper%20logo.svg\",\n    documentationUrl: \"https://support.jasper.ai/category/1-getting-started\",\n    contactUrl: \"https://www.jasper.ai/contact-sales\",\n    privacyPolicyUrl: \"https://www.jasper.ai/privacy\",\n    foundedYear: 2021,\n    socialLinks: {\n      twitter: \"heyjasperai\",\n      linkedin: \"company/jasper-ai\",\n      youtube: \"@JasperAI\"\n    },\n    metaTitle: \"Jasper | The AI Content Platform for Business\",\n    metaDescription: \"Create amazing, original content 10X faster with Jasper. Best for writing blog posts, social media content, and marketing copy.\",\n    employeeCountRange: undefined,\n    fundingStage: undefined,\n    locationSummary: \"Austin, Texas\",\n    refLink: undefined,\n    affiliateStatus: undefined,\n    scrapedReviewSentimentLabel: undefined,\n    scrapedReviewSentimentScore: undefined,\n    scrapedReviewCount: undefined,\n    entityType: {\n      connect: {\n        id: \"fd181400-c9e6-431c-a8bd-c068d0491aba\"\n      }\n    },\n    submitter: {\n      connect: {\n        id: \"758268a4-a99c-4413-a83f-34e5f70c848c\"\n      }\n    },\n    status: \"PENDING\",\n    entityDetailsTool: {\n      create: {\n        keyFeatures: [\n          \"Brand Voice & Knowledge Base\",\n          \"Jasper Chat\",\n          \"Art Generation\",\n          \"Team Collaboration Workflows\",\n          \"Browser Extension\",\n          \"Surfer SEO Integration\"\n        ],\n        apiAccess: true,\n        technicalLevel: \"BEGINNER\",\n        platforms: [\n          \"Web\",\n          \"Chrome Extension\"\n        ],\n        apiDocumentationUrl: \"https://www.jasper.ai/api\",\n        ~~~~~~~~~~~~~~~~~~~\n        hasFreeTier: false,\n        useCases: [\n          \"Writing blog articles and SEO content\",\n          \"Generating marketing copy for ads and social media\",\n          \"Creating art and images from text descriptions\",\n          \"Repurposing existing content into different formats\",\n          \"Maintaining a consistent brand voice across teams\"\n        ],\n        integrations: [\n          \"Surfer SEO\",\n          \"Google Docs\",\n          \"Grammarly\"\n        ],\n        pricingModel: \"SUBSCRIPTION\",\n        priceRange: \"MEDIUM\",\n        pricingDetails: \"Creator plan starts at $39/user/month (billed annually). Pro and Business plans available with more features.\",\n        pricingUrl: \"https://www.jasper.ai/pricing\",\n        supportEmail: \"<EMAIL>\",\n        hasLiveChat: true,\n        communityUrl: \"https://www.facebook.com/groups/jasper.ai.official/\",\n?       learningCurve?: LearningCurve | Null,\n?       targetAudience?: NullableJsonNullValueInput | Json,\n?       customizationLevel?: String | Null,\n?       demoAvailable?: Boolean | Null,\n?       deploymentOptions?: NullableJsonNullValueInput | Json,\n?       frameworks?: NullableJsonNullValueInput | Json,\n?       libraries?: NullableJsonNullValueInput | Json,\n?       mobileSupport?: Boolean | Null,\n?       openSource?: Boolean | Null,\n?       programmingLanguages?: NullableJsonNullValueInput | Json,\n?       supportChannels?: NullableJsonNullValueInput | Json,\n?       supportedOs?: NullableJsonNullValueInput | Json,\n?       trialAvailable?: Boolean | Null,\n?       hasApi?: Boolean | Null,\n?       id?: String\n      }\n    },\n    entityCategories: undefined,\n    entityTags: undefined,\n    entityFeatures: undefined\n  },\n  include: {\n    entityType: true,\n    submitter: {\n      select: {\n        id: true,\n        authUserId: true,\n        email: true,\n        createdAt: true,\n        lastLogin: true,\n        username: true,\n        displayName: true,\n        profilePictureUrl: true\n      }\n    },\n    entityCategories: {\n      include: {\n        category: true\n      }\n    },\n    entityTags: {\n      include: {\n        tag: true\n      }\n    },\n    entityFeatures: {\n      include: {\n        feature: true\n      }\n    },\n    entityDetailsTool: true,\n    entityDetailsCourse: true,\n    entityDetailsAgency: true,\n    entityDetailsContentCreator: true,\n    entityDetailsCommunity: true,\n    entityDetailsNewsletter: true,\n    entityDetailsDataset: true,\n    entityDetailsResearchPaper: true,\n    entityDetailsSoftware: true,\n    entityDetailsModel: true,\n    entityDetailsProjectReference: true,\n    entityDetailsServiceProvider: true,\n    entityDetailsInvestor: true,\n    entityDetailsEvent: true,\n    entityDetailsJob: true,\n    entityDetailsGrant: true,\n    entityDetailsBounty: true,\n    entityDetailsHardware: true,\n    entityDetailsNews: true,\n    entityDetailsBook: true,\n    entityDetailsPodcast: true,\n    entityDetailsPlatform: true\n  }\n}\n\nUnknown argument `apiDocumentationUrl`. Available options are marked with ?.","correlationId":"unknown"}
{"timestamp":"2025-06-29T01:16:06.353Z","level":"ERROR","message":"Stack: PrismaClientValidationError: \nInvalid `prisma.entity.create()` invocation:\n\n{\n  data: {\n    name: \"Jasper AI\",\n    slug: \"jasper-ai\",\n    websiteUrl: \"https://www.jasper.ai/\",\n    shortDescription: \"The AI Content Platform that helps your team create high-quality content 10x faster.\",\n    description: \"Jasper is a generative AI platform designed for business teams to scale their content marketing. It provides features for brainstorming, writing, repurposing content, and maintaining a consistent brand voice across all marketing channels.\",\n    logoUrl: \"https://assets-global.website-files.com/63359966422237835146b38c/63359966422237626946b397_Jasper%20logo.svg\",\n    documentationUrl: \"https://support.jasper.ai/category/1-getting-started\",\n    contactUrl: \"https://www.jasper.ai/contact-sales\",\n    privacyPolicyUrl: \"https://www.jasper.ai/privacy\",\n    foundedYear: 2021,\n    socialLinks: {\n      twitter: \"heyjasperai\",\n      linkedin: \"company/jasper-ai\",\n      youtube: \"@JasperAI\"\n    },\n    metaTitle: \"Jasper | The AI Content Platform for Business\",\n    metaDescription: \"Create amazing, original content 10X faster with Jasper. Best for writing blog posts, social media content, and marketing copy.\",\n    employeeCountRange: undefined,\n    fundingStage: undefined,\n    locationSummary: \"Austin, Texas\",\n    refLink: undefined,\n    affiliateStatus: undefined,\n    scrapedReviewSentimentLabel: undefined,\n    scrapedReviewSentimentScore: undefined,\n    scrapedReviewCount: undefined,\n    entityType: {\n      connect: {\n        id: \"fd181400-c9e6-431c-a8bd-c068d0491aba\"\n      }\n    },\n    submitter: {\n      connect: {\n        id: \"758268a4-a99c-4413-a83f-34e5f70c848c\"\n      }\n    },\n    status: \"PENDING\",\n    entityDetailsTool: {\n      create: {\n        keyFeatures: [\n          \"Brand Voice & Knowledge Base\",\n          \"Jasper Chat\",\n          \"Art Generation\",\n          \"Team Collaboration Workflows\",\n          \"Browser Extension\",\n          \"Surfer SEO Integration\"\n        ],\n        apiAccess: true,\n        technicalLevel: \"BEGINNER\",\n        platforms: [\n          \"Web\",\n          \"Chrome Extension\"\n        ],\n        apiDocumentationUrl: \"https://www.jasper.ai/api\",\n        ~~~~~~~~~~~~~~~~~~~\n        hasFreeTier: false,\n        useCases: [\n          \"Writing blog articles and SEO content\",\n          \"Generating marketing copy for ads and social media\",\n          \"Creating art and images from text descriptions\",\n          \"Repurposing existing content into different formats\",\n          \"Maintaining a consistent brand voice across teams\"\n        ],\n        integrations: [\n          \"Surfer SEO\",\n          \"Google Docs\",\n          \"Grammarly\"\n        ],\n        pricingModel: \"SUBSCRIPTION\",\n        priceRange: \"MEDIUM\",\n        pricingDetails: \"Creator plan starts at $39/user/month (billed annually). Pro and Business plans available with more features.\",\n        pricingUrl: \"https://www.jasper.ai/pricing\",\n        supportEmail: \"<EMAIL>\",\n        hasLiveChat: true,\n        communityUrl: \"https://www.facebook.com/groups/jasper.ai.official/\",\n?       learningCurve?: LearningCurve | Null,\n?       targetAudience?: NullableJsonNullValueInput | Json,\n?       customizationLevel?: String | Null,\n?       demoAvailable?: Boolean | Null,\n?       deploymentOptions?: NullableJsonNullValueInput | Json,\n?       frameworks?: NullableJsonNullValueInput | Json,\n?       libraries?: NullableJsonNullValueInput | Json,\n?       mobileSupport?: Boolean | Null,\n?       openSource?: Boolean | Null,\n?       programmingLanguages?: NullableJsonNullValueInput | Json,\n?       supportChannels?: NullableJsonNullValueInput | Json,\n?       supportedOs?: NullableJsonNullValueInput | Json,\n?       trialAvailable?: Boolean | Null,\n?       hasApi?: Boolean | Null,\n?       id?: String\n      }\n    },\n    entityCategories: undefined,\n    entityTags: undefined,\n    entityFeatures: undefined\n  },\n  include: {\n    entityType: true,\n    submitter: {\n      select: {\n        id: true,\n        authUserId: true,\n        email: true,\n        createdAt: true,\n        lastLogin: true,\n        username: true,\n        displayName: true,\n        profilePictureUrl: true\n      }\n    },\n    entityCategories: {\n      include: {\n        category: true\n      }\n    },\n    entityTags: {\n      include: {\n        tag: true\n      }\n    },\n    entityFeatures: {\n      include: {\n        feature: true\n      }\n    },\n    entityDetailsTool: true,\n    entityDetailsCourse: true,\n    entityDetailsAgency: true,\n    entityDetailsContentCreator: true,\n    entityDetailsCommunity: true,\n    entityDetailsNewsletter: true,\n    entityDetailsDataset: true,\n    entityDetailsResearchPaper: true,\n    entityDetailsSoftware: true,\n    entityDetailsModel: true,\n    entityDetailsProjectReference: true,\n    entityDetailsServiceProvider: true,\n    entityDetailsInvestor: true,\n    entityDetailsEvent: true,\n    entityDetailsJob: true,\n    entityDetailsGrant: true,\n    entityDetailsBounty: true,\n    entityDetailsHardware: true,\n    entityDetailsNews: true,\n    entityDetailsBook: true,\n    entityDetailsPodcast: true,\n    entityDetailsPlatform: true\n  }\n}\n\nUnknown argument `apiDocumentationUrl`. Available options are marked with ?.\n    at kn (/opt/render/project/src/generated/prisma/runtime/library.js:32:1363)\n    at Zn.handleRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:7102)\n    at Zn.handleAndLogRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:6784)\n    at Zn.request (/opt/render/project/src/generated/prisma/runtime/library.js:124:6491)\n    at async l (/opt/render/project/src/generated/prisma/runtime/library.js:133:9778)\n    at async newEntity.prisma.$transaction.maxWait (/opt/render/project/src/dist/entities/entities.service.js:653:33)\n    at async Proxy._transactionWithCallback (/opt/render/project/src/generated/prisma/runtime/library.js:133:8139)\n    at async EntitiesService.create (/opt/render/project/src/dist/entities/entities.service.js:612:27)\n    at async EntitiesController.create (/opt/render/project/src/dist/entities/entities.controller.js:665:24)\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:46:28","correlationId":"unknown"}
{"timestamp":"2025-06-29T01:16:06.353Z","level":"ERROR","message":"OriginalException: {\"name\":\"PrismaClientValidationError\",\"clientVersion\":\"6.10.1\"}","correlationId":"unknown"}