# 🚀 Frontend Integration Guide

This guide provides comprehensive examples and best practices for integrating the enhanced AI Nav Backend APIs into your frontend application. It covers React, Vue, and vanilla JavaScript implementations with TypeScript support.

## 📋 Table of Contents
- [Quick Start](#quick-start)
- [React Integration](#react-integration)
- [Vue Integration](#vue-integration)
- [TypeScript Definitions](#typescript-definitions)
- [State Management](#state-management)
- [Performance Optimization](#performance-optimization)
- [Error Handling](#error-handling)
- [Best Practices](#best-practices)

## 🚀 Quick Start

### Installation
```bash
# Install required dependencies
npm install axios react-query @tanstack/react-query
# or
yarn add axios react-query @tanstack/react-query
```

### Basic API Client
```typescript
// lib/api-client.ts
import axios from 'axios';

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle authentication error
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
```

## ⚛️ React Integration

### Custom Hooks for Recommendations

```typescript
// hooks/useRecommendations.ts
import { useState, useCallback } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import apiClient from '../lib/api-client';
import type { RecommendationRequest, RecommendationResponse } from '../types/api';

export const useRecommendations = () => {
  const [lastRequest, setLastRequest] = useState<RecommendationRequest | null>(null);

  const mutation = useMutation({
    mutationFn: async (request: RecommendationRequest): Promise<RecommendationResponse> => {
      const response = await apiClient.post('/recommendations', request);
      return response.data;
    },
    onSuccess: (data, variables) => {
      setLastRequest(variables);
    },
  });

  const getRecommendations = useCallback((request: RecommendationRequest) => {
    mutation.mutate(request);
  }, [mutation]);

  return {
    getRecommendations,
    data: mutation.data,
    loading: mutation.isPending,
    error: mutation.error,
    lastRequest,
    reset: mutation.reset,
  };
};

// Usage in component
const RecommendationsPage = () => {
  const { getRecommendations, data, loading, error } = useRecommendations();

  const handleSearch = (description: string, filters: any) => {
    getRecommendations({
      problem_description: description,
      filters: {
        ...filters,
        max_candidates: 20,
      },
    });
  };

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  return (
    <div>
      <SearchForm onSearch={handleSearch} />
      {data && <RecommendationsList entities={data.recommended_entities} />}
    </div>
  );
};
```

### Advanced Search Component

```typescript
// components/AdvancedSearch.tsx
import React, { useState } from 'react';
import { useRecommendations } from '../hooks/useRecommendations';
import type { EntityType, TechnicalLevel, PriceRange } from '../types/api';

interface AdvancedSearchProps {
  onResults?: (results: any) => void;
}

export const AdvancedSearch: React.FC<AdvancedSearchProps> = ({ onResults }) => {
  const [description, setDescription] = useState('');
  const [entityTypes, setEntityTypes] = useState<EntityType[]>([]);
  const [technicalLevels, setTechnicalLevels] = useState<TechnicalLevel[]>([]);
  const [hasApi, setHasApi] = useState<boolean | undefined>();
  const [hasFreeTier, setHasFreeTier] = useState<boolean | undefined>();
  const [platforms, setPlatforms] = useState<string[]>([]);
  const [priceRanges, setPriceRanges] = useState<PriceRange[]>([]);

  const { getRecommendations, data, loading, error } = useRecommendations();

  React.useEffect(() => {
    if (data && onResults) {
      onResults(data);
    }
  }, [data, onResults]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    getRecommendations({
      problem_description: description,
      filters: {
        entityTypeIds: entityTypes,
        technical_levels: technicalLevels.length > 0 ? technicalLevels : undefined,
        has_api: hasApi,
        has_free_tier: hasFreeTier,
        platforms: platforms.length > 0 ? platforms : undefined,
        price_ranges: priceRanges.length > 0 ? priceRanges : undefined,
        max_candidates: 25,
      },
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Description Input */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Describe what you're looking for
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="I need an AI tool for machine learning projects..."
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
          rows={3}
          required
        />
      </div>

      {/* Entity Types */}
      <div>
        <label className="block text-sm font-medium text-gray-700">Entity Types</label>
        <div className="mt-2 space-y-2">
          {['ai-tool', 'course', 'job', 'event', 'hardware'].map((type) => (
            <label key={type} className="inline-flex items-center mr-4">
              <input
                type="checkbox"
                checked={entityTypes.includes(type as EntityType)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setEntityTypes([...entityTypes, type as EntityType]);
                  } else {
                    setEntityTypes(entityTypes.filter(t => t !== type));
                  }
                }}
                className="rounded border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700 capitalize">
                {type.replace('-', ' ')}
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Technical Levels */}
      <div>
        <label className="block text-sm font-medium text-gray-700">Technical Level</label>
        <select
          multiple
          value={technicalLevels}
          onChange={(e) => {
            const values = Array.from(e.target.selectedOptions, option => option.value);
            setTechnicalLevels(values as TechnicalLevel[]);
          }}
          className="mt-1 block w-full rounded-md border-gray-300"
        >
          <option value="BEGINNER">Beginner</option>
          <option value="INTERMEDIATE">Intermediate</option>
          <option value="ADVANCED">Advanced</option>
          <option value="EXPERT">Expert</option>
        </select>
      </div>

      {/* Boolean Filters */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">API Access</label>
          <select
            value={hasApi === undefined ? '' : hasApi.toString()}
            onChange={(e) => {
              const value = e.target.value;
              setHasApi(value === '' ? undefined : value === 'true');
            }}
            className="mt-1 block w-full rounded-md border-gray-300"
          >
            <option value="">Any</option>
            <option value="true">Required</option>
            <option value="false">Not Required</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Free Tier</label>
          <select
            value={hasFreeTier === undefined ? '' : hasFreeTier.toString()}
            onChange={(e) => {
              const value = e.target.value;
              setHasFreeTier(value === '' ? undefined : value === 'true');
            }}
            className="mt-1 block w-full rounded-md border-gray-300"
          >
            <option value="">Any</option>
            <option value="true">Required</option>
            <option value="false">Not Required</option>
          </select>
        </div>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={loading || !description.trim()}
        className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
      >
        {loading ? 'Searching...' : 'Find Recommendations'}
      </button>

      {/* Error Display */}
      {error && (
        <div className="text-red-600 text-sm">
          Error: {error.message}
        </div>
      )}
    </form>
  );
};
```

### Chat Interface Component

```typescript
// components/ChatInterface.tsx
import React, { useState, useRef, useEffect } from 'react';
import { useChat } from '../hooks/useChat';
import type { ChatMessage } from '../types/api';

export const ChatInterface: React.FC = () => {
  const [input, setInput] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { sendMessage, messages, loading, sessionId, discoveredEntities } = useChat();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || loading) return;

    const message = input.trim();
    setInput('');
    await sendMessage(message);
  };

  return (
    <div className="flex flex-col h-96 border rounded-lg">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.role === 'user'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-800'
              }`}
            >
              {message.content}
            </div>
          </div>
        ))}
        
        {/* Discovered Entities */}
        {discoveredEntities.length > 0 && (
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-medium text-green-800 mb-2">
              Found {discoveredEntities.length} recommendations:
            </h4>
            <div className="space-y-2">
              {discoveredEntities.slice(0, 3).map((entity) => (
                <div key={entity.id} className="text-sm">
                  <span className="font-medium">{entity.name}</span>
                  <span className="text-gray-600 ml-2">
                    ({entity.relevanceScore?.toFixed(2)} relevance)
                  </span>
                </div>
              ))}
              {discoveredEntities.length > 3 && (
                <div className="text-sm text-gray-600">
                  +{discoveredEntities.length - 3} more...
                </div>
              )}
            </div>
          </div>
        )}
        
        {loading && (
          <div className="flex justify-start">
            <div className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <form onSubmit={handleSubmit} className="border-t p-4">
        <div className="flex space-x-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask me about AI tools, courses, jobs..."
            className="flex-1 rounded-md border-gray-300 shadow-sm"
            disabled={loading}
          />
          <button
            type="submit"
            disabled={loading || !input.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
          >
            Send
          </button>
        </div>
      </form>
    </div>
  );
};
```

### Custom Hook for Chat

```typescript
// hooks/useChat.ts
import { useState, useCallback } from 'react';
import apiClient from '../lib/api-client';
import type { ChatRequest, ChatResponse, ChatMessage, DiscoveredEntity } from '../types/api';

export const useChat = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [discoveredEntities, setDiscoveredEntities] = useState<DiscoveredEntity[]>([]);

  const sendMessage = useCallback(async (message: string) => {
    setLoading(true);
    setError(null);

    // Add user message immediately
    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);

    try {
      const request: ChatRequest = {
        message,
        session_id: sessionId,
      };

      const response = await apiClient.post<ChatResponse>('/chat', request);
      const data = response.data;

      // Update session ID
      if (!sessionId && data.session_id) {
        setSessionId(data.session_id);
      }

      // Add AI response
      const aiMessage: ChatMessage = {
        role: 'assistant',
        content: data.message,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiMessage]);

      // Update discovered entities
      if (data.discovered_entities?.length > 0) {
        setDiscoveredEntities(data.discovered_entities);
      }

    } catch (err) {
      setError(err as Error);
      
      // Add error message
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  }, [sessionId]);

  const resetChat = useCallback(() => {
    setMessages([]);
    setSessionId(null);
    setDiscoveredEntities([]);
    setError(null);
  }, []);

  return {
    messages,
    sessionId,
    loading,
    error,
    discoveredEntities,
    sendMessage,
    resetChat,
  };
};
```

## 🎨 Vue Integration

### Vue Composition API

```typescript
// composables/useRecommendations.ts
import { ref, reactive } from 'vue';
import apiClient from '../lib/api-client';
import type { RecommendationRequest, RecommendationResponse } from '../types/api';

export const useRecommendations = () => {
  const data = ref<RecommendationResponse | null>(null);
  const loading = ref(false);
  const error = ref<Error | null>(null);

  const getRecommendations = async (request: RecommendationRequest) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await apiClient.post<RecommendationResponse>('/recommendations', request);
      data.value = response.data;
    } catch (err) {
      error.value = err as Error;
    } finally {
      loading.value = false;
    }
  };

  const reset = () => {
    data.value = null;
    error.value = null;
  };

  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    getRecommendations,
    reset,
  };
};
```

### Vue Component

```vue
<!-- components/RecommendationSearch.vue -->
<template>
  <div class="recommendation-search">
    <form @submit.prevent="handleSubmit" class="space-y-4">
      <div>
        <label for="description">Describe what you're looking for:</label>
        <textarea
          id="description"
          v-model="form.description"
          placeholder="I need an AI tool for..."
          required
          class="w-full p-2 border rounded"
        />
      </div>

      <div>
        <label>Entity Types:</label>
        <div class="flex flex-wrap gap-2 mt-2">
          <label v-for="type in entityTypes" :key="type" class="flex items-center">
            <input
              type="checkbox"
              :value="type"
              v-model="form.entityTypes"
              class="mr-1"
            />
            {{ type.replace('-', ' ') }}
          </label>
        </div>
      </div>

      <button
        type="submit"
        :disabled="loading || !form.description.trim()"
        class="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
      >
        {{ loading ? 'Searching...' : 'Search' }}
      </button>
    </form>

    <div v-if="error" class="text-red-600 mt-4">
      Error: {{ error.message }}
    </div>

    <div v-if="data" class="mt-6">
      <h3 class="text-lg font-semibold mb-4">
        Found {{ data.recommended_entities.length }} recommendations
      </h3>
      <div class="grid gap-4">
        <div
          v-for="entity in data.recommended_entities"
          :key="entity.id"
          class="border p-4 rounded-lg"
        >
          <h4 class="font-medium">{{ entity.name }}</h4>
          <p class="text-gray-600 text-sm">{{ entity.shortDescription }}</p>
          <div class="flex items-center mt-2">
            <span class="text-yellow-500">★</span>
            <span class="ml-1">{{ entity.avgRating?.toFixed(1) }}</span>
            <span class="ml-2 text-gray-500">({{ entity.reviewCount }} reviews)</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { useRecommendations } from '../composables/useRecommendations';

const { data, loading, error, getRecommendations } = useRecommendations();

const entityTypes = ['ai-tool', 'course', 'job', 'event', 'hardware'];

const form = reactive({
  description: '',
  entityTypes: [] as string[],
});

const handleSubmit = () => {
  getRecommendations({
    problem_description: form.description,
    filters: {
      entityTypeIds: form.entityTypes.length > 0 ? form.entityTypes : undefined,
      max_candidates: 20,
    },
  });
};
</script>
```

## 📝 TypeScript Definitions

```typescript
// types/api.ts

// Common types
export type EntityType = 'ai-tool' | 'course' | 'job' | 'event' | 'hardware' | 'software' | 'research-paper' | 'podcast' | 'community' | 'grant' | 'newsletter' | 'book';
export type TechnicalLevel = 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
export type PriceRange = 'FREE' | 'LOW' | 'MEDIUM' | 'HIGH';
export type ConversationStage = 'discovery' | 'refinement' | 'clarification' | 'ready';

// Recommendation API types
export interface RecommendationFilters {
  entityTypeIds?: EntityType[];
  searchTerm?: string;
  categoryIds?: string[];
  technical_levels?: TechnicalLevel[];
  learning_curves?: string[];
  has_api?: boolean;
  has_free_tier?: boolean;
  open_source?: boolean;
  platforms?: string[];
  frameworks?: string[];
  use_cases_search?: string;
  key_features_search?: string;
  integrations?: string[];
  target_audience?: string[];
  price_ranges?: PriceRange[];
  price_min?: number;
  price_max?: number;
  max_candidates?: number;
  // ... other filters
}

export interface RecommendationRequest {
  problem_description: string;
  filters: RecommendationFilters;
}

export interface EntityRanking {
  vectorSimilarity: number;
  filterMatch: number;
  entityQuality: number;
  socialProof: number;
  userPreference: number;
  personalizedRelevance: number;
  recency: number;
  popularity: number;
  diversityBonus: number;
  trendingBonus: number;
}

export interface RecommendedEntity {
  id: string;
  name: string;
  description: string;
  shortDescription: string;
  type: string;
  entityType: {
    name: string;
    slug: string;
  };
  categories: Array<{
    category: {
      name: string;
      slug: string;
    };
  }>;
  avgRating: number;
  reviewCount: number;
  website: string;
  pricing: {
    hasFreeTier: boolean;
    priceRange: string;
    details: string;
  };
  features: string[];
  platforms: string[];
  frameworks: string[];
  integrations: string[];
  technicalLevel: string;
  hasApi: boolean;
  isOpenSource: boolean;
  rankingScore: number;
  rankingBreakdown: EntityRanking;
  rankingReason: string;
  relevanceScore: number;
  matchedFilters: string[];
}

export interface RecommendationResponse {
  recommended_entities: RecommendedEntity[];
  candidates_analyzed: number;
  explanation: string;
  applied_filters: Record<string, any>;
  performance_metrics: {
    query_time_ms: number;
    cache_hit: boolean;
    ranking_time_ms: number;
  };
}

// Chat API types
export interface ChatRequest {
  message: string;
  session_id: string | null;
  user_id?: string;
  context?: Record<string, any>;
}

export interface FollowUpQuestion {
  question: string;
  purpose: string;
  expectedFilterKeys: string[];
  priority: number;
}

export interface SuggestedAction {
  action: string;
  description: string;
  priority: number;
}

export interface DiscoveredEntity {
  id: string;
  name: string;
  description: string;
  type: string;
  relevanceScore: number;
  matchedFilters: string[];
  rankingScore: number;
  website: string;
  avgRating: number;
  reviewCount: number;
  pricing: {
    hasFreeTier: boolean;
    priceRange: string;
  };
}

export interface ChatResponse {
  message: string;
  session_id: string;
  conversation_stage: ConversationStage;
  discovered_entities: DiscoveredEntity[];
  follow_up_questions: FollowUpQuestion[];
  suggested_actions: SuggestedAction[];
  extracted_filters: Record<string, any>;
  filter_confidence: Record<string, number>;
  conversation_metadata: {
    total_messages: number;
    filters_extracted: number;
    entities_discovered: number;
    conversation_quality: number;
    readiness_score: number;
  };
  ready_for_recommendations: boolean;
  performance_metrics: {
    response_time_ms: number;
    intent_classification_time_ms: number;
    entity_discovery_time_ms: number;
    cache_hit: boolean;
  };
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

// Error types
export interface ApiError {
  statusCode: number;
  message: string | string[];
  error: string;
}
```

## 🔗 Related Documentation
- [Enhanced Recommendations API](../api/enhanced-recommendations.md)
- [Enhanced Chat API](../api/enhanced-chat.md)
- [Performance Optimization Guide](./performance-guide.md)
- [Error Handling Best Practices](./error-handling.md)
