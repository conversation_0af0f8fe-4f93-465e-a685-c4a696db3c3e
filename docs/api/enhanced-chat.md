# 🤖 Enhanced Chat API

The Enhanced Chat API provides intelligent conversational AI for entity discovery. It uses advanced intent classification, progressive filter refinement, and context-aware responses to help users find the perfect AI tools, courses, jobs, and resources through natural conversation.

## 🚀 Quick Start

### Basic Chat Message
```typescript
POST /chat
Content-Type: application/json

{
  "message": "I need help finding AI tools for my startup",
  "session_id": null
}
```

### Continuing a Conversation
```typescript
POST /chat
Content-Type: application/json

{
  "message": "I'm specifically interested in machine learning tools",
  "session_id": "chat_session_abc123"
}
```

## 📋 Request Schema

### Core Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `message` | string | ✅ | User's message (1-2000 characters) |
| `session_id` | string \| null | ✅ | Session ID for conversation continuity |

### Optional Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `user_id` | string | User ID for personalization |
| `context` | object | Additional context information |

## 📤 Response Schema

### Success Response (200)
```typescript
{
  "message": "string",                    // AI response message
  "session_id": "string",                 // Session identifier
  "conversation_stage": "string",         // Current conversation stage
  "discovered_entities": [                // Entities found based on conversation
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "relevanceScore": number,
      "matchedFilters": ["string"],
      "rankingScore": number,
      "website": "string",
      "avgRating": number,
      "reviewCount": number,
      "pricing": {
        "hasFreeTier": boolean,
        "priceRange": "string"
      }
    }
  ],
  "follow_up_questions": [                // Strategic questions to ask
    {
      "question": "string",
      "purpose": "string",
      "expectedFilterKeys": ["string"],
      "priority": number
    }
  ],
  "suggested_actions": [                  // Recommended next steps
    {
      "action": "string",
      "description": "string",
      "priority": number
    }
  ],
  "extracted_filters": {                  // Filters extracted from conversation
    "entityTypeIds": ["string"],
    "technical_levels": ["string"],
    "has_api": boolean,
    // ... other filters based on conversation
  },
  "filter_confidence": {                  // Confidence in extracted filters
    "entityTypeIds": number,
    "technical_levels": number,
    // ... confidence scores (0-1)
  },
  "conversation_metadata": {
    "total_messages": number,
    "filters_extracted": number,
    "entities_discovered": number,
    "conversation_quality": number,
    "readiness_score": number
  },
  "ready_for_recommendations": boolean,   // Whether enough info for recommendations
  "performance_metrics": {
    "response_time_ms": number,
    "intent_classification_time_ms": number,
    "entity_discovery_time_ms": number,
    "cache_hit": boolean
  }
}
```

## 🎯 Conversation Stages

The chat system progresses through intelligent conversation stages:

### 1. **Discovery** (`discovery`)
- Initial exploration of user needs
- Broad entity type identification
- General use case understanding

**Example:**
```
User: "I need help with AI for my business"
AI: "I'd be happy to help you find the right AI solution! What type of AI assistance are you looking for? For example:
• AI tools for specific tasks (like content creation, data analysis)
• Educational courses to learn about AI
• Job opportunities in the AI field
• Events and conferences about AI"
```

### 2. **Refinement** (`refinement`)
- Progressive filter building
- Detailed requirement gathering
- Preference clarification

**Example:**
```
User: "I need AI tools for content creation"
AI: "Great! Content creation tools can vary quite a bit. To find the best match:
• What's your experience level with AI tools? (Beginner/Intermediate/Advanced)
• What type of content? (Text, images, videos, social media posts)
• Do you need API access for integration?
• What's your budget range?"
```

### 3. **Clarification** (`clarification`)
- Resolving conflicting information
- Handling uncertain filters
- Asking strategic questions

**Example:**
```
User: "I want something advanced but also beginner-friendly"
AI: "I understand you want something powerful but accessible. Let me clarify:
• Are you looking for advanced features with a beginner-friendly interface?
• Or do you want to start with beginner tools and grow into advanced ones?
• What's your current experience with similar tools?"
```

### 4. **Ready** (`ready`)
- Sufficient information gathered
- High-confidence filter set
- Ready for recommendations

**Example:**
```
User: "Yes, I need Python integration and it should be under $100/month"
AI: "Perfect! I have all the information I need. Based on our conversation, I found 8 AI content creation tools that match your criteria:
• Beginner-friendly with advanced features
• Python API integration
• Under $100/month
• Excellent ratings and reviews

Here are the top recommendations..."
```

## 🧠 Advanced Features

### Intent Classification
The system automatically classifies user intents:

| Intent Type | Description | Example |
|-------------|-------------|---------|
| `discovery` | General exploration | "I need AI tools" |
| `specific_tool` | Asking about specific entity | "Tell me about GPT-4" |
| `comparison` | Comparing entities | "TensorFlow vs PyTorch" |
| `refinement` | Modifying criteria | "Actually, I prefer free tools" |
| `general_question` | Non-discovery questions | "What is machine learning?" |

### Progressive Filter Building
Filters are built progressively through conversation:

```typescript
// Message 1: "I need AI tools"
{
  "extracted_filters": {
    "entityTypeIds": ["ai-tool"]
  },
  "filter_confidence": {
    "entityTypeIds": 0.9
  }
}

// Message 2: "For machine learning projects"
{
  "extracted_filters": {
    "entityTypeIds": ["ai-tool"],
    "use_cases_search": "machine learning"
  },
  "filter_confidence": {
    "entityTypeIds": 0.9,
    "use_cases_search": 0.8
  }
}

// Message 3: "I'm a beginner"
{
  "extracted_filters": {
    "entityTypeIds": ["ai-tool"],
    "use_cases_search": "machine learning",
    "technical_levels": ["BEGINNER"]
  },
  "filter_confidence": {
    "entityTypeIds": 0.9,
    "use_cases_search": 0.8,
    "technical_levels": 0.9
  }
}
```

### Context-Aware Responses
The AI maintains context across the entire conversation:

- **Memory**: Remembers all previous messages and extracted information
- **Consistency**: Maintains coherent conversation flow
- **Adaptation**: Adjusts questioning strategy based on user responses
- **Personalization**: Tailors responses to user's technical level and preferences

### Strategic Question Generation
The system generates intelligent follow-up questions:

```typescript
{
  "follow_up_questions": [
    {
      "question": "What's your experience level with machine learning tools?",
      "purpose": "Determine appropriate technical complexity",
      "expectedFilterKeys": ["technical_levels"],
      "priority": 9
    },
    {
      "question": "Do you need API access for integration with your existing systems?",
      "purpose": "Identify integration requirements",
      "expectedFilterKeys": ["has_api", "integrations"],
      "priority": 7
    }
  ]
}
```

## 💡 Usage Examples

### Example 1: Complete Conversation Flow
```typescript
// Message 1
const response1 = await fetch('/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    message: "I need AI tools for my startup",
    session_id: null
  })
});

// Response includes session_id and follow-up questions

// Message 2
const response2 = await fetch('/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    message: "We're building content creation software",
    session_id: response1.session_id
  })
});

// Message 3
const response3 = await fetch('/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    message: "I'm a beginner, budget is under $50/month",
    session_id: response1.session_id
  })
});

// Final response includes discovered entities and recommendations
```

### Example 2: React Chat Component
```typescript
import { useState, useCallback } from 'react';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export const ChatInterface = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const sendMessage = useCallback(async (message: string) => {
    setLoading(true);
    
    // Add user message
    setMessages(prev => [...prev, {
      role: 'user',
      content: message,
      timestamp: new Date()
    }]);

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message,
          session_id: sessionId
        })
      });

      const data = await response.json();
      
      // Update session ID
      if (!sessionId) {
        setSessionId(data.session_id);
      }

      // Add AI response
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: data.message,
        timestamp: new Date()
      }]);

      // Handle discovered entities
      if (data.discovered_entities?.length > 0) {
        // Display entities in UI
        console.log('Found entities:', data.discovered_entities);
      }

    } catch (error) {
      console.error('Chat error:', error);
    } finally {
      setLoading(false);
    }
  }, [sessionId]);

  return (
    <div className="chat-interface">
      {/* Chat messages */}
      {/* Input field */}
      {/* Entity display */}
    </div>
  );
};
```

### Example 3: Conversation History
```typescript
// Get conversation history
const historyResponse = await fetch(`/chat/${sessionId}/history`, {
  method: 'GET'
});

const history = await historyResponse.json();
console.log('Conversation history:', history.messages);
```

## 🎛️ Advanced Configuration

### Custom Context
```typescript
{
  "message": "I need enterprise AI solutions",
  "session_id": "session_123",
  "context": {
    "user_preferences": {
      "technical_level": "ADVANCED",
      "budget_range": "HIGH",
      "preferred_categories": ["machine-learning", "nlp"]
    },
    "company_info": {
      "size": "ENTERPRISE",
      "industry": "TECHNOLOGY"
    }
  }
}
```

### Conversation Optimization
The system automatically optimizes conversation flow:

```typescript
{
  "conversation_metadata": {
    "total_messages": 4,
    "filters_extracted": 6,
    "entities_discovered": 12,
    "conversation_quality": 0.85,      // 0-1 quality score
    "readiness_score": 0.92,           // 0-1 readiness for recommendations
    "efficiency": 0.78                 // Conversation efficiency
  }
}
```

## 🔄 Session Management

### Session Lifecycle
1. **Creation**: New session created on first message
2. **Continuation**: Subsequent messages use session_id
3. **Persistence**: Sessions maintained for 30 minutes of inactivity
4. **Cleanup**: Automatic cleanup of expired sessions

### Session Information
```typescript
GET /chat/{session_id}/info

{
  "session_id": "string",
  "created_at": "ISO_DATE",
  "last_active": "ISO_DATE",
  "message_count": number,
  "current_stage": "string",
  "extracted_filters": object,
  "discovered_entities_count": number
}
```

## 📊 Performance Features

### Intelligent Caching
- **Filter Extraction**: Cached for similar queries
- **Entity Discovery**: Cached based on filter combinations
- **Response Generation**: Template-based caching for common patterns

### Response Time Optimization
- **Average Response Time**: < 1.5 seconds
- **Intent Classification**: < 200ms
- **Entity Discovery**: < 800ms
- **Response Generation**: < 300ms

### Concurrent Session Handling
- **Simultaneous Sessions**: 100+ concurrent users
- **Session Isolation**: Complete separation between sessions
- **Memory Management**: Efficient session state storage

## 🚨 Error Handling

### Common Errors
```typescript
// Empty message
{
  "statusCode": 400,
  "message": "Message cannot be empty",
  "error": "Bad Request"
}

// Invalid session
{
  "statusCode": 404,
  "message": "Session not found or expired",
  "error": "Not Found"
}

// Message too long
{
  "statusCode": 400,
  "message": "Message exceeds maximum length of 2000 characters",
  "error": "Bad Request"
}
```

### Graceful Degradation
The system handles failures gracefully:
- **Service Unavailable**: Falls back to basic responses
- **Timeout**: Returns partial results with explanation
- **Rate Limiting**: Queues requests with user notification

## 🔗 Integration with Recommendations

### Seamless Transition
When ready, chat seamlessly transitions to recommendations:

```typescript
// When ready_for_recommendations: true
const recommendationRequest = {
  problem_description: "Generated from conversation context",
  filters: response.extracted_filters
};

const recommendations = await fetch('/recommendations', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(recommendationRequest)
});
```

### Conversation-Enhanced Recommendations
Chat-discovered filters enhance recommendation quality:
- **Higher Confidence**: Filters validated through conversation
- **Better Context**: Rich understanding of user needs
- **Personalized Results**: Tailored to conversation insights

## 🔗 Related APIs
- [Enhanced Recommendations API](./enhanced-recommendations.md) - Direct recommendation access
- [Session Management API](./sessions.md) - Session handling
- [User Preferences API](./user-preferences.md) - User customization
