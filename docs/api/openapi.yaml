openapi: 3.0.3
info:
  title: AI Nav Backend - Enhanced API
  description: |
    Comprehensive API for AI entity discovery with advanced filtering, conversational AI, and intelligent recommendations.
    
    ## Features
    - 🎯 **Enhanced Recommendations**: 80+ filter parameters with multi-factor ranking
    - 🤖 **Conversational AI**: Intelligent chat-based entity discovery
    - ⚡ **Performance Optimized**: Sub-second responses with intelligent caching
    - 🛡️ **Secure & Reliable**: Enterprise-grade security and error handling
    
    ## Quick Start
    1. Use `/recommendations` for direct entity discovery with comprehensive filters
    2. Use `/chat` for conversational entity discovery through natural language
    3. All responses include performance metrics and ranking explanations
    
    ## Authentication
    Include your API key in the Authorization header:
    ```
    Authorization: Bearer your_api_key_here
    ```
  version: 2.0.0
  contact:
    name: AI Nav Backend Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.ainav.com/v2
    description: Production server
  - url: https://staging-api.ainav.com/v2
    description: Staging server
  - url: http://localhost:3000
    description: Development server

security:
  - BearerAuth: []

paths:
  /recommendations:
    post:
      summary: Get Enhanced Recommendations
      description: |
        Get intelligent AI entity recommendations using advanced filtering and multi-factor ranking.
        
        Supports 80+ filter parameters across all entity types with natural language processing
        and sophisticated ranking algorithms.
      operationId: getRecommendations
      tags:
        - Recommendations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RecommendationRequest'
            examples:
              simple:
                summary: Simple AI tool search
                value:
                  problem_description: "I need an AI tool for data analysis"
                  filters:
                    max_candidates: 10
              complex:
                summary: Complex multi-criteria search
                value:
                  problem_description: "Enterprise ML platform with API access"
                  filters:
                    entityTypeIds: ["ai-tool"]
                    technical_levels: ["ADVANCED", "EXPERT"]
                    has_api: true
                    platforms: ["Web", "Linux"]
                    frameworks: ["TensorFlow", "PyTorch"]
                    price_ranges: ["MEDIUM", "HIGH"]
                    max_candidates: 20
      responses:
        '200':
          description: Successful recommendation response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecommendationResponse'
              examples:
                success:
                  summary: Successful recommendations
                  value:
                    recommended_entities:
                      - id: "tool_123"
                        name: "Advanced ML Platform"
                        description: "Enterprise machine learning platform"
                        type: "ai-tool"
                        avgRating: 4.8
                        reviewCount: 245
                        rankingScore: 0.92
                        rankingReason: "excellent quality, matches technical level"
                    candidates_analyzed: 1247
                    explanation: "Found 8 high-quality recommendations"
                    performance_metrics:
                      query_time_ms: 245
                      cache_hit: false
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimit'
        '500':
          $ref: '#/components/responses/InternalError'

  /chat:
    post:
      summary: Send Chat Message
      description: |
        Send a message to the conversational AI for intelligent entity discovery.
        
        The AI progressively builds filters through natural conversation and provides
        strategic questions to refine search criteria.
      operationId: sendChatMessage
      tags:
        - Chat
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRequest'
            examples:
              new_conversation:
                summary: Start new conversation
                value:
                  message: "I need help finding AI tools for my startup"
                  session_id: null
              continue_conversation:
                summary: Continue existing conversation
                value:
                  message: "I'm specifically interested in machine learning"
                  session_id: "chat_session_abc123"
      responses:
        '200':
          description: Successful chat response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatResponse'
              examples:
                discovery_stage:
                  summary: Discovery stage response
                  value:
                    message: "I'd be happy to help! What type of AI tools are you looking for?"
                    session_id: "chat_session_abc123"
                    conversation_stage: "discovery"
                    follow_up_questions:
                      - question: "What's your experience level with AI tools?"
                        purpose: "Determine technical complexity"
                        priority: 9
                ready_stage:
                  summary: Ready for recommendations
                  value:
                    message: "Perfect! I found 8 AI tools that match your criteria."
                    session_id: "chat_session_abc123"
                    conversation_stage: "ready"
                    discovered_entities:
                      - id: "tool_456"
                        name: "Beginner ML Tool"
                        relevanceScore: 0.89
                    ready_for_recommendations: true
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          description: Session not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '429':
          $ref: '#/components/responses/RateLimit'

  /chat/{sessionId}/history:
    get:
      summary: Get Chat History
      description: Retrieve the conversation history for a specific chat session.
      operationId: getChatHistory
      tags:
        - Chat
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          description: Chat session identifier
          example: "chat_session_abc123"
      responses:
        '200':
          description: Chat history retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  session_id:
                    type: string
                  messages:
                    type: array
                    items:
                      $ref: '#/components/schemas/ChatMessage'
                  metadata:
                    type: object
                    properties:
                      created_at:
                        type: string
                        format: date-time
                      total_messages:
                        type: integer
                      current_stage:
                        type: string
        '404':
          description: Session not found

  /entities:
    get:
      summary: List Entities with Advanced Filtering
      description: |
        Get entities with comprehensive filtering capabilities.
        Supports all 80+ filter parameters for precise entity discovery.
      operationId: listEntities
      tags:
        - Entities
      parameters:
        - name: entityTypeIds
          in: query
          schema:
            type: array
            items:
              type: string
              enum: [ai-tool, course, job, event, hardware, software, research-paper, podcast, community, grant, newsletter, book]
          style: form
          explode: true
          description: Entity types to filter by
        - name: searchTerm
          in: query
          schema:
            type: string
          description: General search term
        - name: technical_levels
          in: query
          schema:
            type: array
            items:
              type: string
              enum: [BEGINNER, INTERMEDIATE, ADVANCED, EXPERT]
          style: form
          explode: true
          description: Technical difficulty levels
        - name: has_api
          in: query
          schema:
            type: boolean
          description: Must have API access
        - name: has_free_tier
          in: query
          schema:
            type: boolean
          description: Must have free tier
        - name: platforms
          in: query
          schema:
            type: array
            items:
              type: string
          style: form
          explode: true
          description: Supported platforms
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          description: Maximum number of results
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
          description: Page number for pagination
      responses:
        '200':
          description: Entities retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityListResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    RecommendationRequest:
      type: object
      required:
        - problem_description
        - filters
      properties:
        problem_description:
          type: string
          minLength: 1
          maxLength: 2000
          description: Natural language description of what you're looking for
          example: "I need a beginner-friendly AI tool with API access for Python machine learning projects"
        filters:
          $ref: '#/components/schemas/RecommendationFilters'

    RecommendationFilters:
      type: object
      properties:
        entityTypeIds:
          type: array
          items:
            type: string
            enum: [ai-tool, course, job, event, hardware, software, research-paper, podcast, community, grant, newsletter, book]
          description: Types of entities to search for
        searchTerm:
          type: string
          description: General search term
        categoryIds:
          type: array
          items:
            type: string
          description: Category slugs to filter by
        technical_levels:
          type: array
          items:
            type: string
            enum: [BEGINNER, INTERMEDIATE, ADVANCED, EXPERT]
          description: Required technical expertise levels
        learning_curves:
          type: array
          items:
            type: string
            enum: [EASY, MODERATE, STEEP, EXPERT]
          description: Learning difficulty levels
        has_api:
          type: boolean
          description: Must have API access
        has_free_tier:
          type: boolean
          description: Must have free tier available
        open_source:
          type: boolean
          description: Must be open source
        platforms:
          type: array
          items:
            type: string
          description: Supported platforms (Web, Linux, Windows, macOS, Mobile, Cloud)
        frameworks:
          type: array
          items:
            type: string
          description: Supported ML frameworks
        use_cases_search:
          type: string
          description: Specific use cases (e.g., "computer vision", "nlp")
        key_features_search:
          type: string
          description: Required features (e.g., "real-time processing")
        integrations:
          type: array
          items:
            type: string
          description: Integration options (REST API, Python SDK, etc.)
        target_audience:
          type: array
          items:
            type: string
          description: Intended users (Developers, Data Scientists, etc.)
        price_ranges:
          type: array
          items:
            type: string
            enum: [FREE, LOW, MEDIUM, HIGH]
          description: Price categories
        price_min:
          type: number
          minimum: 0
          description: Minimum price ($/month)
        price_max:
          type: number
          minimum: 0
          description: Maximum price ($/month)
        max_candidates:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: Maximum number of results to return

    RecommendationResponse:
      type: object
      properties:
        recommended_entities:
          type: array
          items:
            $ref: '#/components/schemas/RecommendedEntity'
        candidates_analyzed:
          type: integer
          description: Total number of entities analyzed
        explanation:
          type: string
          description: Human-readable explanation of the results
        applied_filters:
          type: object
          description: Echo of applied filters with confidence scores
        performance_metrics:
          $ref: '#/components/schemas/PerformanceMetrics'

    RecommendedEntity:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        shortDescription:
          type: string
        type:
          type: string
        entityType:
          type: object
          properties:
            name:
              type: string
            slug:
              type: string
        avgRating:
          type: number
          format: float
          minimum: 0
          maximum: 5
        reviewCount:
          type: integer
          minimum: 0
        website:
          type: string
          format: uri
        pricing:
          type: object
          properties:
            hasFreeTier:
              type: boolean
            priceRange:
              type: string
            details:
              type: string
        features:
          type: array
          items:
            type: string
        platforms:
          type: array
          items:
            type: string
        frameworks:
          type: array
          items:
            type: string
        technicalLevel:
          type: string
        hasApi:
          type: boolean
        isOpenSource:
          type: boolean
        rankingScore:
          type: number
          format: float
          minimum: 0
          maximum: 1
          description: Overall ranking score (0-1)
        rankingBreakdown:
          $ref: '#/components/schemas/EntityRanking'
        rankingReason:
          type: string
          description: Human-readable explanation of ranking
        relevanceScore:
          type: number
          format: float
          minimum: 0
          maximum: 1
        matchedFilters:
          type: array
          items:
            type: string
          description: List of filters this entity matched

    EntityRanking:
      type: object
      description: Detailed breakdown of ranking factors
      properties:
        vectorSimilarity:
          type: number
          format: float
          minimum: 0
          maximum: 1
        filterMatch:
          type: number
          format: float
          minimum: 0
          maximum: 1
        entityQuality:
          type: number
          format: float
          minimum: 0
          maximum: 1
        socialProof:
          type: number
          format: float
          minimum: 0
          maximum: 1
        userPreference:
          type: number
          format: float
          minimum: 0
          maximum: 1
        personalizedRelevance:
          type: number
          format: float
          minimum: 0
          maximum: 1
        recency:
          type: number
          format: float
          minimum: 0
          maximum: 1
        popularity:
          type: number
          format: float
          minimum: 0
          maximum: 1
        diversityBonus:
          type: number
          format: float
          minimum: 0
          maximum: 1
        trendingBonus:
          type: number
          format: float
          minimum: 0
          maximum: 1

    ChatRequest:
      type: object
      required:
        - message
        - session_id
      properties:
        message:
          type: string
          minLength: 1
          maxLength: 2000
          description: User's message
          example: "I need help finding AI tools for my startup"
        session_id:
          type: string
          nullable: true
          description: Session ID for conversation continuity (null for new conversation)
          example: "chat_session_abc123"
        user_id:
          type: string
          description: Optional user ID for personalization
        context:
          type: object
          description: Additional context information

    ChatResponse:
      type: object
      properties:
        message:
          type: string
          description: AI response message
        session_id:
          type: string
          description: Session identifier
        conversation_stage:
          type: string
          enum: [discovery, refinement, clarification, ready]
          description: Current conversation stage
        discovered_entities:
          type: array
          items:
            $ref: '#/components/schemas/DiscoveredEntity'
        follow_up_questions:
          type: array
          items:
            $ref: '#/components/schemas/FollowUpQuestion'
        suggested_actions:
          type: array
          items:
            $ref: '#/components/schemas/SuggestedAction'
        extracted_filters:
          type: object
          description: Filters extracted from conversation
        filter_confidence:
          type: object
          description: Confidence scores for extracted filters (0-1)
        conversation_metadata:
          $ref: '#/components/schemas/ConversationMetadata'
        ready_for_recommendations:
          type: boolean
          description: Whether enough information has been gathered
        performance_metrics:
          $ref: '#/components/schemas/ChatPerformanceMetrics'

    DiscoveredEntity:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        type:
          type: string
        relevanceScore:
          type: number
          format: float
          minimum: 0
          maximum: 1
        matchedFilters:
          type: array
          items:
            type: string
        rankingScore:
          type: number
          format: float
          minimum: 0
          maximum: 1
        website:
          type: string
          format: uri
        avgRating:
          type: number
          format: float
        reviewCount:
          type: integer
        pricing:
          type: object
          properties:
            hasFreeTier:
              type: boolean
            priceRange:
              type: string

    FollowUpQuestion:
      type: object
      properties:
        question:
          type: string
        purpose:
          type: string
        expectedFilterKeys:
          type: array
          items:
            type: string
        priority:
          type: integer
          minimum: 1
          maximum: 10

    SuggestedAction:
      type: object
      properties:
        action:
          type: string
        description:
          type: string
        priority:
          type: integer
          minimum: 1
          maximum: 10

    ConversationMetadata:
      type: object
      properties:
        total_messages:
          type: integer
        filters_extracted:
          type: integer
        entities_discovered:
          type: integer
        conversation_quality:
          type: number
          format: float
          minimum: 0
          maximum: 1
        readiness_score:
          type: number
          format: float
          minimum: 0
          maximum: 1

    ChatMessage:
      type: object
      properties:
        role:
          type: string
          enum: [user, assistant]
        content:
          type: string
        timestamp:
          type: string
          format: date-time

    PerformanceMetrics:
      type: object
      properties:
        query_time_ms:
          type: integer
          description: Total query execution time in milliseconds
        cache_hit:
          type: boolean
          description: Whether the result was served from cache
        ranking_time_ms:
          type: integer
          description: Time spent on ranking algorithms

    ChatPerformanceMetrics:
      type: object
      properties:
        response_time_ms:
          type: integer
        intent_classification_time_ms:
          type: integer
        entity_discovery_time_ms:
          type: integer
        cache_hit:
          type: boolean

    EntityListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/RecommendedEntity'
        pagination:
          type: object
          properties:
            page:
              type: integer
            limit:
              type: integer
            total:
              type: integer
            totalPages:
              type: integer

    Error:
      type: object
      properties:
        statusCode:
          type: integer
        message:
          oneOf:
            - type: string
            - type: array
              items:
                type: string
        error:
          type: string

  responses:
    BadRequest:
      description: Bad request - invalid parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            statusCode: 400
            message: ["technical_levels must be one of: BEGINNER, INTERMEDIATE, ADVANCED, EXPERT"]
            error: "Bad Request"

    Unauthorized:
      description: Unauthorized - invalid or missing API key
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            statusCode: 401
            message: "Unauthorized"
            error: "Unauthorized"

    RateLimit:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            statusCode: 429
            message: "Rate limit exceeded"
            error: "Too Many Requests"

    InternalError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            statusCode: 500
            message: "Internal server error"
            error: "Internal Server Error"

tags:
  - name: Recommendations
    description: Enhanced recommendation system with advanced filtering and ranking
  - name: Chat
    description: Conversational AI for intelligent entity discovery
  - name: Entities
    description: Direct entity access and management
