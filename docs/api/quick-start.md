# 🚀 Quick Start Guide - AI Nav Backend API

Get up and running with the world's most advanced AI discovery API in just 5 minutes! This guide will walk you through everything you need to start building amazing AI discovery experiences.

## 📋 Prerequisites

- Basic knowledge of REST APIs
- A programming language of choice (JavaScript, Python, etc.)
- An HTTP client (curl, Postman, or your favorite library)

## 🔑 Step 1: Get Your API Key

### Option A: Sign Up Online
1. Visit [https://ainav.com/api](https://ainav.com/api)
2. Create your account
3. Get your API key from the dashboard

### Option B: Development/Testing
For this guide, we'll use a demo API key:
```bash
export API_KEY="demo_key_for_testing"
export API_URL="http://localhost:3000"
```

## 🎯 Step 2: Your First Recommendation

Let's start with a simple recommendation request:

### Using curl
```bash
curl -X POST "$API_URL/recommendations" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "problem_description": "I need an AI tool for data analysis",
    "filters": {
      "max_candidates": 10
    }
  }'
```

### Using JavaScript (fetch)
```javascript
const response = await fetch('http://localhost:3000/recommendations', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer demo_key_for_testing',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    problem_description: "I need an AI tool for data analysis",
    filters: {
      max_candidates: 10
    }
  })
});

const data = await response.json();
console.log(`Found ${data.recommended_entities.length} recommendations!`);
```

### Using Python (requests)
```python
import requests

response = requests.post(
    'http://localhost:3000/recommendations',
    headers={
        'Authorization': 'Bearer demo_key_for_testing',
        'Content-Type': 'application/json'
    },
    json={
        'problem_description': 'I need an AI tool for data analysis',
        'filters': {
            'max_candidates': 10
        }
    }
)

data = response.json()
print(f"Found {len(data['recommended_entities'])} recommendations!")
```

### Expected Response
```json
{
  "recommended_entities": [
    {
      "id": "tool_123",
      "name": "DataAnalyzer Pro",
      "description": "Advanced AI-powered data analysis platform",
      "type": "ai-tool",
      "avgRating": 4.8,
      "reviewCount": 245,
      "rankingScore": 0.92,
      "rankingReason": "Excellent match for data analysis with high user ratings",
      "website": "https://dataanalyzer.com",
      "pricing": {
        "hasFreeTier": true,
        "priceRange": "MEDIUM"
      }
    }
  ],
  "candidates_analyzed": 1247,
  "explanation": "Found 8 high-quality AI tools for data analysis",
  "performance_metrics": {
    "query_time_ms": 245,
    "cache_hit": false
  }
}
```

## 🤖 Step 3: Try the Chat Interface

Now let's try the conversational AI:

### Start a Conversation
```bash
curl -X POST "$API_URL/chat" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "I need help finding AI tools for my startup",
    "session_id": null
  }'
```

### Continue the Conversation
```javascript
// First message
let response = await fetch('http://localhost:3000/chat', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer demo_key_for_testing',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    message: "I need help finding AI tools for my startup",
    session_id: null
  })
});

let data = await response.json();
console.log('AI:', data.message);
const sessionId = data.session_id;

// Second message
response = await fetch('http://localhost:3000/chat', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer demo_key_for_testing',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    message: "We're building content creation software",
    session_id: sessionId
  })
});

data = await response.json();
console.log('AI:', data.message);
console.log('Discovered entities:', data.discovered_entities.length);
```

### Expected Chat Flow
```
You: "I need help finding AI tools for my startup"
AI: "I'd be happy to help! What type of AI assistance are you looking for? For example:
    • AI tools for specific tasks (like content creation, data analysis)
    • Educational courses to learn about AI
    • Job opportunities in the AI field"

You: "We're building content creation software"
AI: "Great! Content creation tools can vary quite a bit. To find the best match:
    • What's your experience level with AI tools?
    • What type of content? (Text, images, videos)
    • Do you need API access for integration?"

You: "I'm a beginner, need API access, budget under $100"
AI: "Perfect! I found 6 beginner-friendly content creation tools with API access under $100..."
```

## 🎯 Step 4: Advanced Filtering

Let's try a more complex search with multiple filters:

```javascript
const complexSearch = {
  problem_description: "Enterprise machine learning platform with comprehensive features",
  filters: {
    entityTypeIds: ["ai-tool"],
    technical_levels: ["ADVANCED", "EXPERT"],
    has_api: true,
    has_free_tier: false,
    platforms: ["Web", "Linux", "Cloud"],
    frameworks: ["TensorFlow", "PyTorch"],
    use_cases_search: "machine learning",
    key_features_search: "enterprise scalability",
    price_ranges: ["MEDIUM", "HIGH"],
    integrations: ["REST API", "Python SDK", "Docker"],
    target_audience: ["Data Scientists", "Enterprises"],
    max_candidates: 20
  }
};

const response = await fetch('http://localhost:3000/recommendations', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer demo_key_for_testing',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(complexSearch)
});

const data = await response.json();
console.log('Advanced search results:', data.recommended_entities.length);
```

## 🔍 Step 5: Explore Different Entity Types

### Find Courses
```javascript
const courseSearch = {
  problem_description: "Beginner machine learning courses with certificates",
  filters: {
    entityTypeIds: ["course"],
    skill_levels: ["BEGINNER"],
    certificate_available: true,
    course_format: "Online",
    language: "English",
    max_candidates: 15
  }
};
```

### Find Jobs
```javascript
const jobSearch = {
  problem_description: "Remote machine learning engineering positions",
  filters: {
    entityTypeIds: ["job"],
    employment_types: ["FULL_TIME"],
    experience_levels: ["SENIOR"],
    location_types: ["Remote"],
    salary_min: 120,
    salary_max: 200,
    max_candidates: 20
  }
};
```

### Find Events
```javascript
const eventSearch = {
  problem_description: "AI conferences happening this year",
  filters: {
    entityTypeIds: ["event"],
    event_types: ["Conference"],
    is_online: true,
    cost_type: "Free",
    max_candidates: 10
  }
};
```

## 📊 Step 6: Understanding the Response

### Ranking Information
Every entity includes detailed ranking information:

```javascript
const entity = data.recommended_entities[0];
console.log('Ranking Score:', entity.rankingScore); // 0-1 score
console.log('Ranking Reason:', entity.rankingReason); // Human explanation

// Detailed breakdown
console.log('Ranking Breakdown:', entity.rankingBreakdown);
// {
//   vectorSimilarity: 0.89,
//   filterMatch: 0.95,
//   entityQuality: 0.88,
//   socialProof: 0.76,
//   // ... other factors
// }
```

### Performance Metrics
Monitor API performance:

```javascript
const metrics = data.performance_metrics;
console.log('Query Time:', metrics.query_time_ms, 'ms');
console.log('Cache Hit:', metrics.cache_hit);
console.log('Ranking Time:', metrics.ranking_time_ms, 'ms');
```

## 🛠️ Step 7: Error Handling

Always handle errors gracefully:

```javascript
async function getRecommendations(params) {
  try {
    const response = await fetch('http://localhost:3000/recommendations', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer demo_key_for_testing',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`API Error ${response.status}: ${error.message}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Request failed:', error.message);
    throw error;
  }
}
```

### Common Error Scenarios
```javascript
// Handle validation errors
try {
  await getRecommendations({
    problem_description: "", // Empty description
    filters: { max_candidates: 1000 } // Too many candidates
  });
} catch (error) {
  console.log('Validation error:', error.message);
}

// Handle rate limiting
try {
  // Too many requests
} catch (error) {
  if (error.message.includes('429')) {
    console.log('Rate limited, please wait...');
  }
}
```

## 🚀 Step 8: Build a Simple Interface

Here's a complete React component to get you started:

```jsx
import React, { useState } from 'react';

const AIDiscovery = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const search = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:3000/recommendations', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer demo_key_for_testing',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          problem_description: query,
          filters: { max_candidates: 10 }
        })
      });

      const data = await response.json();
      setResults(data.recommended_entities);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">AI Discovery</h1>
      
      <div className="mb-4">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Describe what you're looking for..."
          className="w-full p-2 border rounded"
        />
        <button
          onClick={search}
          disabled={loading || !query}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          {loading ? 'Searching...' : 'Search'}
        </button>
      </div>

      <div className="space-y-4">
        {results.map(entity => (
          <div key={entity.id} className="border p-4 rounded">
            <h3 className="font-bold">{entity.name}</h3>
            <p className="text-gray-600">{entity.description}</p>
            <div className="flex items-center mt-2">
              <span className="text-yellow-500">★</span>
              <span className="ml-1">{entity.avgRating?.toFixed(1)}</span>
              <span className="ml-2 text-gray-500">
                ({entity.reviewCount} reviews)
              </span>
              <span className="ml-4 text-blue-600">
                Score: {entity.rankingScore?.toFixed(2)}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AIDiscovery;
```

## 🎯 Next Steps

### 1. **Explore Advanced Features**
- Try different entity types (courses, jobs, events)
- Experiment with complex filter combinations
- Test the conversational AI with different scenarios

### 2. **Optimize Performance**
- Implement caching for repeated queries
- Use pagination for large result sets
- Monitor response times and optimize accordingly

### 3. **Enhance User Experience**
- Add loading states and error handling
- Implement search suggestions
- Create filter interfaces for better UX

### 4. **Production Readiness**
- Get a production API key
- Implement proper authentication
- Add monitoring and analytics
- Set up error tracking

## 📚 Additional Resources

- **[Complete API Reference](./enhanced-recommendations.md)** - Detailed documentation
- **[Frontend Integration Guide](../frontend/integration-guide.md)** - React, Vue examples
- **[TypeScript Definitions](../frontend/types.ts)** - Complete type definitions
- **[Best Practices](../frontend/best-practices.md)** - Optimization tips
- **[Troubleshooting](../frontend/troubleshooting.md)** - Common issues and solutions

## 🎉 You're Ready!

Congratulations! You now have everything you need to build amazing AI discovery experiences. The API provides:

- ✅ **Comprehensive Entity Discovery** - 80+ filter parameters
- ✅ **Intelligent Conversational AI** - Natural language interface
- ✅ **Lightning-Fast Performance** - Sub-second responses
- ✅ **Advanced Ranking** - Multi-factor relevance scoring
- ✅ **Production-Ready** - Scalable, reliable, secure

Start building the future of AI discovery! 🚀
