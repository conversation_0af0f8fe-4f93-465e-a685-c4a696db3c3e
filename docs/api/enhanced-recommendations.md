# 🎯 Enhanced Recommendations API

The Enhanced Recommendations API provides intelligent AI entity discovery with advanced filtering, multi-factor ranking, and natural language processing. It supports 80+ filter parameters across all entity types for precise, personalized recommendations.

## 🚀 Quick Start

### Basic Request
```typescript
POST /recommendations
Content-Type: application/json

{
  "problem_description": "I need a beginner-friendly AI tool for machine learning",
  "filters": {
    "max_candidates": 10
  }
}
```

### Enhanced Request with Comprehensive Filters
```typescript
POST /recommendations
Content-Type: application/json

{
  "problem_description": "I need an enterprise-grade AI tool with API access for Python machine learning projects under $100/month",
  "filters": {
    "entityTypeIds": ["ai-tool"],
    "technical_levels": ["INTERMEDIATE", "ADVANCED"],
    "has_api": true,
    "has_free_tier": false,
    "platforms": ["Web", "Linux"],
    "frameworks": ["TensorFlow", "PyTorch"],
    "use_cases_search": "machine learning",
    "key_features_search": "enterprise",
    "price_ranges": ["MEDIUM"],
    "integrations": ["REST API", "Python SDK"],
    "target_audience": ["Developers", "Data Scientists"],
    "max_candidates": 20
  }
}
```

## 📋 Request Schema

### Core Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `problem_description` | string | ✅ | Natural language description of your needs |
| `filters` | object | ✅ | Comprehensive filter criteria |

### Universal Filters (All Entity Types)
| Parameter | Type | Options | Description |
|-----------|------|---------|-------------|
| `entityTypeIds` | string[] | `ai-tool`, `course`, `job`, `event`, `hardware`, `software`, `research-paper`, `podcast`, `community`, `grant`, `newsletter`, `book` | Entity types to search |
| `searchTerm` | string | - | General search term |
| `categoryIds` | string[] | - | Category slugs to filter by |
| `max_candidates` | number | 1-50 | Maximum results to return |

### AI Tool Specific Filters
| Parameter | Type | Options | Description |
|-----------|------|---------|-------------|
| `technical_levels` | string[] | `BEGINNER`, `INTERMEDIATE`, `ADVANCED`, `EXPERT` | Required technical expertise |
| `learning_curves` | string[] | `EASY`, `MODERATE`, `STEEP`, `EXPERT` | Learning difficulty |
| `has_api` | boolean | - | Must have API access |
| `has_free_tier` | boolean | - | Must have free tier |
| `open_source` | boolean | - | Must be open source |
| `platforms` | string[] | `Web`, `Linux`, `Windows`, `macOS`, `Mobile`, `Cloud` | Supported platforms |
| `frameworks` | string[] | `TensorFlow`, `PyTorch`, `Scikit-learn`, `Keras`, `Hugging Face` | ML frameworks |
| `use_cases_search` | string | - | Specific use cases (e.g., "computer vision") |
| `key_features_search` | string | - | Required features (e.g., "real-time processing") |
| `integrations` | string[] | `REST API`, `Python SDK`, `JavaScript SDK`, `Docker`, `Kubernetes` | Integration options |
| `target_audience` | string[] | `Developers`, `Data Scientists`, `Researchers`, `Students`, `Enterprises` | Intended users |
| `price_ranges` | string[] | `FREE`, `LOW`, `MEDIUM`, `HIGH` | Price categories |
| `price_min` | number | - | Minimum price ($/month) |
| `price_max` | number | - | Maximum price ($/month) |

### Course Specific Filters
| Parameter | Type | Options | Description |
|-----------|------|---------|-------------|
| `skill_levels` | string[] | `BEGINNER`, `INTERMEDIATE`, `ADVANCED`, `EXPERT` | Course difficulty |
| `certificate_available` | boolean | - | Offers certificates |
| `instructor_name` | string | - | Instructor name search |
| `duration_text` | string | - | Duration keywords (e.g., "weeks", "months") |
| `prerequisites` | string | - | Required prerequisites |
| `course_format` | string | `Online`, `In-Person`, `Hybrid`, `Self-Paced` | Delivery format |
| `language` | string | - | Course language |
| `university_name` | string | - | University/institution name |

### Job Specific Filters
| Parameter | Type | Options | Description |
|-----------|------|---------|-------------|
| `employment_types` | string[] | `FULL_TIME`, `PART_TIME`, `CONTRACT`, `FREELANCE`, `INTERNSHIP` | Employment type |
| `experience_levels` | string[] | `ENTRY`, `JUNIOR`, `SENIOR`, `LEAD`, `EXECUTIVE` | Experience required |
| `location_types` | string[] | `Remote`, `On-site`, `Hybrid` | Work location |
| `salary_min` | number | - | Minimum salary (k$/year) |
| `salary_max` | number | - | Maximum salary (k$/year) |
| `company_name` | string | - | Company name search |
| `job_description` | string | - | Job description keywords |
| `benefits` | string[] | `Health Insurance`, `Stock Options`, `Remote Work`, `Flexible Hours` | Required benefits |
| `company_size` | string[] | `STARTUP`, `SMALL`, `MEDIUM`, `LARGE`, `ENTERPRISE` | Company size |

### Event Specific Filters
| Parameter | Type | Options | Description |
|-----------|------|---------|-------------|
| `event_types` | string[] | `Conference`, `Workshop`, `Webinar`, `Meetup`, `Hackathon` | Event type |
| `is_online` | boolean | - | Online event |
| `location` | string | - | Event location |
| `start_date_from` | string | - | Start date range (ISO format) |
| `start_date_to` | string | - | End date range (ISO format) |
| `registration_required` | boolean | - | Requires registration |
| `cost_type` | string | `Free`, `Paid`, `Freemium` | Cost structure |

### Hardware Specific Filters
| Parameter | Type | Options | Description |
|-----------|------|---------|-------------|
| `hardware_types` | string[] | `GPU`, `CPU`, `TPU`, `FPGA`, `Memory`, `Storage` | Hardware category |
| `manufacturers` | string[] | `NVIDIA`, `AMD`, `Intel`, `Google`, `Apple` | Manufacturer |
| `memory_search` | string | - | Memory specifications |
| `processor_search` | string | - | Processor details |
| `power_consumption` | string | `Low`, `Medium`, `High` | Power usage |
| `form_factor` | string | `Desktop`, `Server`, `Mobile`, `Embedded` | Physical form |

## 📤 Response Schema

### Success Response (200)
```typescript
{
  "recommended_entities": [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "shortDescription": "string",
      "type": "string",
      "entityType": {
        "name": "string",
        "slug": "string"
      },
      "categories": [
        {
          "category": {
            "name": "string",
            "slug": "string"
          }
        }
      ],
      "avgRating": number,
      "reviewCount": number,
      "website": "string",
      "pricing": {
        "hasFreeTier": boolean,
        "priceRange": "string",
        "details": "string"
      },
      "features": ["string"],
      "platforms": ["string"],
      "frameworks": ["string"],
      "integrations": ["string"],
      "technicalLevel": "string",
      "hasApi": boolean,
      "isOpenSource": boolean,
      "rankingScore": number,
      "rankingBreakdown": {
        "vectorSimilarity": number,
        "filterMatch": number,
        "entityQuality": number,
        "socialProof": number,
        "userPreference": number,
        "personalizedRelevance": number,
        "recency": number,
        "popularity": number,
        "diversityBonus": number,
        "trendingBonus": number
      },
      "rankingReason": "string",
      "relevanceScore": number,
      "matchedFilters": ["string"]
    }
  ],
  "candidates_analyzed": number,
  "explanation": "string",
  "applied_filters": {
    // Echo of applied filters with confidence scores
  },
  "performance_metrics": {
    "query_time_ms": number,
    "cache_hit": boolean,
    "ranking_time_ms": number
  }
}
```

### Error Response (400)
```typescript
{
  "statusCode": 400,
  "message": "string | string[]",
  "error": "Bad Request"
}
```

## 🎯 Advanced Features

### Multi-Factor Ranking
The API uses a sophisticated 10-factor ranking algorithm:

1. **Vector Similarity** (25%) - Semantic relevance to query
2. **Filter Match** (15%) - Precision of filter matching
3. **Entity Quality** (15%) - Ratings, reviews, completeness
4. **Social Proof** (10%) - GitHub stars, downloads, community
5. **User Preferences** (12%) - Category preferences, exclusions
6. **Personalized Relevance** (8%) - Based on user history
7. **Recency** (5%) - How recently updated
8. **Popularity** (5%) - Current trending and engagement
9. **Diversity Bonus** (3%) - Prevents echo chambers
10. **Trending Bonus** (2%) - Emerging/hot entities

### Natural Language Processing
The API automatically extracts filters from natural language:

```typescript
// Input
"I need a beginner-friendly AI tool with API access for Python projects under $50"

// Automatically extracted filters
{
  "entityTypeIds": ["ai-tool"],
  "technical_levels": ["BEGINNER"],
  "has_api": true,
  "frameworks": ["Python"],
  "price_ranges": ["FREE", "LOW"]
}
```

### Performance Optimization
- **Intelligent Caching**: 80%+ cache hit rate for repeated queries
- **Query Optimization**: Automatic filter ordering for performance
- **Response Time**: Sub-second responses for most queries
- **Concurrent Handling**: Supports 50+ simultaneous requests

## 💡 Usage Examples

### Example 1: Simple AI Tool Discovery
```typescript
const response = await fetch('/recommendations', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    problem_description: "I need an AI tool for data analysis",
    filters: {
      entityTypeIds: ["ai-tool"],
      max_candidates: 10
    }
  })
});

const data = await response.json();
console.log(`Found ${data.recommended_entities.length} recommendations`);
```

### Example 2: Complex Multi-Criteria Search
```typescript
const complexSearch = {
  problem_description: "Enterprise machine learning platform with comprehensive features",
  filters: {
    entityTypeIds: ["ai-tool"],
    technical_levels: ["ADVANCED", "EXPERT"],
    has_api: true,
    has_free_tier: false,
    platforms: ["Web", "Linux", "Cloud"],
    frameworks: ["TensorFlow", "PyTorch"],
    use_cases_search: "machine learning",
    key_features_search: "enterprise scalability",
    price_ranges: ["MEDIUM", "HIGH"],
    integrations: ["REST API", "Python SDK", "Docker"],
    target_audience: ["Data Scientists", "Enterprises"],
    max_candidates: 25
  }
};

const response = await fetch('/recommendations', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(complexSearch)
});
```

### Example 3: Course Discovery
```typescript
const courseSearch = {
  problem_description: "Beginner machine learning courses with certificates",
  filters: {
    entityTypeIds: ["course"],
    skill_levels: ["BEGINNER"],
    certificate_available: true,
    course_format: "Online",
    language: "English",
    max_candidates: 15
  }
};
```

### Example 4: Job Search
```typescript
const jobSearch = {
  problem_description: "Remote machine learning engineering positions",
  filters: {
    entityTypeIds: ["job"],
    employment_types: ["FULL_TIME"],
    experience_levels: ["SENIOR"],
    location_types: ["Remote"],
    salary_min: 120,
    salary_max: 200,
    job_description: "machine learning engineer",
    max_candidates: 20
  }
};
```

## 🔧 Frontend Integration Tips

### React Hook Example
```typescript
import { useState, useCallback } from 'react';

interface RecommendationFilters {
  problem_description: string;
  filters: Record<string, any>;
}

export const useRecommendations = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);

  const getRecommendations = useCallback(async (params: RecommendationFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/recommendations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      
      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  return { getRecommendations, loading, data, error };
};
```

### TypeScript Interfaces
```typescript
// Complete TypeScript definitions available in:
// types/api/recommendations.ts
```

## 📊 Performance Monitoring

The API includes performance metrics in responses:

```typescript
{
  "performance_metrics": {
    "query_time_ms": 245,        // Total query execution time
    "cache_hit": true,           // Whether cache was used
    "ranking_time_ms": 89,       // Time spent on ranking
    "entities_analyzed": 1247    // Total entities considered
  }
}
```

## 🚨 Error Handling

### Common Error Scenarios
- **400 Bad Request**: Invalid filters or missing required fields
- **422 Unprocessable Entity**: Valid format but invalid filter combinations
- **429 Too Many Requests**: Rate limiting exceeded
- **500 Internal Server Error**: Server-side processing error

### Error Response Format
```typescript
{
  "statusCode": 400,
  "message": [
    "technical_levels must be one of: BEGINNER, INTERMEDIATE, ADVANCED, EXPERT",
    "max_candidates must be between 1 and 50"
  ],
  "error": "Bad Request"
}
```

## 🔗 Related APIs
- [Enhanced Chat API](./enhanced-chat.md) - Conversational entity discovery
- [Entities API](./entities.md) - Direct entity access
- [Authentication API](./auth.md) - User authentication
