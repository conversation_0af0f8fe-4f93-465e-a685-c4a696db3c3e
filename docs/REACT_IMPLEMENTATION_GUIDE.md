# ⚛️ React Implementation Guide - Entity Filtering

## 🚀 Quick Start - Copy & Paste Ready Components

This guide provides production-ready React components for implementing the enhanced entity filtering system.

## 🔧 Core Hook - useEntityFilters

```jsx
// hooks/useEntityFilters.js
import { useState, useCallback, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';

export const useEntityFilters = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [entities, setEntities] = useState([]);
  const [pagination, setPagination] = useState({});

  // Parse current filters from URL
  const currentFilters = useMemo(() => ({
    searchTerm: searchParams.get('searchTerm') || '',
    page: parseInt(searchParams.get('page')) || 1,
    limit: parseInt(searchParams.get('limit')) || 20,
    entityTypes: searchParams.getAll('entity_types'),
    
    // Tool filters
    hasApi: searchParams.get('has_api') === 'true',
    hasFreeTier: searchParams.get('has_free_tier') === 'true',
    openSource: searchParams.get('open_source') === 'true',
    technicalLevels: searchParams.getAll('technical_levels'),
    platforms: searchParams.getAll('platforms'),
    frameworks: searchParams.getAll('frameworks'),
    
    // Course filters
    certificateAvailable: searchParams.get('certificate_available') === 'true',
    skillLevels: searchParams.getAll('skill_levels'),
    instructorName: searchParams.get('instructor_name') || '',
    
    // Job filters
    employmentTypes: searchParams.getAll('employment_types'),
    experienceLevels: searchParams.getAll('experience_levels'),
    companyName: searchParams.get('company_name') || '',
    salaryMin: searchParams.get('salary_min') ? parseInt(searchParams.get('salary_min')) : null,
    salaryMax: searchParams.get('salary_max') ? parseInt(searchParams.get('salary_max')) : null,
    
    // Event filters
    eventTypes: searchParams.getAll('event_types'),
    isOnline: searchParams.get('is_online') === 'true',
    location: searchParams.get('location') || '',
    
    // Hardware filters
    hardwareTypes: searchParams.getAll('hardware_types'),
    manufacturers: searchParams.getAll('manufacturers'),
    priceMin: searchParams.get('price_min') ? parseInt(searchParams.get('price_min')) : null,
    priceMax: searchParams.get('price_max') ? parseInt(searchParams.get('price_max')) : null,
  }), [searchParams]);

  // Update filters and URL
  const updateFilters = useCallback((newFilters) => {
    const params = new URLSearchParams();
    
    // Add non-empty values to params
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value === null || value === undefined || value === '') return;
      
      if (Array.isArray(value)) {
        value.forEach(item => params.append(getParamName(key), item));
      } else if (typeof value === 'boolean') {
        if (value) params.append(getParamName(key), 'true');
      } else {
        params.append(getParamName(key), value.toString());
      }
    });
    
    setSearchParams(params);
  }, [setSearchParams]);

  // Convert camelCase to snake_case for API
  const getParamName = (key) => {
    const mapping = {
      searchTerm: 'searchTerm',
      entityTypes: 'entity_types',
      hasApi: 'has_api',
      hasFreeTier: 'has_free_tier',
      openSource: 'open_source',
      technicalLevels: 'technical_levels',
      certificateAvailable: 'certificate_available',
      skillLevels: 'skill_levels',
      instructorName: 'instructor_name',
      employmentTypes: 'employment_types',
      experienceLevels: 'experience_levels',
      companyName: 'company_name',
      salaryMin: 'salary_min',
      salaryMax: 'salary_max',
      eventTypes: 'event_types',
      isOnline: 'is_online',
      hardwareTypes: 'hardware_types',
      priceMin: 'price_min',
      priceMax: 'price_max',
    };
    return mapping[key] || key;
  };

  // Fetch entities
  const fetchEntities = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`/entities?${searchParams.toString()}`);
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const data = await response.json();
      setEntities(data.data);
      setPagination({
        total: data.total,
        page: data.page,
        limit: data.limit,
        totalPages: data.totalPages
      });
    } catch (error) {
      console.error('Failed to fetch entities:', error);
      setEntities([]);
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  return {
    filters: currentFilters,
    updateFilters,
    entities,
    pagination,
    loading,
    fetchEntities
  };
};
```

## 🎛️ Filter Components

### Search Component
```jsx
// components/SearchFilter.jsx
import React from 'react';
import { useDebounce } from '../hooks/useDebounce';

export const SearchFilter = ({ value, onChange }) => {
  const debouncedOnChange = useDebounce(onChange, 300);

  return (
    <div className="search-filter">
      <input
        type="text"
        placeholder="Search entities..."
        defaultValue={value}
        onChange={(e) => debouncedOnChange(e.target.value)}
        className="w-full px-4 py-2 border rounded-lg"
      />
    </div>
  );
};
```

### Multi-Select Filter Component
```jsx
// components/MultiSelectFilter.jsx
import React from 'react';

export const MultiSelectFilter = ({ 
  label, 
  options, 
  value = [], 
  onChange, 
  placeholder = "Select options..." 
}) => {
  const handleToggle = (option) => {
    const newValue = value.includes(option)
      ? value.filter(v => v !== option)
      : [...value, option];
    onChange(newValue);
  };

  return (
    <div className="multi-select-filter">
      <label className="block text-sm font-medium mb-2">{label}</label>
      <div className="space-y-2 max-h-48 overflow-y-auto">
        {options.map(option => (
          <label key={option} className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={value.includes(option)}
              onChange={() => handleToggle(option)}
              className="rounded"
            />
            <span className="text-sm">{option}</span>
          </label>
        ))}
      </div>
      {value.length > 0 && (
        <div className="mt-2 text-xs text-gray-600">
          {value.length} selected
        </div>
      )}
    </div>
  );
};
```

### Boolean Filter Component
```jsx
// components/BooleanFilter.jsx
import React from 'react';

export const BooleanFilter = ({ label, value, onChange, description }) => {
  return (
    <div className="boolean-filter">
      <label className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={value}
          onChange={(e) => onChange(e.target.checked)}
          className="rounded"
        />
        <span className="text-sm font-medium">{label}</span>
      </label>
      {description && (
        <p className="text-xs text-gray-600 mt-1">{description}</p>
      )}
    </div>
  );
};
```

### Range Filter Component
```jsx
// components/RangeFilter.jsx
import React from 'react';

export const RangeFilter = ({ 
  label, 
  minValue, 
  maxValue, 
  onMinChange, 
  onMaxChange,
  minPlaceholder = "Min",
  maxPlaceholder = "Max"
}) => {
  return (
    <div className="range-filter">
      <label className="block text-sm font-medium mb-2">{label}</label>
      <div className="flex space-x-2">
        <input
          type="number"
          placeholder={minPlaceholder}
          value={minValue || ''}
          onChange={(e) => onMinChange(e.target.value ? parseInt(e.target.value) : null)}
          className="flex-1 px-3 py-2 border rounded"
        />
        <span className="self-center text-gray-500">to</span>
        <input
          type="number"
          placeholder={maxPlaceholder}
          value={maxValue || ''}
          onChange={(e) => onMaxChange(e.target.value ? parseInt(e.target.value) : null)}
          className="flex-1 px-3 py-2 border rounded"
        />
      </div>
    </div>
  );
};
```

## 🎯 Entity-Specific Filter Panels

### Tool Filters Panel
```jsx
// components/ToolFilters.jsx
import React from 'react';
import { MultiSelectFilter, BooleanFilter } from './';

const TECHNICAL_LEVELS = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
const LEARNING_CURVES = ['EASY', 'MODERATE', 'STEEP', 'VERY_STEEP'];
const PLATFORMS = ['Windows', 'macOS', 'Linux', 'Web', 'Mobile'];
const FRAMEWORKS = ['TensorFlow', 'PyTorch', 'Scikit-learn', 'Keras', 'OpenCV'];

export const ToolFilters = ({ filters, onFiltersChange }) => {
  const updateFilter = (key, value) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">AI Tool Filters</h3>
      
      <BooleanFilter
        label="Has API Access"
        value={filters.hasApi}
        onChange={(value) => updateFilter('hasApi', value)}
        description="Tools that provide API access"
      />
      
      <BooleanFilter
        label="Has Free Tier"
        value={filters.hasFreeTier}
        onChange={(value) => updateFilter('hasFreeTier', value)}
        description="Tools with free usage options"
      />
      
      <BooleanFilter
        label="Open Source"
        value={filters.openSource}
        onChange={(value) => updateFilter('openSource', value)}
        description="Open source tools"
      />
      
      <MultiSelectFilter
        label="Technical Levels"
        options={TECHNICAL_LEVELS}
        value={filters.technicalLevels}
        onChange={(value) => updateFilter('technicalLevels', value)}
      />
      
      <MultiSelectFilter
        label="Learning Curves"
        options={LEARNING_CURVES}
        value={filters.learningCurves}
        onChange={(value) => updateFilter('learningCurves', value)}
      />
      
      <MultiSelectFilter
        label="Platforms"
        options={PLATFORMS}
        value={filters.platforms}
        onChange={(value) => updateFilter('platforms', value)}
      />
      
      <MultiSelectFilter
        label="Frameworks"
        options={FRAMEWORKS}
        value={filters.frameworks}
        onChange={(value) => updateFilter('frameworks', value)}
      />
    </div>
  );
};
```

### Job Filters Panel
```jsx
// components/JobFilters.jsx
import React from 'react';
import { MultiSelectFilter, RangeFilter } from './';

const EMPLOYMENT_TYPES = ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'FREELANCE', 'INTERNSHIP', 'TEMPORARY'];
const EXPERIENCE_LEVELS = ['ENTRY', 'JUNIOR', 'MID', 'SENIOR', 'LEAD', 'PRINCIPAL', 'DIRECTOR'];
const LOCATION_TYPES = ['Remote', 'On-site', 'Hybrid'];

export const JobFilters = ({ filters, onFiltersChange }) => {
  const updateFilter = (key, value) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Job Filters</h3>
      
      <MultiSelectFilter
        label="Employment Types"
        options={EMPLOYMENT_TYPES}
        value={filters.employmentTypes}
        onChange={(value) => updateFilter('employmentTypes', value)}
      />
      
      <MultiSelectFilter
        label="Experience Levels"
        options={EXPERIENCE_LEVELS}
        value={filters.experienceLevels}
        onChange={(value) => updateFilter('experienceLevels', value)}
      />
      
      <MultiSelectFilter
        label="Location Types"
        options={LOCATION_TYPES}
        value={filters.locationTypes}
        onChange={(value) => updateFilter('locationTypes', value)}
      />
      
      <div>
        <label className="block text-sm font-medium mb-2">Company Name</label>
        <input
          type="text"
          placeholder="e.g., Google, Microsoft"
          value={filters.companyName}
          onChange={(e) => updateFilter('companyName', e.target.value)}
          className="w-full px-3 py-2 border rounded"
        />
      </div>
      
      <RangeFilter
        label="Salary Range (thousands)"
        minValue={filters.salaryMin}
        maxValue={filters.salaryMax}
        onMinChange={(value) => updateFilter('salaryMin', value)}
        onMaxChange={(value) => updateFilter('salaryMax', value)}
        minPlaceholder="Min salary"
        maxPlaceholder="Max salary"
      />
    </div>
  );
};
```

## 🎪 Main Filter Container

```jsx
// components/EntityFilters.jsx
import React, { useEffect } from 'react';
import { useEntityFilters } from '../hooks/useEntityFilters';
import { SearchFilter, ToolFilters, JobFilters, CourseFilters, EventFilters } from './';

export const EntityFilters = () => {
  const { filters, updateFilters, entities, pagination, loading, fetchEntities } = useEntityFilters();

  // Fetch entities when filters change
  useEffect(() => {
    fetchEntities();
  }, [fetchEntities]);

  const renderEntitySpecificFilters = () => {
    if (filters.entityTypes.length === 0 || filters.entityTypes.length > 1) {
      return <div className="text-gray-500">Select a specific entity type to see filters</div>;
    }

    const entityType = filters.entityTypes[0];
    
    switch (entityType) {
      case 'ai-tool':
        return <ToolFilters filters={filters} onFiltersChange={updateFilters} />;
      case 'job':
        return <JobFilters filters={filters} onFiltersChange={updateFilters} />;
      case 'course':
        return <CourseFilters filters={filters} onFiltersChange={updateFilters} />;
      case 'event':
        return <EventFilters filters={filters} onFiltersChange={updateFilters} />;
      default:
        return <div className="text-gray-500">Filters for {entityType} coming soon</div>;
    }
  };

  return (
    <div className="flex">
      {/* Sidebar Filters */}
      <div className="w-80 p-6 bg-gray-50 border-r">
        <div className="space-y-6">
          <SearchFilter
            value={filters.searchTerm}
            onChange={(value) => updateFilters({ ...filters, searchTerm: value })}
          />
          
          <MultiSelectFilter
            label="Entity Types"
            options={['ai-tool', 'course', 'job', 'event', 'hardware', 'agency', 'software']}
            value={filters.entityTypes}
            onChange={(value) => updateFilters({ ...filters, entityTypes: value })}
          />
          
          {renderEntitySpecificFilters()}
        </div>
      </div>
      
      {/* Results */}
      <div className="flex-1 p-6">
        {loading ? (
          <div className="text-center py-8">Loading...</div>
        ) : (
          <>
            <div className="mb-4">
              <h2 className="text-xl font-semibold">
                {pagination.total} Results
              </h2>
              <p className="text-gray-600">
                Page {pagination.page} of {pagination.totalPages}
              </p>
            </div>
            
            <div className="grid gap-4">
              {entities.map(entity => (
                <div key={entity.id} className="p-4 border rounded-lg">
                  <h3 className="font-semibold">{entity.name}</h3>
                  <p className="text-gray-600">{entity.shortDescription}</p>
                  <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
                    {entity.entityType.name}
                  </span>
                </div>
              ))}
            </div>
            
            {/* Pagination */}
            <div className="mt-6 flex justify-center space-x-2">
              {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map(page => (
                <button
                  key={page}
                  onClick={() => updateFilters({ ...filters, page })}
                  className={`px-3 py-1 rounded ${
                    page === pagination.page 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 hover:bg-gray-300'
                  }`}
                >
                  {page}
                </button>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
};
```

## 🔧 Utility Hooks

```jsx
// hooks/useDebounce.js
import { useCallback, useRef } from 'react';

export const useDebounce = (callback, delay) => {
  const timeoutRef = useRef(null);

  return useCallback((...args) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};
```

## 🚀 Usage Example

```jsx
// App.jsx
import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { EntityFilters } from './components/EntityFilters';

function App() {
  return (
    <BrowserRouter>
      <div className="min-h-screen bg-gray-100">
        <header className="bg-white shadow">
          <h1 className="text-2xl font-bold p-6">AI Entity Explorer</h1>
        </header>
        <main>
          <EntityFilters />
        </main>
      </div>
    </BrowserRouter>
  );
}

export default App;
```

## ✅ Testing Checklist

- [ ] Search functionality works with debouncing
- [ ] Multi-select filters update URL correctly
- [ ] Boolean filters toggle properly
- [ ] Range filters accept valid numbers
- [ ] Entity-specific filters show/hide based on selection
- [ ] Pagination works with filters
- [ ] URL state persists on page refresh
- [ ] Loading states display correctly
- [ ] Error handling works for API failures

## 🎯 Next Steps

1. **Implement remaining entity filters** (Hardware, Agency, Software, etc.)
2. **Add filter presets** for common use cases
3. **Implement saved searches** functionality
4. **Add filter analytics** to track popular combinations
5. **Optimize performance** with virtual scrolling for large result sets

The backend is fully ready - start implementing these components and you'll have a world-class filtering system! 🚀
