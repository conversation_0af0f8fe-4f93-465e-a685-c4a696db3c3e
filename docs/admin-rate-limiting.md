# Admin Rate Limiting Configuration

## Overview

This document describes the rate limiting configuration for admin users, specifically designed to support scraping operations while maintaining security for regular users.

## Changes Made

### 1. Entity Submission Rate Limits (ValidationService)

**File**: `src/common/validation.service.ts`

- **ADMIN users**: Unlimited entity submissions (no daily limit)
- **MODERATOR users**: 20 submissions per day
- **USER users**: 3 submissions per day

Admin users now bypass the daily submission limit entirely to support scraping operations.

### 2. HTTP Rate Limiting (AdminThrottlerGuard)

**File**: `src/common/guards/admin-throttler.guard.ts`

Created a custom throttler guard that:
- Bypasses HTTP rate limiting for admin users on POST and PATCH `/entities` endpoints
- Maintains normal rate limiting for all other endpoints and user roles
- Falls back to standard throttling if there's an error checking user roles

**Global Rate Limits**:
- **Default**: 20 requests per minute for all endpoints
- **Admin users on entities endpoints**: Unlimited requests
- **Specific endpoints** (auth, chat, recommendations): Have their own `@Throttle` decorators

### 3. Module Configuration

**Files Updated**:
- `src/app.module.ts`: Replaced `ThrottlerGuard` with `AdminThrottlerGuard`
- `src/common/common.module.ts`: Added `AdminThrottlerGuard` to providers and exports

## How It Works

### Entity Submission Flow

1. **Authentication**: User must be authenticated via JWT
2. **Daily Limit Check**: `ValidationService.validateEntitySubmissionRate()` is called
   - Admin users: Skip limit check entirely
   - Other users: Check against daily limits
3. **HTTP Rate Limiting**: `AdminThrottlerGuard.canActivate()` is called
   - Admin users on entities endpoints: Skip throttling
   - All other cases: Apply standard throttling

### Admin Detection

The system identifies admin users by:
1. Checking the `user.role` field in the database
2. Comparing against `UserRole.ADMIN` enum value
3. Making database queries only when necessary (entities endpoints with authenticated users)

## Security Considerations

### What's Protected
- Regular users still have strict rate limits (3 submissions/day, 20 requests/minute)
- Moderators have elevated but limited access (20 submissions/day)
- Non-entities endpoints maintain rate limiting for all users
- Authentication is still required for all entity operations

### What's Bypassed for Admins
- Daily entity submission limits
- HTTP rate limiting on POST/PATCH `/entities` endpoints only

### Error Handling
- If user role lookup fails, the system falls back to standard rate limiting
- Database errors don't prevent rate limiting from working
- Graceful degradation ensures security is maintained

## Testing

To test the implementation:

1. **Regular User**: Should hit rate limits after 3 daily submissions or 20 requests/minute
2. **Admin User**: Should be able to make unlimited entity submissions and requests
3. **Non-Entity Endpoints**: Should maintain rate limiting for all users including admins

## Usage for Scraping

Admin users can now:
- Submit unlimited entities per day (no daily limit)
- Make unlimited POST/PATCH requests to `/entities` endpoints
- Perform bulk operations without hitting rate limits

This configuration is specifically designed to support automated scraping and bulk data import operations while maintaining security for regular users.
