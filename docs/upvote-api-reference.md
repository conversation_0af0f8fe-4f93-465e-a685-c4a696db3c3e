# Upvote API Quick Reference

## 🚀 Quick Start

### Authentication
All requests require JW<PERSON> token in header:
```
Authorization: Bearer {your_jwt_token}
```

### Base Endpoints
```
POST   /entities/{entityId}/upvote    # Add upvote
DELETE /entities/{entityId}/upvote    # Remove upvote
```

## 📋 API Specification

### Add Upvote
```http
POST /entities/{entityId}/upvote
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**Responses:**
```json
// 201 Created - New upvote added
{
  "userId": "123e4567-e89b-12d3-a456-************",
  "entityId": "987fcdeb-51a2-43d1-9f4e-123456789abc", 
  "createdAt": "2025-06-22T12:00:00.000Z"
}

// 200 OK - Already upvoted (idempotent)
{
  "userId": "123e4567-e89b-12d3-a456-************",
  "entityId": "987fcdeb-51a2-43d1-9f4e-123456789abc",
  "createdAt": "2025-06-20T10:30:00.000Z"
}

// 400 Bad Request
{
  "message": "Validation failed (uuid is expected)",
  "error": "Bad Request",
  "statusCode": 400
}

// 401 Unauthorized  
{
  "message": "Unauthorized",
  "statusCode": 401
}

// 404 Not Found
{
  "message": "Entity with ID \"invalid-id\" not found.",
  "error": "Not Found", 
  "statusCode": 404
}
```

### Remove Upvote
```http
DELETE /entities/{entityId}/upvote
Authorization: Bearer {jwt_token}
```

**Responses:**
```http
// 204 No Content - Success (no body)

// 400 Bad Request
{
  "message": "Validation failed (uuid is expected)",
  "error": "Bad Request",
  "statusCode": 400
}

// 401 Unauthorized
{
  "message": "Unauthorized", 
  "statusCode": 401
}
```

## 🔧 JavaScript Examples

### Fetch API
```javascript
// Add upvote
const addUpvote = async (entityId, token) => {
  const response = await fetch(`/entities/${entityId}/upvote`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (response.ok) {
    return await response.json();
  }
  throw new Error(`HTTP ${response.status}`);
};

// Remove upvote  
const removeUpvote = async (entityId, token) => {
  const response = await fetch(`/entities/${entityId}/upvote`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (response.ok) {
    return true; // 204 No Content
  }
  throw new Error(`HTTP ${response.status}`);
};
```

### Axios
```javascript
import axios from 'axios';

const api = axios.create({
  baseURL: '/api',
  headers: {
    'Authorization': `Bearer ${userToken}`
  }
});

// Add upvote
const addUpvote = (entityId) => 
  api.post(`/entities/${entityId}/upvote`);

// Remove upvote
const removeUpvote = (entityId) => 
  api.delete(`/entities/${entityId}/upvote`);
```

## 📊 Entity Data Updates

### Before (existing entity response)
```json
{
  "id": "987fcdeb-51a2-43d1-9f4e-123456789abc",
  "name": "ChatGPT",
  "slug": "chatgpt",
  "description": "AI conversational assistant",
  "websiteUrl": "https://chat.openai.com",
  "status": "ACTIVE"
}
```

### After (with upvote count)
```json
{
  "id": "987fcdeb-51a2-43d1-9f4e-123456789abc", 
  "name": "ChatGPT",
  "slug": "chatgpt",
  "description": "AI conversational assistant",
  "websiteUrl": "https://chat.openai.com",
  "status": "ACTIVE",
  "upvoteCount": 42  // ← NEW FIELD
}
```

## ⚡ Frontend Implementation Pattern

### 1. Component State
```typescript
interface UpvoteState {
  count: number;           // Current upvote count
  isUpvoted: boolean;      // User's upvote status
  isLoading: boolean;      // Request in progress
}
```

### 2. Toggle Function
```typescript
const toggleUpvote = async () => {
  setIsLoading(true);
  
  try {
    if (isUpvoted) {
      await removeUpvote(entityId, token);
      setCount(prev => prev - 1);
      setIsUpvoted(false);
    } else {
      await addUpvote(entityId, token);
      setCount(prev => prev + 1); 
      setIsUpvoted(true);
    }
  } catch (error) {
    // Handle error
  } finally {
    setIsLoading(false);
  }
};
```

### 3. UI States
```tsx
<button 
  onClick={toggleUpvote}
  disabled={isLoading}
  className={isUpvoted ? 'upvoted' : 'not-upvoted'}
>
  {isLoading ? '⏳' : isUpvoted ? '❤️' : '🤍'}
  {count}
</button>
```

## 🎯 Key Features

### ✅ Idempotent Operations
- Adding upvote twice = no error, returns existing upvote
- Removing non-existent upvote = no error, returns success

### ✅ Real-time Count Updates  
- Database triggers automatically maintain `upvoteCount`
- No need to manually calculate counts

### ✅ Performance Optimized
- Average response time: <100ms
- Efficient database operations
- Scalable for high traffic

### ✅ Error Handling
- Comprehensive HTTP status codes
- Descriptive error messages
- Graceful failure modes

## 🔍 Testing Endpoints

### Using curl
```bash
# Add upvote
curl -X POST \
  http://localhost:3000/entities/YOUR_ENTITY_ID/upvote \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# Remove upvote  
curl -X DELETE \
  http://localhost:3000/entities/YOUR_ENTITY_ID/upvote \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Using Postman
1. Set method to POST/DELETE
2. URL: `{{baseUrl}}/entities/{{entityId}}/upvote`
3. Headers: `Authorization: Bearer {{token}}`
4. Send request

## 🚨 Important Notes

### User Upvote Status
The API doesn't provide "is this entity upvoted by current user" endpoint yet. Options:

1. **Track client-side** (recommended for MVP):
   ```javascript
   const userUpvotes = new Set(JSON.parse(localStorage.getItem('upvotes') || '[]'));
   const isUpvoted = userUpvotes.has(entityId);
   ```

2. **Request backend enhancement**:
   ```
   GET /users/me/upvotes
   GET /entities/{entityId}/upvoted-by-me
   ```

### Entity ID Format
- Must be valid UUID format
- Example: `123e4567-e89b-12d3-a456-************`
- Invalid formats return 400 Bad Request

### Authentication
- JWT token required for all operations
- Invalid/expired tokens return 401 Unauthorized
- No anonymous upvoting allowed

## 📚 Additional Resources

- **Swagger Docs**: `/api-docs`
- **Full Documentation**: `docs/upvote-functionality.md`
- **Integration Guide**: `docs/frontend-upvote-integration.md`
- **Performance Tests**: `scripts/test-upvote-performance.js`

## 🆘 Troubleshooting

| Issue | Solution |
|-------|----------|
| 401 Unauthorized | Check JWT token validity |
| 400 Bad Request | Verify entityId is valid UUID |
| 404 Not Found | Confirm entity exists in database |
| Network errors | Check API base URL and connectivity |
| CORS issues | Verify frontend domain is allowed |

## 🎉 Ready to Implement!

The upvote system is fully deployed and ready for frontend integration. All endpoints are live, tested, and documented. Happy coding! 🚀
