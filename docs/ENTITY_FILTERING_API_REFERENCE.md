# 🔍 Entity Filtering API Reference

## 🚀 Production Ready - All Filters Tested & Working

This document provides complete reference for the enhanced entity filtering system. All parameters have been tested and are production-ready.

## 📡 Base Endpoint

```
GET /entities
```

## 🔧 Core Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `searchTerm` | string | Global search across name, description | `searchTerm=AI` |
| `page` | number | Page number (1-based) | `page=1` |
| `limit` | number | Results per page (1-100) | `limit=20` |
| `entity_types` | string[] | Filter by entity types | `entity_types=ai-tool&entity_types=course` |
| `sortBy` | string | Sort field | `sortBy=createdAt` |
| `sortOrder` | string | Sort direction | `sortOrder=desc` |

## 🛠️ Tool Filters (AI Tool Entity Type)

### Boolean Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `has_api` | boolean | Has API access | `has_api=true` |
| `has_free_tier` | boolean | Has free tier available | `has_free_tier=true` |
| `open_source` | boolean | Is open source | `open_source=true` |
| `has_live_chat` | boolean | Has live chat support | `has_live_chat=true` |

### Array Filters
| Parameter | Type | Options | Example |
|-----------|------|---------|---------|
| `technical_levels` | enum[] | BEGINNER, INTERMEDIATE, ADVANCED, EXPERT | `technical_levels=BEGINNER&technical_levels=INTERMEDIATE` |
| `learning_curves` | enum[] | EASY, MODERATE, STEEP, VERY_STEEP | `learning_curves=EASY` |
| `platforms` | string[] | Windows, macOS, Linux, Web, Mobile | `platforms=Windows&platforms=macOS` |
| `frameworks` | string[] | TensorFlow, PyTorch, Scikit-learn | `frameworks=TensorFlow` |
| `integrations` | string[] | GitHub, Slack, Discord | `integrations=GitHub` |
| `libraries` | string[] | NumPy, Pandas, OpenCV | `libraries=NumPy` |
| `deployment_options` | string[] | Cloud, On-premise, Hybrid | `deployment_options=Cloud` |

### String Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `key_features_search` | string | Search in key features | `key_features_search=natural language` |
| `use_cases_search` | string | Search in use cases | `use_cases_search=content generation` |
| `target_audience_search` | string | Search in target audience | `target_audience_search=developers` |
| `customization_level` | string | Customization level | `customization_level=high` |
| `pricing_details_search` | string | Search in pricing details | `pricing_details_search=per user` |

## 🎓 Course Filters

### Boolean Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `certificate_available` | boolean | Certificate available | `certificate_available=true` |
| `has_syllabus_url` | boolean | Has syllabus URL | `has_syllabus_url=true` |

### Array Filters
| Parameter | Type | Options | Example |
|-----------|------|---------|---------|
| `skill_levels` | enum[] | BEGINNER, INTERMEDIATE, ADVANCED, EXPERT | `skill_levels=BEGINNER` |

### String Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `instructor_name` | string | Instructor name (partial match) | `instructor_name=John` |
| `duration_text` | string | Course duration (partial match) | `duration_text=10 hours` |
| `prerequisites` | string | Prerequisites (partial match) | `prerequisites=programming` |

### Number Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `enrollment_min` | number | Minimum enrollment | `enrollment_min=100` |
| `enrollment_max` | number | Maximum enrollment | `enrollment_max=1000` |

## 💼 Job Filters

### Array Filters
| Parameter | Type | Options | Example |
|-----------|------|---------|---------|
| `employment_types` | enum[] | FULL_TIME, PART_TIME, CONTRACT, FREELANCE, INTERNSHIP, TEMPORARY | `employment_types=FULL_TIME` |
| `experience_levels` | enum[] | ENTRY, JUNIOR, MID, SENIOR, LEAD, PRINCIPAL, DIRECTOR | `experience_levels=SENIOR` |
| `location_types` | string[] | Remote, On-site, Hybrid | `location_types=Remote` |

### String Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `company_name` | string | Company name (partial match) | `company_name=Google` |
| `job_title` | string | Job title (partial match) | `job_title=AI Engineer` |
| `job_description` | string | Job description (partial match) | `job_description=machine learning` |

### Number Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `salary_min` | number | Minimum salary (thousands) | `salary_min=50` |
| `salary_max` | number | Maximum salary (thousands) | `salary_max=150` |

### Boolean Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `has_application_url` | boolean | Has application URL | `has_application_url=true` |

## 🎪 Event Filters

### Array Filters
| Parameter | Type | Options | Example |
|-----------|------|---------|---------|
| `event_types` | string[] | Conference, Workshop, Webinar, Meetup, Hackathon | `event_types=Conference` |

### Boolean Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `is_online` | boolean | Is online event | `is_online=true` |
| `registration_required` | boolean | Registration required | `registration_required=true` |
| `has_registration_url` | boolean | Has registration URL | `has_registration_url=true` |

### String Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `location` | string | Event location (partial match) | `location=San Francisco` |
| `price_text` | string | Price text (partial match) | `price_text=Free` |
| `speakers_search` | string | Key speakers (partial match) | `speakers_search=Elon Musk` |

### Date Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `start_date_from` | date | Start date from (YYYY-MM-DD) | `start_date_from=2024-01-01` |
| `start_date_to` | date | Start date to (YYYY-MM-DD) | `start_date_to=2024-12-31` |
| `end_date_from` | date | End date from (YYYY-MM-DD) | `end_date_from=2024-01-01` |
| `end_date_to` | date | End date to (YYYY-MM-DD) | `end_date_to=2024-12-31` |

## 🖥️ Hardware Filters

### Array Filters
| Parameter | Type | Options | Example |
|-----------|------|---------|---------|
| `hardware_types` | enum[] | GPU, CPU, FPGA, TPU, ASIC, NPU, Memory, Storage | `hardware_types=GPU` |
| `manufacturers` | string[] | NVIDIA, AMD, Intel, Apple | `manufacturers=NVIDIA` |

### String Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `price_range` | string | Price range (partial match) | `price_range=$500` |
| `memory_search` | string | Memory specs (partial match) | `memory_search=16GB` |
| `processor_search` | string | Processor specs (partial match) | `processor_search=Intel i7` |

### Date Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `release_date_from` | date | Release date from (YYYY-MM-DD) | `release_date_from=2023-01-01` |
| `release_date_to` | date | Release date to (YYYY-MM-DD) | `release_date_to=2024-12-31` |

### Number Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `price_min` | number | Minimum price | `price_min=500` |
| `price_max` | number | Maximum price | `price_max=2000` |

### Boolean Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `has_datasheet` | boolean | Has datasheet URL | `has_datasheet=true` |

## 🏢 Agency Filters

### Array Filters
| Parameter | Type | Options | Example |
|-----------|------|---------|---------|
| `services_offered` | string[] | AI Strategy, Machine Learning, Data Science, Automation | `services_offered=AI Strategy` |
| `industry_focus` | string[] | Healthcare, Finance, E-commerce, Manufacturing | `industry_focus=Healthcare` |

### Boolean Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `has_portfolio` | boolean | Has portfolio URL | `has_portfolio=true` |

## 💻 Software Filters

### Array Filters
| Parameter | Type | Options | Example |
|-----------|------|---------|---------|
| `license_types` | string[] | MIT, Apache, GPL, Commercial, Proprietary | `license_types=MIT` |
| `programming_languages` | string[] | Python, JavaScript, Java, C++, R | `programming_languages=Python` |
| `platform_compatibility` | string[] | Windows, macOS, Linux, Web, Mobile | `platform_compatibility=Windows` |

### String Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `current_version` | string | Current version (partial match) | `current_version=2.0` |

### Boolean Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `has_repository` | boolean | Has repository URL | `has_repository=true` |

## 📚 Research Paper Filters

### Array Filters
| Parameter | Type | Options | Example |
|-----------|------|---------|---------|
| `research_areas` | string[] | Machine Learning, Computer Vision, NLP, Robotics | `research_areas=Machine Learning` |

### String Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `authors_search` | string | Authors (partial match) | `authors_search=Geoffrey Hinton` |

### Date Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `publication_date_from` | date | Publication date from (YYYY-MM-DD) | `publication_date_from=2020-01-01` |
| `publication_date_to` | date | Publication date to (YYYY-MM-DD) | `publication_date_to=2024-12-31` |

## 📖 Book Filters

### String Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `author_name` | string | Author name (partial match) | `author_name=Andrew Ng` |
| `isbn` | string | ISBN (partial match) | `isbn=978-0262035613` |

### Array Filters
| Parameter | Type | Options | Example |
|-----------|------|---------|---------|
| `formats` | string[] | Hardcover, Paperback, eBook, Audiobook | `formats=eBook` |

## 🎧 Additional Entity Types

The system also supports filters for:
- **Podcasts** - Episode filters, host search, topic filters
- **Communities** - Platform filters, member count, activity level
- **Grants** - Funding amount, deadline filters, eligibility criteria
- **Newsletters** - Frequency filters, topic categories, subscriber count

## 📝 Response Format

All requests return the same standardized format:

```json
{
  "data": [
    {
      "id": "uuid",
      "name": "Entity Name",
      "slug": "entity-slug",
      "logoUrl": "https://...",
      "shortDescription": "Brief description",
      "websiteUrl": "https://...",
      "entityType": {
        "name": "AI Tool",
        "slug": "ai-tool"
      },
      "avgRating": 4.5,
      "reviewCount": 123,
      "saveCount": 456
    }
  ],
  "total": 1234,
  "page": 1,
  "limit": 20,
  "totalPages": 62
}
```

## ⚡ Performance Tips

1. **Use specific filters** instead of broad searches when possible
2. **Combine multiple filters** for more targeted results
3. **Use pagination** with reasonable limit values (20-50)
4. **Cache results** on the frontend for better UX
5. **Debounce search inputs** to reduce API calls

## 🔗 Example API Calls

```javascript
// Search for AI tools with API access
GET /entities?entity_types=ai-tool&has_api=true&technical_levels=BEGINNER

// Find online AI conferences
GET /entities?entity_types=event&event_types=Conference&is_online=true

// Search for machine learning courses with certificates
GET /entities?entity_types=course&searchTerm=machine learning&certificate_available=true

// Find senior AI jobs at tech companies
GET /entities?entity_types=job&experience_levels=SENIOR&company_name=Google

// Search for NVIDIA GPUs under $2000
GET /entities?entity_types=hardware&hardware_types=GPU&manufacturers=NVIDIA&price_max=2000
```

## 🚨 Error Handling

The API returns detailed validation errors:

```json
{
  "statusCode": 400,
  "message": "Input validation failed. Please check your data and try again.",
  "error": "ValidationError",
  "details": {
    "reason": "Invalid input data based on schema."
  }
}
```

Common validation errors:
- Invalid enum values (use exact case: `FULL_TIME`, not `full_time`)
- Invalid date formats (use `YYYY-MM-DD`)
- Invalid number ranges (check min/max constraints)
- Missing required parameters for certain combinations
