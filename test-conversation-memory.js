const axios = require('axios');

/**
 * Test script to verify conversation memory is working correctly
 * This tests the REAL issue - whether assistant responses are being saved to conversation context
 */
async function testConversationMemory() {
  const baseURL = 'http://localhost:3000';
  
  // You'll need to replace this with a valid JWT token
  const authToken = 'your-jwt-token-here';
  
  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };

  try {
    console.log('🧪 Testing Conversation Memory...\n');

    // Test 1: Start a conversation and check if responses are remembered
    console.log('📝 Test 1: Basic conversation memory');
    
    // First message
    console.log('Sending first message...');
    const response1 = await axios.post(`${baseURL}/chat`, {
      message: "Hello, I need help with AI tools"
    }, { headers });
    
    const sessionId = response1.data.session_id;
    console.log('Session ID:', sessionId);
    console.log('First response:', response1.data.message.substring(0, 100) + '...');
    console.log('---');

    // Second message in same session
    console.log('Sending second message in same session...');
    const response2 = await axios.post(`${baseURL}/chat`, {
      message: "What did I just ask you about?",
      session_id: sessionId
    }, { headers });
    
    console.log('Second response:', response2.data.message.substring(0, 100) + '...');
    console.log('---');

    // Check if the assistant remembers the previous conversation
    const remembersContext = response2.data.message.toLowerCase().includes('ai tools') || 
                           response2.data.message.toLowerCase().includes('help') ||
                           response2.data.message.toLowerCase().includes('asked');
    
    console.log('🔍 Memory Test Result:');
    console.log('Assistant remembers context:', remembersContext ? '✅ YES' : '❌ NO');
    
    if (!remembersContext) {
      console.log('❌ MEMORY ISSUE: Assistant doesn\'t remember what user asked about');
      console.log('This indicates conversation context is not being properly maintained');
    }

    // Test 2: Repeat the same question to see if responses are different
    console.log('\n📝 Test 2: Repeated question handling');
    
    const question = "What's the best AI tool for content creation?";
    
    // Ask the question first time
    console.log('Asking question first time...');
    const response3 = await axios.post(`${baseURL}/chat`, {
      message: question,
      session_id: sessionId
    }, { headers });
    
    console.log('First answer:', response3.data.message.substring(0, 150) + '...');
    
    // Ask the same question again
    console.log('Asking same question again...');
    const response4 = await axios.post(`${baseURL}/chat`, {
      message: question,
      session_id: sessionId
    }, { headers });
    
    console.log('Second answer:', response4.data.message.substring(0, 150) + '...');
    
    // Check if responses are identical (bad) or different (good)
    const responsesIdentical = response3.data.message === response4.data.message;
    const followUpQuestionsIdentical = JSON.stringify(response3.data.follow_up_questions) === 
                                     JSON.stringify(response4.data.follow_up_questions);
    
    console.log('🔍 Repetition Test Result:');
    console.log('Responses identical:', responsesIdentical ? '❌ YES (BAD)' : '✅ NO (GOOD)');
    console.log('Follow-up questions identical:', followUpQuestionsIdentical ? '❌ YES (BAD)' : '✅ NO (GOOD)');
    
    // Test 3: Check conversation history length
    console.log('\n📝 Test 3: Conversation history tracking');
    
    // Send a message asking about conversation history
    const response5 = await axios.post(`${baseURL}/chat`, {
      message: "How many messages have we exchanged so far?",
      session_id: sessionId
    }, { headers });
    
    console.log('History response:', response5.data.message.substring(0, 150) + '...');
    
    // The assistant should be aware of the conversation length
    const mentionsHistory = response5.data.message.toLowerCase().includes('message') ||
                          response5.data.message.toLowerCase().includes('conversation') ||
                          response5.data.message.toLowerCase().includes('discussed');
    
    console.log('Assistant aware of conversation history:', mentionsHistory ? '✅ YES' : '❌ NO');

    // Summary
    console.log('\n📊 OVERALL RESULTS:');
    console.log('Memory working:', remembersContext ? '✅' : '❌');
    console.log('Responses varied:', !responsesIdentical ? '✅' : '❌');
    console.log('Follow-ups varied:', !followUpQuestionsIdentical ? '✅' : '❌');
    console.log('History tracking:', mentionsHistory ? '✅' : '❌');
    
    const allTestsPassed = remembersContext && !responsesIdentical && !followUpQuestionsIdentical && mentionsHistory;
    
    if (allTestsPassed) {
      console.log('\n🎉 ALL TESTS PASSED! Conversation memory is working correctly.');
    } else {
      console.log('\n⚠️ SOME TESTS FAILED. Conversation memory needs investigation.');
      
      if (!remembersContext) {
        console.log('- Assistant responses are not being saved to conversation context');
      }
      if (responsesIdentical) {
        console.log('- Identical responses indicate memory or variation issues');
      }
      if (followUpQuestionsIdentical) {
        console.log('- Identical follow-up questions indicate poor question generation');
      }
      if (!mentionsHistory) {
        console.log('- Assistant not aware of conversation history');
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n💡 Note: You need to update the authToken in this script with a valid JWT token');
      console.log('You can get one by logging in through the frontend or API');
    }
  }
}

// Additional test for debugging conversation state
async function debugConversationState() {
  console.log('\n🔧 DEBUGGING TIPS:');
  console.log('1. Check server logs for these messages:');
  console.log('   - "Added user message to session {sessionId}"');
  console.log('   - "Saved assistant response to conversation {sessionId}"');
  console.log('   - "Retrieved existing conversation session {sessionId}"');
  console.log('');
  console.log('2. If assistant responses are not being saved, the issue is in chat.service.ts');
  console.log('3. If conversation context is not being retrieved, check memory-conversation-state.service.ts');
  console.log('4. If session IDs are changing unexpectedly, check conversation-manager.service.ts');
  console.log('');
  console.log('🎯 The fix should ensure assistant responses are saved to conversation context!');
}

// Run the tests
testConversationMemory().then(() => {
  debugConversationState();
});
