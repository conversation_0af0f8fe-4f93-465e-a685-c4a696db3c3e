{"name": "Comprehensive Software Test", "description": "A comprehensive test software to verify all DTO fields work correctly", "url": "https://example.com/comprehensive-software", "entity_type_slug": "software", "software_details": {"key_features": ["Advanced analytics", "Real-time collaboration", "Cloud integration"], "use_cases": ["Data analysis", "Team collaboration", "Project management"], "integrations": ["<PERSON><PERSON>ck", "Google Drive", "Microsoft Teams", "Salesforce"], "target_audience": ["Business Analysts", "Project Managers", "Developers"], "deployment_options": ["Cloud", "On-premise", "Desktop"], "supported_os": ["Windows", "macOS", "Linux"], "mobile_support": true, "api_access": true, "has_free_tier": true, "pricing_model": "SUBSCRIPTION", "price_range": "MEDIUM", "pricing_details": "Starts at $29/month per user", "pricing_url": "https://example.com/pricing", "support_channels": ["Email", "Live Chat", "Phone", "Community Forum"], "support_email": "<EMAIL>", "has_live_chat": true, "community_url": "https://example.com/community", "repository_url": "https://github.com/company/awesome-software", "license_type": "MIT", "programming_languages": ["Python", "JavaScript", "TypeScript"], "platform_compatibility": ["Windows", "macOS", "Linux", "Web"], "current_version": "2.1.0", "release_date": "2024-01-15T00:00:00.000Z", "open_source": true, "customization_level": "High", "demo_available": true, "frameworks": ["React", "Django"], "has_api": true, "libraries": ["TensorFlow", "<PERSON><PERSON>"], "trial_available": true}}