/**
 * Comprehensive verification script for the chat repetition fix
 * 
 * INSTRUCTIONS:
 * 1. Get a JWT token by logging into https://ai-nav.onrender.com
 * 2. Open browser dev tools > Application > Local Storage
 * 3. Copy the JWT token and paste it below
 * 4. Run: node verify-chat-fix.js
 */

const axios = require('axios');

// 🔑 PASTE YOUR JWT TOKEN HERE
const JWT_TOKEN = 'YOUR_JWT_TOKEN_HERE';

const BASE_URL = 'https://ai-nav.onrender.com';
const TEST_SESSION_ID = `verify_fix_${Date.now()}`;

async function verifyFix() {
  console.log('🔍 COMPREHENSIVE CHAT FIX VERIFICATION\n');
  console.log('=' * 50);
  
  if (JWT_TOKEN === 'YOUR_JWT_TOKEN_HERE') {
    console.log('❌ Please update JWT_TOKEN with a valid token from your browser');
    console.log('   1. Go to https://ai-nav.onrender.com');
    console.log('   2. Login or create account');
    console.log('   3. Open Dev Tools > Application > Local Storage');
    console.log('   4. Copy the JWT token and paste it in this script');
    return;
  }
  
  const headers = {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  };
  
  try {
    // Test 1: Initial context setting
    console.log('📝 TEST 1: Setting user context...');
    const response1 = await axios.post(
      `${BASE_URL}/chat`,
      {
        message: 'My name is Alex and I work in software development. I need AI tools for code documentation.',
        session_id: TEST_SESSION_ID
      },
      { headers }
    );
    
    console.log('✅ Response 1 received');
    console.log(`📋 Session: ${response1.data.session_id}`);
    console.log(`🤖 Response: "${response1.data.message.substring(0, 100)}..."`);
    console.log(`🔧 Provider: ${response1.data.metadata?.llm_provider}`);
    
    // Check if it's using fallback
    if (response1.data.metadata?.llm_provider?.includes('FALLBACK')) {
      console.log('⚠️  WARNING: Using fallback response - may indicate parsing issues');
    }
    
    await sleep(3000);
    
    // Test 2: Memory verification
    console.log('\n📝 TEST 2: Testing conversation memory...');
    const response2 = await axios.post(
      `${BASE_URL}/chat`,
      {
        message: 'What is my name and what field do I work in?',
        session_id: TEST_SESSION_ID
      },
      { headers }
    );
    
    console.log('✅ Response 2 received');
    console.log(`🤖 Response: "${response2.data.message}"`);
    
    // Analyze response for context awareness
    const response2Text = response2.data.message.toLowerCase();
    const hasName = response2Text.includes('alex');
    const hasField = response2Text.includes('software') || response2Text.includes('development');
    
    console.log(`🔍 Analysis:`);
    console.log(`   - Remembers name (Alex): ${hasName ? '✅' : '❌'}`);
    console.log(`   - Remembers field (software development): ${hasField ? '✅' : '❌'}`);
    
    await sleep(3000);
    
    // Test 3: Continued conversation
    console.log('\n📝 TEST 3: Testing conversation continuity...');
    const response3 = await axios.post(
      `${BASE_URL}/chat`,
      {
        message: 'Can you recommend specific tools for my work?',
        session_id: TEST_SESSION_ID
      },
      { headers }
    );
    
    console.log('✅ Response 3 received');
    console.log(`🤖 Response: "${response3.data.message.substring(0, 150)}..."`);
    
    // Check if response is contextual
    const response3Text = response3.data.message.toLowerCase();
    const isContextual = response3Text.includes('code') || 
                        response3Text.includes('documentation') || 
                        response3Text.includes('development') ||
                        response3Text.includes('software');
    
    console.log(`🔍 Contextual response: ${isContextual ? '✅' : '❌'}`);
    
    await sleep(2000);
    
    // Test 4: Conversation history
    console.log('\n📝 TEST 4: Checking conversation history...');
    const historyResponse = await axios.get(
      `${BASE_URL}/chat/${TEST_SESSION_ID}/history`,
      { headers }
    );
    
    console.log('✅ History retrieved');
    console.log(`📊 Total messages: ${historyResponse.data.total_messages}`);
    console.log(`📋 Messages in response: ${historyResponse.data.messages.length}`);
    
    // Verify all messages are stored
    if (historyResponse.data.total_messages >= 6) { // 3 user + 3 assistant
      console.log('✅ All messages properly stored');
    } else {
      console.log('⚠️  Some messages may be missing from history');
    }
    
    // Final assessment
    console.log('\n🎯 FINAL ASSESSMENT:');
    console.log('=' * 30);
    
    const memoryWorks = hasName && hasField;
    const contextWorks = isContextual;
    const historyWorks = historyResponse.data.total_messages >= 6;
    const noFallbacks = !response1.data.metadata?.llm_provider?.includes('FALLBACK') &&
                       !response2.data.metadata?.llm_provider?.includes('FALLBACK') &&
                       !response3.data.metadata?.llm_provider?.includes('FALLBACK');
    
    console.log(`Memory retention: ${memoryWorks ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Contextual responses: ${contextWorks ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`History storage: ${historyWorks ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`LLM parsing: ${noFallbacks ? '✅ PASS' : '⚠️  SOME FALLBACKS'}`);
    
    const overallSuccess = memoryWorks && contextWorks && historyWorks;
    
    console.log(`\n🏆 OVERALL RESULT: ${overallSuccess ? '✅ SUCCESS - Chat repetition issue FIXED!' : '❌ ISSUES DETECTED'}`);
    
    if (!overallSuccess) {
      console.log('\n🔧 TROUBLESHOOTING:');
      if (!memoryWorks) console.log('- Memory issue: Check LLM prompt and conversation context passing');
      if (!contextWorks) console.log('- Context issue: Verify LLM is using conversation history');
      if (!historyWorks) console.log('- Storage issue: Check database conversation storage');
      if (!noFallbacks) console.log('- Parsing issue: Check LLM response format and JSON parsing');
    }
    
  } catch (error) {
    console.log('\n❌ TEST FAILED:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n💡 Your JWT token may have expired. Get a fresh one from the browser.');
    }
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Run verification
verifyFix();
