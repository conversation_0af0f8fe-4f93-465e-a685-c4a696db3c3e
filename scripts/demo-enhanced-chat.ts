#!/usr/bin/env ts-node

/**
 * Enhanced Chat System Demonstration Script
 * 
 * This script demonstrates the sophisticated conversational filter refinement
 * capabilities of the enhanced chat system.
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { ChatService } from '../src/chat/services/chat.service';

interface DemoConversation {
  title: string;
  description: string;
  messages: string[];
  expectedOutcomes: string[];
}

const demoConversations: DemoConversation[] = [
  {
    title: "🤖 Progressive AI Tool Discovery",
    description: "Demonstrates how the system builds comprehensive filters through natural conversation",
    messages: [
      "I need help with AI for my startup",
      "We're working on content creation",
      "I'm a beginner and need something with API access",
      "Budget is under $50 per month",
      "Must work on Windows and support Python"
    ],
    expectedOutcomes: [
      "Extracts entity type: ai-tool",
      "Identifies use case: content creation",
      "Sets technical level: beginner",
      "Applies budget constraints",
      "Filters by platform and framework",
      "Provides targeted recommendations"
    ]
  },
  {
    title: "📚 Intelligent Course Recommendation",
    description: "Shows smart questioning and filter accumulation for educational resources",
    messages: [
      "I want to learn machine learning",
      "I'm completely new to this",
      "I need a certificate for my resume",
      "Prefer online format, maybe 6-8 weeks duration"
    ],
    expectedOutcomes: [
      "Identifies learning intent",
      "Sets skill level appropriately",
      "Applies certification requirement",
      "Filters by format and duration",
      "Asks strategic follow-up questions"
    ]
  },
  {
    title: "💼 Smart Job Search Conversation",
    description: "Demonstrates job-specific filter extraction and refinement",
    messages: [
      "I'm looking for a machine learning job",
      "Senior level position",
      "Remote work is essential",
      "Salary should be above $120k",
      "Prefer tech companies"
    ],
    expectedOutcomes: [
      "Extracts job search intent",
      "Sets experience level",
      "Applies location preferences",
      "Filters by salary range",
      "Considers company preferences"
    ]
  },
  {
    title: "🔧 Filter Correction Handling",
    description: "Shows how the system handles user corrections intelligently",
    messages: [
      "I need an advanced AI tool",
      "Actually, I'm a beginner, not advanced",
      "And I prefer free tools",
      "Wait, I can spend up to $30 per month"
    ],
    expectedOutcomes: [
      "Initial filter extraction",
      "Detects and applies correction",
      "Updates budget preferences",
      "Handles multiple corrections gracefully"
    ]
  },
  {
    title: "🎯 Multi-Entity Type Discovery",
    description: "Demonstrates handling of complex requests spanning multiple entity types",
    messages: [
      "I need resources to learn AI",
      "Both tools and courses would be helpful",
      "For computer vision specifically",
      "I'm intermediate level",
      "Budget is flexible but prefer good value"
    ],
    expectedOutcomes: [
      "Handles multiple entity types",
      "Applies domain-specific filtering",
      "Sets appropriate skill level",
      "Balances budget considerations",
      "Provides comprehensive recommendations"
    ]
  }
];

async function runDemo() {
  console.log('🚀 Enhanced Chat System Demonstration\n');
  console.log('This demo showcases the sophisticated conversational filter refinement capabilities.\n');

  const app = await NestFactory.createApplicationContext(AppModule);
  const chatService = app.get(ChatService);

  for (const [index, conversation] of demoConversations.entries()) {
    console.log(`\n${'='.repeat(80)}`);
    console.log(`Demo ${index + 1}: ${conversation.title}`);
    console.log(`${'='.repeat(80)}`);
    console.log(`📝 ${conversation.description}\n`);

    const sessionId = `demo-session-${Date.now()}-${index}`;
    const userId = 'demo-user';

    console.log('💬 Conversation Flow:');
    console.log('-'.repeat(40));

    for (const [msgIndex, message] of conversation.messages.entries()) {
      console.log(`\n👤 User: "${message}"`);
      
      try {
        const response = await chatService.sendMessage(userId, {
          message,
          session_id: sessionId,
        });

        console.log(`🤖 Assistant: "${response.message.substring(0, 150)}${response.message.length > 150 ? '...' : ''}"`);
        
        // Show extracted information
        if (response.metadata?.enhancedFeatures) {
          const features = response.metadata.enhancedFeatures;
          console.log(`   📊 Filters accumulated: ${features.filtersAccumulated || 0}`);
          console.log(`   🎯 Action type: ${features.actionType || 'discovery'}`);
          
          if (features.correctionHandled) {
            console.log(`   ✅ Correction handled successfully`);
          }
        }

        if (response.discovered_entities && response.discovered_entities.length > 0) {
          console.log(`   🔍 Found ${response.discovered_entities.length} relevant entities`);
        }

        if (response.follow_up_questions && response.follow_up_questions.length > 0) {
          console.log(`   ❓ Strategic questions: ${response.follow_up_questions.length}`);
        }

        if (response.should_transition_to_recommendations) {
          console.log(`   🎯 Ready for recommendations!`);
        }

      } catch (error) {
        console.log(`❌ Error: ${error.message}`);
      }

      // Add delay for readability
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log(`\n✅ Expected Outcomes:`);
    conversation.expectedOutcomes.forEach((outcome, i) => {
      console.log(`   ${i + 1}. ${outcome}`);
    });

    console.log(`\n⏱️  Conversation completed in ${conversation.messages.length} turns`);
  }

  console.log(`\n${'='.repeat(80)}`);
  console.log('🎉 Demo Complete!');
  console.log(`${'='.repeat(80)}`);
  console.log('\n📈 Key Capabilities Demonstrated:');
  console.log('   • Progressive filter building through natural conversation');
  console.log('   • Intelligent intent classification with 80+ filter parameters');
  console.log('   • Smart correction handling and conflict resolution');
  console.log('   • Strategic clarifying questions based on missing criteria');
  console.log('   • Multi-entity type discovery and filtering');
  console.log('   • Conversation flow optimization for efficiency');
  console.log('   • Context-aware entity ranking and recommendation readiness');

  console.log('\n🌟 This makes your platform the most sophisticated AI discovery system in the world!');

  await app.close();
}

// Performance monitoring for the demo
async function runPerformanceDemo() {
  console.log('\n🔬 Performance Analysis Demo');
  console.log('-'.repeat(40));

  const app = await NestFactory.createApplicationContext(AppModule);
  const chatService = app.get(ChatService);

  const testMessages = [
    "I need an AI tool for data analysis",
    "I'm a beginner and prefer free options",
    "Must have API access and work on Mac",
    "Actually, I can spend up to $25/month"
  ];

  const sessionId = `perf-test-${Date.now()}`;
  const userId = 'perf-user';

  console.log('Testing conversation performance...\n');

  const startTime = Date.now();
  
  for (const [index, message] of testMessages.entries()) {
    const msgStart = Date.now();
    
    try {
      const response = await chatService.sendMessage(userId, {
        message,
        session_id: sessionId,
      });

      const msgTime = Date.now() - msgStart;
      console.log(`Message ${index + 1}: ${msgTime}ms`);
      console.log(`  Entities found: ${response.discovered_entities?.length || 0}`);
      console.log(`  Filters accumulated: ${response.metadata?.enhancedFeatures?.filtersAccumulated || 0}`);
      
    } catch (error) {
      console.log(`Message ${index + 1}: Error - ${error.message}`);
    }
  }

  const totalTime = Date.now() - startTime;
  console.log(`\nTotal conversation time: ${totalTime}ms`);
  console.log(`Average per message: ${Math.round(totalTime / testMessages.length)}ms`);

  // Get performance metrics
  try {
    const metrics = chatService.getPerformanceMetrics();
    console.log('\n📊 System Performance Metrics:');
    console.log(JSON.stringify(metrics, null, 2));
  } catch (error) {
    console.log('Performance metrics not available');
  }

  await app.close();
}

// Main execution
async function main() {
  try {
    await runDemo();
    await runPerformanceDemo();
  } catch (error) {
    console.error('Demo failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { runDemo, runPerformanceDemo };
