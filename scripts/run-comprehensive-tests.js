#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

/**
 * Comprehensive Test Runner Script
 * 
 * This script orchestrates the execution of all comprehensive tests
 * and provides detailed reporting on the results.
 */

class ComprehensiveTestRunner {
  constructor() {
    this.results = {
      startTime: new Date(),
      endTime: null,
      testSuites: [],
      overallStatus: 'PENDING',
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0
      }
    };
  }

  async run() {
    console.log(chalk.blue.bold('🧪 AI Nav Backend - Comprehensive Test Suite'));
    console.log(chalk.blue('================================================'));
    console.log(chalk.gray(`Started at: ${this.results.startTime.toISOString()}`));
    console.log('');

    try {
      // Ensure test environment is ready
      await this.setupTestEnvironment();

      // Run test suites in order
      await this.runTestSuite('Core Services Unit Tests', 'test:core-services');
      await this.runTestSuite('Enhanced Recommendations Integration', 'test:recommendations');
      await this.runTestSuite('Enhanced Chat Integration', 'test:chat');
      await this.runTestSuite('Performance & Load Tests', 'test:performance');
      await this.runTestSuite('Security & Edge Cases', 'test:security');
      await this.runTestSuite('Full System Validation', 'test:full-validation');

      // Generate comprehensive report
      await this.generateReport();

    } catch (error) {
      console.error(chalk.red.bold('❌ Test execution failed:'), error.message);
      this.results.overallStatus = 'FAILED';
    } finally {
      this.results.endTime = new Date();
      this.printSummary();
    }
  }

  async setupTestEnvironment() {
    console.log(chalk.yellow('🔧 Setting up test environment...'));
    
    // Check if required directories exist
    const requiredDirs = [
      'test-reports',
      'test-reports/comprehensive',
      'coverage',
      'coverage/comprehensive'
    ];

    for (const dir of requiredDirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(chalk.gray(`   Created directory: ${dir}`));
      }
    }

    // Check if test database is accessible
    try {
      // This would check database connectivity in a real scenario
      console.log(chalk.green('   ✅ Test environment ready'));
    } catch (error) {
      throw new Error(`Test environment setup failed: ${error.message}`);
    }
  }

  async runTestSuite(name, scriptName) {
    console.log(chalk.cyan.bold(`\n🧪 Running: ${name}`));
    console.log(chalk.cyan('─'.repeat(50)));

    const startTime = Date.now();
    const result = {
      name,
      scriptName,
      startTime: new Date(),
      endTime: null,
      duration: 0,
      status: 'PENDING',
      output: '',
      error: null,
      stats: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0
      }
    };

    try {
      const output = await this.executeNpmScript(scriptName);
      result.output = output;
      result.status = 'PASSED';
      
      // Parse test results from output
      this.parseTestResults(output, result.stats);
      
      console.log(chalk.green(`   ✅ ${name} completed successfully`));
      console.log(chalk.gray(`   Duration: ${result.duration}ms`));
      
    } catch (error) {
      result.status = 'FAILED';
      result.error = error.message;
      console.log(chalk.red(`   ❌ ${name} failed`));
      console.log(chalk.red(`   Error: ${error.message}`));
    } finally {
      result.endTime = new Date();
      result.duration = Date.now() - startTime;
      this.results.testSuites.push(result);
      
      // Update overall summary
      this.results.summary.total += result.stats.total;
      this.results.summary.passed += result.stats.passed;
      this.results.summary.failed += result.stats.failed;
      this.results.summary.skipped += result.stats.skipped;
    }
  }

  executeNpmScript(scriptName) {
    return new Promise((resolve, reject) => {
      const child = spawn('npm', ['run', scriptName], {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
        // Show real-time output
        process.stdout.write(chalk.gray(data.toString()));
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
        process.stderr.write(chalk.yellow(data.toString()));
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve(stdout);
        } else {
          reject(new Error(`Script ${scriptName} exited with code ${code}\n${stderr}`));
        }
      });

      child.on('error', (error) => {
        reject(new Error(`Failed to start script ${scriptName}: ${error.message}`));
      });
    });
  }

  parseTestResults(output, stats) {
    // Parse Jest output to extract test statistics
    const testRegex = /Tests:\s+(\d+)\s+failed,\s+(\d+)\s+passed,\s+(\d+)\s+total/;
    const passedRegex = /Tests:\s+(\d+)\s+passed,\s+(\d+)\s+total/;
    const failedRegex = /Tests:\s+(\d+)\s+failed/;

    let match = output.match(testRegex);
    if (match) {
      stats.failed = parseInt(match[1]);
      stats.passed = parseInt(match[2]);
      stats.total = parseInt(match[3]);
      return;
    }

    match = output.match(passedRegex);
    if (match) {
      stats.passed = parseInt(match[1]);
      stats.total = parseInt(match[2]);
      stats.failed = 0;
      return;
    }

    // Fallback: try to count test descriptions
    const testDescriptions = output.match(/✓|×/g);
    if (testDescriptions) {
      stats.total = testDescriptions.length;
      stats.passed = (output.match(/✓/g) || []).length;
      stats.failed = (output.match(/×/g) || []).length;
    }
  }

  async generateReport() {
    console.log(chalk.blue.bold('\n📊 Generating comprehensive report...'));

    const reportData = {
      ...this.results,
      generatedAt: new Date().toISOString(),
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      }
    };

    // Generate JSON report
    const jsonReport = path.join('test-reports', 'comprehensive', 'comprehensive-results.json');
    fs.writeFileSync(jsonReport, JSON.stringify(reportData, null, 2));
    console.log(chalk.gray(`   JSON report: ${jsonReport}`));

    // Generate markdown report
    const markdownReport = this.generateMarkdownReport(reportData);
    const mdReport = path.join('test-reports', 'comprehensive', 'comprehensive-results.md');
    fs.writeFileSync(mdReport, markdownReport);
    console.log(chalk.gray(`   Markdown report: ${mdReport}`));

    console.log(chalk.green('   ✅ Reports generated successfully'));
  }

  generateMarkdownReport(data) {
    const duration = (data.endTime - data.startTime) / 1000;
    const successRate = (data.summary.passed / data.summary.total * 100).toFixed(1);

    let markdown = `# Comprehensive Test Results\n\n`;
    markdown += `**Generated:** ${data.generatedAt}\n`;
    markdown += `**Duration:** ${duration.toFixed(1)} seconds\n`;
    markdown += `**Success Rate:** ${successRate}%\n\n`;

    markdown += `## Summary\n\n`;
    markdown += `| Metric | Value |\n`;
    markdown += `|--------|-------|\n`;
    markdown += `| Total Tests | ${data.summary.total} |\n`;
    markdown += `| Passed | ${data.summary.passed} |\n`;
    markdown += `| Failed | ${data.summary.failed} |\n`;
    markdown += `| Skipped | ${data.summary.skipped} |\n\n`;

    markdown += `## Test Suites\n\n`;
    data.testSuites.forEach(suite => {
      const status = suite.status === 'PASSED' ? '✅' : '❌';
      markdown += `### ${status} ${suite.name}\n\n`;
      markdown += `- **Status:** ${suite.status}\n`;
      markdown += `- **Duration:** ${suite.duration}ms\n`;
      markdown += `- **Tests:** ${suite.stats.passed}/${suite.stats.total} passed\n\n`;
      
      if (suite.error) {
        markdown += `**Error:**\n\`\`\`\n${suite.error}\n\`\`\`\n\n`;
      }
    });

    return markdown;
  }

  printSummary() {
    const duration = (this.results.endTime - this.results.startTime) / 1000;
    const successRate = this.results.summary.total > 0 
      ? (this.results.summary.passed / this.results.summary.total * 100).toFixed(1)
      : 0;

    console.log(chalk.blue.bold('\n📋 COMPREHENSIVE TEST SUMMARY'));
    console.log(chalk.blue('====================================='));
    
    console.log(`⏱️  Duration: ${chalk.cyan(duration.toFixed(1))} seconds`);
    console.log(`📊 Success Rate: ${chalk.cyan(successRate)}%`);
    console.log(`📈 Tests: ${chalk.green(this.results.summary.passed)} passed, ${chalk.red(this.results.summary.failed)} failed, ${chalk.yellow(this.results.summary.skipped)} skipped`);
    
    console.log('\n🧪 Test Suites:');
    this.results.testSuites.forEach(suite => {
      const status = suite.status === 'PASSED' 
        ? chalk.green('✅ PASS') 
        : chalk.red('❌ FAIL');
      const duration = chalk.gray(`(${suite.duration}ms)`);
      console.log(`   ${status} ${suite.name} ${duration}`);
    });

    // Overall status
    const allPassed = this.results.testSuites.every(suite => suite.status === 'PASSED');
    if (allPassed && this.results.summary.failed === 0) {
      console.log(chalk.green.bold('\n🎉 ALL TESTS PASSED - SYSTEM FULLY VALIDATED!'));
      console.log(chalk.green('Your enhanced AI discovery platform is ready for production! 🚀'));
      this.results.overallStatus = 'PASSED';
    } else {
      console.log(chalk.red.bold('\n⚠️  SOME TESTS FAILED - REVIEW REQUIRED'));
      console.log(chalk.yellow('Please check the detailed reports and fix any issues.'));
      this.results.overallStatus = 'FAILED';
    }

    console.log(chalk.blue('\n📁 Reports available in: test-reports/comprehensive/'));
    console.log(chalk.blue('=====================================\n'));

    // Exit with appropriate code
    process.exit(this.results.overallStatus === 'PASSED' ? 0 : 1);
  }
}

// Run the comprehensive test suite
if (require.main === module) {
  const runner = new ComprehensiveTestRunner();
  runner.run().catch(error => {
    console.error(chalk.red.bold('Fatal error:'), error);
    process.exit(1);
  });
}

module.exports = ComprehensiveTestRunner;
