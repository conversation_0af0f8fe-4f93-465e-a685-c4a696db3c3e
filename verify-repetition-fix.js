#!/usr/bin/env node

/**
 * Verification script to check if our repetition fixes are properly implemented
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Repetition Fix Implementation...\n');

const checks = [
  {
    name: 'ResponseVariationService exists',
    file: 'src/chat/services/response-variation.service.ts',
    check: (content) => content.includes('export class ResponseVariationService')
  },
  {
    name: 'ResponseVariationService imported in chat.service.ts',
    file: 'src/chat/services/chat.service.ts',
    check: (content) => content.includes('ResponseVariationService')
  },
  {
    name: 'ResponseVariationService added to chat.module.ts',
    file: 'src/chat/chat.module.ts',
    check: (content) => content.includes('ResponseVariationService')
  },
  {
    name: 'Repeated question detection in OpenAI service',
    file: 'src/common/llm/services/openai-llm.service.ts',
    check: (content) => content.includes('isRepeatedQuestion') && content.includes('REPEATED QUESTION DETECTED')
  },
  {
    name: 'Repeated question detection in Anthropic service',
    file: 'src/common/llm/services/anthropic-llm.service.ts',
    check: (content) => content.includes('isRepeatedQuestion')
  },
  {
    name: 'Repeated question detection in Google Gemini service',
    file: 'src/common/llm/services/google-gemini-llm.service.ts',
    check: (content) => content.includes('isRepeatedQuestion')
  },
  {
    name: 'Response variation applied in chat service',
    file: 'src/chat/services/chat.service.ts',
    check: (content) => content.includes('addVariationToResponse') && content.includes('variedResponse')
  },
  {
    name: 'Enhanced anti-repetition rules in prompts',
    file: 'src/common/llm/services/openai-llm.service.ts',
    check: (content) => content.includes('ALWAYS provide unique, varied responses')
  }
];

let passedChecks = 0;
let totalChecks = checks.length;

checks.forEach((check, index) => {
  try {
    const filePath = path.join(__dirname, check.file);
    
    if (!fs.existsSync(filePath)) {
      console.log(`❌ ${index + 1}. ${check.name}: File not found`);
      return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    if (check.check(content)) {
      console.log(`✅ ${index + 1}. ${check.name}`);
      passedChecks++;
    } else {
      console.log(`❌ ${index + 1}. ${check.name}: Check failed`);
    }
  } catch (error) {
    console.log(`❌ ${index + 1}. ${check.name}: Error - ${error.message}`);
  }
});

console.log(`\n📊 Results: ${passedChecks}/${totalChecks} checks passed`);

if (passedChecks === totalChecks) {
  console.log('🎉 All checks passed! The repetition fix is properly implemented.');
  console.log('\n📝 Next steps:');
  console.log('1. Start the server: npm run start:dev');
  console.log('2. Test with the frontend or use the test scripts');
  console.log('3. Monitor logs for "Applied response variation" messages');
} else {
  console.log('⚠️  Some checks failed. Please review the implementation.');
}

// Additional verification: Check for key methods in ResponseVariationService
try {
  const responseVariationPath = path.join(__dirname, 'src/chat/services/response-variation.service.ts');
  if (fs.existsSync(responseVariationPath)) {
    const content = fs.readFileSync(responseVariationPath, 'utf8');
    
    const keyMethods = [
      'addVariationToResponse',
      'isRepeatedQuestion',
      'isSimilarToPreviousResponses',
      'varyResponseForRepeatedQuestion',
      'ensureResponseUniqueness'
    ];
    
    console.log('\n🔧 ResponseVariationService method verification:');
    keyMethods.forEach(method => {
      if (content.includes(method)) {
        console.log(`✅ ${method} method exists`);
      } else {
        console.log(`❌ ${method} method missing`);
      }
    });
  }
} catch (error) {
  console.log('Error verifying ResponseVariationService methods:', error.message);
}

console.log('\n🧪 To test the fix:');
console.log('1. Use the test-repetition-issue.js script');
console.log('2. Or manually test by asking the same question multiple times');
console.log('3. Check that responses are varied and acknowledge repetition');
