-- Create conversation_sessions table for persistent chat storage
-- This fixes the issue where conversations are lost on server restart

CREATE TABLE IF NOT EXISTS conversation_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    user_id UUID NOT NULL,
    context_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversation_sessions_user_id ON conversation_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_sessions_expires_at ON conversation_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_conversation_sessions_updated_at ON conversation_sessions(updated_at);

-- Add foreign key constraint to users table if it exists
-- ALTER TABLE conversation_sessions 
-- ADD CONSTRAINT fk_conversation_sessions_user_id 
-- <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(auth_user_id) ON DELETE CASCADE;

COMMENT ON TABLE conversation_sessions IS 'Stores chat conversation contexts persistently across server restarts';
COMMENT ON COLUMN conversation_sessions.session_id IS 'Unique session identifier (e.g., chat_uuid)';
COMMENT ON COLUMN conversation_sessions.user_id IS 'User ID from auth system';
COMMENT ON COLUMN conversation_sessions.context_data IS 'JSON blob containing conversation context, messages, and metadata';
COMMENT ON COLUMN conversation_sessions.expires_at IS 'When this session expires and can be cleaned up';
