/**
 * Simple test to check if our chat improvements are working
 * This will test without authentication to see the error responses
 */

const axios = require('axios');

const BASE_URL = 'https://ai-nav.onrender.com';

async function testChatEndpoint() {
  console.log('🧪 Testing Chat Endpoint (without auth)...\n');
  
  try {
    console.log('📝 Test: Sending message to chat endpoint...');
    const response = await axios.post(
      `${BASE_URL}/chat`,
      {
        message: 'My name is <PERSON> and I work in education. Please remember this.',
        session_id: `test_${Date.now()}`
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Unexpected success:', response.data);
    
  } catch (error) {
    console.log('❌ Expected error (no auth):', error.response?.status, error.response?.data?.message);
    
    // Check if it's just an auth error (which is expected)
    if (error.response?.status === 401) {
      console.log('✅ Good: Authentication is working (401 Unauthorized as expected)');
    }
  }
  
  // Test health endpoint to make sure API is responding
  try {
    console.log('\n📝 Test: Checking API health...');
    const healthResponse = await axios.get(`${BASE_URL}/healthz`);
    console.log('✅ API is healthy:', healthResponse.data);
  } catch (error) {
    console.log('❌ API health check failed:', error.message);
  }
  
  // Test API docs endpoint to see if it's accessible
  try {
    console.log('\n📝 Test: Checking API docs...');
    const docsResponse = await axios.get(`${BASE_URL}/api-docs-json`);
    console.log('✅ API docs accessible, paths found:', Object.keys(docsResponse.data.paths).length);
  } catch (error) {
    console.log('❌ API docs check failed:', error.message);
  }
  
  console.log('\n🔍 To test the actual chat functionality, you need:');
  console.log('1. A valid JWT token from a logged-in user');
  console.log('2. The token should be added to the Authorization header as "Bearer <token>"');
  console.log('3. You can get a token by logging into the application and checking browser storage');
  
  console.log('\n✅ Basic connectivity tests completed!');
}

// Run the test
testChatEndpoint();
