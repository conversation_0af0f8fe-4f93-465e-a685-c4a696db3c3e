/**
 * Verification script to check if the conversation_sessions table was created successfully
 * This script safely verifies the migration without making any changes
 */

const { PrismaClient } = require('./generated/prisma');

async function verifyMigration() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Verifying conversation_sessions table migration...\n');
    
    // Connect to database
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    // Check if table exists
    const tableExists = await prisma.$queryRaw`
      SELECT table_name, table_type
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'conversation_sessions'
    `;
    
    if (tableExists.length === 0) {
      console.log('❌ conversation_sessions table does not exist');
      console.log('   Please run: node run-conversation-migration.js');
      return false;
    }
    
    console.log('✅ conversation_sessions table exists');
    
    // Check table structure
    const columns = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'conversation_sessions'
      ORDER BY ordinal_position
    `;
    
    console.log('\n📋 Table structure:');
    columns.forEach(col => {
      console.log(`   ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });
    
    // Check indexes
    const indexes = await prisma.$queryRaw`
      SELECT indexname, indexdef 
      FROM pg_indexes 
      WHERE tablename = 'conversation_sessions'
      AND schemaname = 'public'
    `;
    
    console.log('\n🔗 Indexes:');
    indexes.forEach(idx => {
      console.log(`   ${idx.indexname}`);
    });
    
    // Test basic operations (without affecting data)
    console.log('\n🧪 Testing basic operations...');
    
    // Test count (should be 0 for new table)
    const count = await prisma.$queryRaw`
      SELECT COUNT(*) as count FROM conversation_sessions
    `;
    console.log(`   Current records: ${count[0].count}`);
    
    console.log('\n🎉 Migration verification complete!');
    console.log('✅ The conversation_sessions table is ready for use');
    
    return true;
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// Run verification
if (require.main === module) {
  verifyMigration()
    .then((success) => {
      if (success) {
        console.log('\n🚀 Ready to restart your application with persistent chat storage!');
      } else {
        console.log('\n⚠️  Please run the migration first: node run-conversation-migration.js');
      }
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('\n💥 Verification script failed:', error);
      process.exit(1);
    });
}

module.exports = { verifyMigration };
