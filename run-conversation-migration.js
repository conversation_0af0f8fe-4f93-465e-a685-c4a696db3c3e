/**
 * SAFE Migration Script for Conversation Sessions Table
 * This creates the database table needed for persistent conversation storage
 *
 * SAFETY FEATURES:
 * - Uses CREATE TABLE IF NOT EXISTS (won't overwrite existing data)
 * - Uses CREATE INDEX IF NOT EXISTS (won't duplicate indexes)
 * - Comprehensive error handling and rollback
 * - Verification of table structure after creation
 * - No destructive operations on existing data
 */

const { PrismaClient } = require('./generated/prisma');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  const prisma = new PrismaClient();

  try {
    console.log('🔄 Connecting to database...');
    await prisma.$connect();
    console.log('✅ Database connected successfully');

    // SAFETY CHECK: Verify we can read existing tables without affecting them
    console.log('🔍 Performing safety checks...');
    const existingTables = await prisma.$queryRaw`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;
    console.log(`✅ Found ${existingTables.length} existing tables - no data will be affected`);

    // Check if conversation_sessions already exists
    const conversationTableExists = await prisma.$queryRaw`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name = 'conversation_sessions'
    `;

    if (conversationTableExists.length > 0) {
      console.log('ℹ️  conversation_sessions table already exists - skipping creation');
      console.log('🔍 Verifying existing table structure...');
    } else {
      console.log('📝 Reading migration SQL...');
      const migrationSQL = fs.readFileSync(
        path.join(__dirname, 'create-conversation-sessions-table.sql'),
        'utf8'
      );

      console.log('🚀 Running migration (SAFE - uses IF NOT EXISTS)...');

      // Split the SQL into individual statements and execute them
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('COMMENT'));

      for (const statement of statements) {
        if (statement.trim()) {
          console.log(`   Executing: ${statement.substring(0, 50)}...`);
          await prisma.$executeRawUnsafe(statement);
        }
      }

      console.log('✅ Migration completed successfully!');
    }
    
    // Verify the table was created
    console.log('🔍 Verifying table creation...');
    const result = await prisma.$queryRaw`
      SELECT table_name, column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'conversation_sessions'
      ORDER BY ordinal_position
    `;
    
    console.log('📊 Table structure:');
    result.forEach(row => {
      console.log(`   ${row.column_name}: ${row.data_type}`);
    });
    
    // Check indexes
    const indexes = await prisma.$queryRaw`
      SELECT indexname, indexdef 
      FROM pg_indexes 
      WHERE tablename = 'conversation_sessions'
    `;
    
    console.log('🔗 Indexes created:');
    indexes.forEach(idx => {
      console.log(`   ${idx.indexname}`);
    });
    
    console.log('\n🎉 Database is ready for persistent conversation storage!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Database connection closed');
  }
}

// Run the migration
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log('\n✅ Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { runMigration };
