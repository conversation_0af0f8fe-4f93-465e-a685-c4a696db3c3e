/**
 * Test script to verify conversation persistence across server restarts
 * This script tests the database-backed conversation storage solution
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TEST_SESSION_ID = `test_persistence_${Date.now()}`;

// Test user credentials (using your admin account)
const TEST_USER = {
  email: '<EMAIL>', // You'll need to provide the actual email
  password: 'testtest' // You'll need to provide the actual password
};

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function authenticateUser() {
  try {
    console.log('🔐 Authenticating test user...');
    const response = await axios.post(`${BASE_URL}/auth/login`, TEST_USER);
    return response.data.access_token;
  } catch (error) {
    console.error('❌ Authentication failed:', error.response?.data || error.message);
    throw error;
  }
}

async function sendChatMessage(token, message, sessionId) {
  try {
    console.log(`💬 Sending message: "${message}"`);
    const response = await axios.post(
      `${BASE_URL}/chat`,
      {
        message: message,
        session_id: sessionId
      },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log(`🤖 Assistant response: "${response.data.response.substring(0, 100)}..."`);
    return response.data;
  } catch (error) {
    console.error('❌ Chat message failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testConversationPersistence() {
  console.log('🚀 Starting conversation persistence test...\n');
  
  try {
    // Step 1: Authenticate
    const token = await authenticateUser();
    console.log('✅ Authentication successful\n');
    
    // Step 2: Send initial message to establish context
    console.log('📝 Phase 1: Establishing conversation context');
    await sendChatMessage(token, 'My name is John and I work in education', TEST_SESSION_ID);
    await sleep(1000);
    
    // Step 3: Send a follow-up message
    await sendChatMessage(token, 'What AI tools would you recommend for my field?', TEST_SESSION_ID);
    await sleep(1000);
    
    console.log('\n⏸️  Phase 2: Simulating server restart...');
    console.log('   (In a real test, you would restart the server here)');
    console.log('   For this test, we\'ll wait 3 seconds to simulate restart delay');
    await sleep(3000);
    
    // Step 4: Test if context is remembered after "restart"
    console.log('🔄 Phase 3: Testing conversation memory after restart');
    const memoryTestResponse = await sendChatMessage(
      token, 
      'What\'s my name and what field do I work in?', 
      TEST_SESSION_ID
    );
    
    // Step 5: Analyze response for context awareness
    const response = memoryTestResponse.response.toLowerCase();
    const remembersName = response.includes('john');
    const remembersField = response.includes('education');
    
    console.log('\n📊 Test Results:');
    console.log(`   Remembers name (John): ${remembersName ? '✅' : '❌'}`);
    console.log(`   Remembers field (education): ${remembersField ? '✅' : '❌'}`);
    
    if (remembersName && remembersField) {
      console.log('\n🎉 SUCCESS: Conversation persistence is working!');
      console.log('   The assistant remembered the conversation context across the simulated restart.');
    } else {
      console.log('\n❌ FAILURE: Conversation persistence is not working properly.');
      console.log('   The assistant did not remember the conversation context.');
    }
    
    // Step 6: Test repetition fix
    console.log('\n🔄 Phase 4: Testing repetition fix');
    console.log('   Asking the same question multiple times...');
    
    const question = 'What\'s the best AI tool for content creation?';
    const responses = [];
    
    for (let i = 0; i < 3; i++) {
      console.log(`   Attempt ${i + 1}:`);
      const response = await sendChatMessage(token, question, TEST_SESSION_ID);
      responses.push(response.response);
      await sleep(1000);
    }
    
    // Check if responses are different
    const uniqueResponses = new Set(responses);
    const hasVariation = uniqueResponses.size > 1;
    
    console.log('\n📊 Repetition Test Results:');
    console.log(`   Unique responses: ${uniqueResponses.size} out of 3`);
    console.log(`   Has variation: ${hasVariation ? '✅' : '❌'}`);
    
    if (hasVariation) {
      console.log('   The assistant provided different responses, showing awareness of repetition.');
    } else {
      console.log('   The assistant provided identical responses, indicating repetition issue persists.');
    }
    
    console.log('\n✅ Test completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testConversationPersistence()
    .then(() => {
      console.log('\n🏁 All tests completed.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { testConversationPersistence };
