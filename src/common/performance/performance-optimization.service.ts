import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Performance Optimization Service
 * 
 * Provides comprehensive performance optimizations for the enhanced
 * recommendation and chat systems including:
 * - Intelligent caching strategies
 * - Query optimization
 * - Response time monitoring
 * - Memory management
 * - Concurrent request handling
 */
@Injectable()
export class PerformanceOptimizationService {
  private readonly logger = new Logger(PerformanceOptimizationService.name);
  
  // Performance caches
  private readonly filterExtractionCache = new Map<string, any>();
  private readonly entityRankingCache = new Map<string, any>();
  private readonly vectorSearchCache = new Map<string, any>();
  private readonly conversationStateCache = new Map<string, any>();
  
  // Performance metrics
  private readonly performanceMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    avgResponseTime: 0,
    totalRequests: 0,
    concurrentRequests: 0,
    memoryUsage: 0,
  };

  // Cache configuration
  private readonly cacheConfig = {
    filterExtractionTTL: 5 * 60 * 1000, // 5 minutes
    entityRankingTTL: 2 * 60 * 1000, // 2 minutes
    vectorSearchTTL: 10 * 60 * 1000, // 10 minutes
    conversationStateTTL: 30 * 60 * 1000, // 30 minutes
    maxCacheSize: 1000,
  };

  constructor(private readonly configService: ConfigService) {
    this.initializePerformanceMonitoring();
  }

  /**
   * Optimized filter extraction with caching
   */
  async optimizedFilterExtraction(
    description: string,
    extractionFn: () => Promise<any>,
  ): Promise<any> {
    const cacheKey = this.generateCacheKey('filter', description);
    
    // Check cache first
    const cached = this.getFromCache(this.filterExtractionCache, cacheKey);
    if (cached) {
      this.performanceMetrics.cacheHits++;
      return cached;
    }

    // Execute extraction with performance monitoring
    const startTime = Date.now();
    try {
      const result = await extractionFn();
      const executionTime = Date.now() - startTime;
      
      // Cache the result
      this.setInCache(
        this.filterExtractionCache,
        cacheKey,
        result,
        this.cacheConfig.filterExtractionTTL,
      );
      
      this.performanceMetrics.cacheMisses++;
      this.updateResponseTime(executionTime);
      
      return result;
    } catch (error) {
      this.logger.error('Filter extraction failed', error.stack);
      throw error;
    }
  }

  /**
   * Optimized entity ranking with intelligent caching
   */
  async optimizedEntityRanking(
    entities: any[],
    context: any,
    rankingFn: () => any[],
  ): Promise<any[]> {
    const cacheKey = this.generateRankingCacheKey(entities, context);
    
    // Check cache first
    const cached = this.getFromCache(this.entityRankingCache, cacheKey);
    if (cached) {
      this.performanceMetrics.cacheHits++;
      return cached;
    }

    // Execute ranking with performance monitoring
    const startTime = Date.now();
    try {
      const result = rankingFn();
      const executionTime = Date.now() - startTime;
      
      // Cache the result (shorter TTL for ranking due to dynamic nature)
      this.setInCache(
        this.entityRankingCache,
        cacheKey,
        result,
        this.cacheConfig.entityRankingTTL,
      );
      
      this.performanceMetrics.cacheMisses++;
      this.updateResponseTime(executionTime);
      
      return result;
    } catch (error) {
      this.logger.error('Entity ranking failed', error.stack);
      throw error;
    }
  }

  /**
   * Optimized vector search with caching
   */
  async optimizedVectorSearch(
    query: string,
    searchFn: () => Promise<any[]>,
  ): Promise<any[]> {
    const cacheKey = this.generateCacheKey('vector', query);
    
    // Check cache first
    const cached = this.getFromCache(this.vectorSearchCache, cacheKey);
    if (cached) {
      this.performanceMetrics.cacheHits++;
      return cached;
    }

    // Execute search with performance monitoring
    const startTime = Date.now();
    try {
      const result = await searchFn();
      const executionTime = Date.now() - startTime;
      
      // Cache the result
      this.setInCache(
        this.vectorSearchCache,
        cacheKey,
        result,
        this.cacheConfig.vectorSearchTTL,
      );
      
      this.performanceMetrics.cacheMisses++;
      this.updateResponseTime(executionTime);
      
      return result;
    } catch (error) {
      this.logger.error('Vector search failed', error.stack);
      throw error;
    }
  }

  /**
   * Optimized conversation state management
   */
  async optimizedConversationState(
    sessionId: string,
    stateFn: () => Promise<any>,
  ): Promise<any> {
    const cacheKey = `conversation:${sessionId}`;
    
    // Check cache first
    const cached = this.getFromCache(this.conversationStateCache, cacheKey);
    if (cached) {
      this.performanceMetrics.cacheHits++;
      return cached;
    }

    // Load state with performance monitoring
    const startTime = Date.now();
    try {
      const result = await stateFn();
      const executionTime = Date.now() - startTime;
      
      // Cache the state
      this.setInCache(
        this.conversationStateCache,
        cacheKey,
        result,
        this.cacheConfig.conversationStateTTL,
      );
      
      this.performanceMetrics.cacheMisses++;
      this.updateResponseTime(executionTime);
      
      return result;
    } catch (error) {
      this.logger.error('Conversation state loading failed', error.stack);
      throw error;
    }
  }

  /**
   * Batch processing optimization for multiple entities
   */
  async optimizedBatchProcessing<T, R>(
    items: T[],
    processFn: (item: T) => Promise<R>,
    batchSize: number = 10,
  ): Promise<R[]> {
    const results: R[] = [];
    
    // Process in batches to avoid overwhelming the system
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchPromises = batch.map(item => processFn(item));
      
      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      } catch (error) {
        this.logger.error(`Batch processing failed for batch starting at index ${i}`, error.stack);
        // Continue with next batch instead of failing completely
      }
    }
    
    return results;
  }

  /**
   * Memory optimization - cleanup old cache entries
   */
  optimizeMemoryUsage(): void {
    const caches = [
      this.filterExtractionCache,
      this.entityRankingCache,
      this.vectorSearchCache,
      this.conversationStateCache,
    ];

    caches.forEach(cache => {
      if (cache.size > this.cacheConfig.maxCacheSize) {
        // Remove oldest entries (simple LRU)
        const entries = Array.from(cache.entries());
        const toRemove = entries.slice(0, entries.length - this.cacheConfig.maxCacheSize);
        toRemove.forEach(([key]) => cache.delete(key));
        
        this.logger.debug(`Cleaned up ${toRemove.length} cache entries`);
      }
    });

    // Update memory usage metric
    this.performanceMetrics.memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024; // MB
  }

  /**
   * Get comprehensive performance metrics
   */
  getPerformanceMetrics(): any {
    const cacheHitRate = this.performanceMetrics.cacheHits / 
                        (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses) * 100;

    return {
      ...this.performanceMetrics,
      cacheHitRate: isNaN(cacheHitRate) ? 0 : cacheHitRate,
      cacheStats: {
        filterExtraction: this.filterExtractionCache.size,
        entityRanking: this.entityRankingCache.size,
        vectorSearch: this.vectorSearchCache.size,
        conversationState: this.conversationStateCache.size,
      },
      systemHealth: this.getSystemHealth(),
    };
  }

  /**
   * Clear all caches (admin operation)
   */
  clearAllCaches(): void {
    this.filterExtractionCache.clear();
    this.entityRankingCache.clear();
    this.vectorSearchCache.clear();
    this.conversationStateCache.clear();
    
    this.logger.log('All performance caches cleared');
  }

  // Private helper methods
  private generateCacheKey(type: string, content: string): string {
    // Create a hash-like key from content
    const hash = Buffer.from(content).toString('base64').slice(0, 16);
    return `${type}:${hash}`;
  }

  private generateRankingCacheKey(entities: any[], context: any): string {
    const entityIds = entities.map(e => e.id).sort().join(',');
    const contextHash = JSON.stringify(context).slice(0, 50);
    return `ranking:${entityIds.slice(0, 50)}:${contextHash}`;
  }

  private getFromCache(cache: Map<string, any>, key: string): any {
    const entry = cache.get(key);
    if (!entry) return null;
    
    // Check if expired
    if (Date.now() > entry.expiry) {
      cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  private setInCache(cache: Map<string, any>, key: string, data: any, ttl: number): void {
    cache.set(key, {
      data,
      expiry: Date.now() + ttl,
    });
  }

  private updateResponseTime(executionTime: number): void {
    this.performanceMetrics.totalRequests++;
    this.performanceMetrics.avgResponseTime = 
      (this.performanceMetrics.avgResponseTime * (this.performanceMetrics.totalRequests - 1) + executionTime) /
      this.performanceMetrics.totalRequests;
  }

  private initializePerformanceMonitoring(): void {
    // Periodic memory cleanup
    setInterval(() => {
      this.optimizeMemoryUsage();
    }, 5 * 60 * 1000); // Every 5 minutes

    // Performance logging
    setInterval(() => {
      const metrics = this.getPerformanceMetrics();
      this.logger.debug('Performance metrics', metrics);
    }, 10 * 60 * 1000); // Every 10 minutes
  }

  private getSystemHealth(): string {
    const metrics = this.performanceMetrics;

    if (metrics.avgResponseTime < 500 && metrics.memoryUsage < 512) {
      return 'EXCELLENT';
    } else if (metrics.avgResponseTime < 1000 && metrics.memoryUsage < 1024) {
      return 'GOOD';
    } else if (metrics.avgResponseTime < 2000 && metrics.memoryUsage < 2048) {
      return 'FAIR';
    } else {
      return 'POOR';
    }
  }

  /**
   * Concurrent request management
   */
  async manageConcurrentRequests<T>(
    requestFn: () => Promise<T>,
    maxConcurrent: number = 10,
  ): Promise<T> {
    // Simple semaphore-like implementation
    while (this.performanceMetrics.concurrentRequests >= maxConcurrent) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    this.performanceMetrics.concurrentRequests++;

    try {
      const result = await requestFn();
      return result;
    } finally {
      this.performanceMetrics.concurrentRequests--;
    }
  }

  /**
   * Intelligent cache warming for frequently accessed data
   */
  async warmCache(
    warmingStrategies: Array<{ key: string; loadFn: () => Promise<any> }>,
  ): Promise<void> {
    this.logger.log('Starting cache warming...');

    const warmingPromises = warmingStrategies.map(async strategy => {
      try {
        const data = await strategy.loadFn();
        this.setInCache(this.vectorSearchCache, strategy.key, data, this.cacheConfig.vectorSearchTTL);
        this.logger.debug(`Cache warmed for key: ${strategy.key}`);
      } catch (error) {
        this.logger.warn(`Cache warming failed for key: ${strategy.key}`, error.message);
      }
    });

    await Promise.allSettled(warmingPromises);
    this.logger.log('Cache warming completed');
  }
}
