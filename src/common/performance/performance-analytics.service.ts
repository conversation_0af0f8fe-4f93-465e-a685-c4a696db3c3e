import { Injectable, Logger } from '@nestjs/common';

/**
 * Performance Analytics Service
 * 
 * Provides comprehensive performance analytics and monitoring for the
 * enhanced recommendation and chat systems including:
 * - Real-time performance metrics
 * - System health monitoring
 * - Performance trend analysis
 * - Bottleneck identification
 * - Optimization recommendations
 */
@Injectable()
export class PerformanceAnalyticsService {
  private readonly logger = new Logger(PerformanceAnalyticsService.name);

  // Performance tracking
  private readonly performanceHistory: PerformanceSnapshot[] = [];
  private readonly maxHistorySize = 1000;

  // System metrics
  private systemMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    avgResponseTime: 0,
    peakResponseTime: 0,
    cacheHitRate: 0,
    systemLoad: 0,
    memoryUsage: 0,
    lastUpdated: new Date(),
  };

  // Performance thresholds
  private readonly thresholds = {
    responseTime: {
      excellent: 200,
      good: 500,
      fair: 1000,
      poor: 2000,
    },
    cacheHitRate: {
      excellent: 90,
      good: 75,
      fair: 60,
      poor: 40,
    },
    memoryUsage: {
      excellent: 256,
      good: 512,
      fair: 1024,
      poor: 2048,
    },
  };

  /**
   * Record a performance snapshot
   */
  recordPerformanceSnapshot(
    operation: string,
    responseTime: number,
    success: boolean,
    metadata?: any,
  ): void {
    const snapshot: PerformanceSnapshot = {
      timestamp: new Date(),
      operation,
      responseTime,
      success,
      metadata,
    };

    // Add to history
    this.performanceHistory.push(snapshot);
    
    // Maintain history size
    if (this.performanceHistory.length > this.maxHistorySize) {
      this.performanceHistory.shift();
    }

    // Update system metrics
    this.updateSystemMetrics(snapshot);

    // Check for performance issues
    this.checkPerformanceThresholds(snapshot);
  }

  /**
   * Get comprehensive performance analytics
   */
  getPerformanceAnalytics(): PerformanceAnalytics {
    const recentSnapshots = this.getRecentSnapshots(60); // Last 60 minutes
    
    return {
      overview: this.getPerformanceOverview(),
      trends: this.getPerformanceTrends(recentSnapshots),
      bottlenecks: this.identifyBottlenecks(recentSnapshots),
      recommendations: this.generateOptimizationRecommendations(),
      systemHealth: this.getSystemHealthScore(),
      alerts: this.getActiveAlerts(),
    };
  }

  /**
   * Get real-time system health dashboard
   */
  getSystemHealthDashboard(): SystemHealthDashboard {
    const recentSnapshots = this.getRecentSnapshots(5); // Last 5 minutes
    
    return {
      status: this.getOverallSystemStatus(),
      metrics: {
        responseTime: this.calculateAverageResponseTime(recentSnapshots),
        throughput: this.calculateThroughput(recentSnapshots),
        errorRate: this.calculateErrorRate(recentSnapshots),
        cachePerformance: this.getCachePerformanceMetrics(),
        systemResources: this.getSystemResourceMetrics(),
      },
      alerts: this.getActiveAlerts(),
      uptime: this.calculateUptime(),
      lastUpdated: new Date(),
    };
  }

  /**
   * Get performance comparison between time periods
   */
  getPerformanceComparison(
    currentPeriodMinutes: number = 60,
    previousPeriodMinutes: number = 60,
  ): PerformanceComparison {
    const currentSnapshots = this.getRecentSnapshots(currentPeriodMinutes);
    const previousSnapshots = this.getSnapshotsFromPeriod(
      currentPeriodMinutes + previousPeriodMinutes,
      currentPeriodMinutes,
    );

    return {
      current: this.calculatePeriodMetrics(currentSnapshots),
      previous: this.calculatePeriodMetrics(previousSnapshots),
      changes: this.calculateMetricChanges(currentSnapshots, previousSnapshots),
      insights: this.generatePerformanceInsights(currentSnapshots, previousSnapshots),
    };
  }

  /**
   * Get operation-specific performance metrics
   */
  getOperationMetrics(operation: string): OperationMetrics {
    const operationSnapshots = this.performanceHistory.filter(s => s.operation === operation);
    
    if (operationSnapshots.length === 0) {
      return {
        operation,
        totalRequests: 0,
        avgResponseTime: 0,
        successRate: 0,
        trends: [],
        recommendations: [],
      };
    }

    return {
      operation,
      totalRequests: operationSnapshots.length,
      avgResponseTime: this.calculateAverageResponseTime(operationSnapshots),
      successRate: this.calculateSuccessRate(operationSnapshots),
      p95ResponseTime: this.calculatePercentile(operationSnapshots, 95),
      p99ResponseTime: this.calculatePercentile(operationSnapshots, 99),
      trends: this.calculateOperationTrends(operationSnapshots),
      recommendations: this.generateOperationRecommendations(operationSnapshots),
    };
  }

  // Private helper methods
  private updateSystemMetrics(snapshot: PerformanceSnapshot): void {
    this.systemMetrics.totalRequests++;
    
    if (snapshot.success) {
      this.systemMetrics.successfulRequests++;
    } else {
      this.systemMetrics.failedRequests++;
    }

    // Update average response time
    this.systemMetrics.avgResponseTime = 
      (this.systemMetrics.avgResponseTime * (this.systemMetrics.totalRequests - 1) + snapshot.responseTime) /
      this.systemMetrics.totalRequests;

    // Update peak response time
    if (snapshot.responseTime > this.systemMetrics.peakResponseTime) {
      this.systemMetrics.peakResponseTime = snapshot.responseTime;
    }

    // Update system resources
    this.systemMetrics.memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024; // MB
    this.systemMetrics.lastUpdated = new Date();
  }

  private checkPerformanceThresholds(snapshot: PerformanceSnapshot): void {
    // Check response time threshold
    if (snapshot.responseTime > this.thresholds.responseTime.poor) {
      this.logger.warn(`Slow response detected: ${snapshot.operation} (${snapshot.responseTime}ms)`);
    }

    // Check memory usage
    if (this.systemMetrics.memoryUsage > this.thresholds.memoryUsage.poor) {
      this.logger.warn(`High memory usage detected: ${this.systemMetrics.memoryUsage}MB`);
    }
  }

  private getRecentSnapshots(minutes: number): PerformanceSnapshot[] {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000);
    return this.performanceHistory.filter(s => s.timestamp >= cutoff);
  }

  private getSnapshotsFromPeriod(startMinutesAgo: number, endMinutesAgo: number): PerformanceSnapshot[] {
    const start = new Date(Date.now() - startMinutesAgo * 60 * 1000);
    const end = new Date(Date.now() - endMinutesAgo * 60 * 1000);
    return this.performanceHistory.filter(s => s.timestamp >= start && s.timestamp <= end);
  }

  private getPerformanceOverview(): PerformanceOverview {
    return {
      totalRequests: this.systemMetrics.totalRequests,
      successRate: (this.systemMetrics.successfulRequests / this.systemMetrics.totalRequests) * 100,
      avgResponseTime: this.systemMetrics.avgResponseTime,
      peakResponseTime: this.systemMetrics.peakResponseTime,
      systemLoad: this.systemMetrics.systemLoad,
      memoryUsage: this.systemMetrics.memoryUsage,
    };
  }

  private getPerformanceTrends(snapshots: PerformanceSnapshot[]): PerformanceTrends {
    const hourlyBuckets = this.groupSnapshotsByHour(snapshots);
    
    return {
      responseTime: hourlyBuckets.map(bucket => ({
        timestamp: bucket.hour,
        value: this.calculateAverageResponseTime(bucket.snapshots),
      })),
      throughput: hourlyBuckets.map(bucket => ({
        timestamp: bucket.hour,
        value: bucket.snapshots.length,
      })),
      errorRate: hourlyBuckets.map(bucket => ({
        timestamp: bucket.hour,
        value: this.calculateErrorRate(bucket.snapshots),
      })),
    };
  }

  private identifyBottlenecks(snapshots: PerformanceSnapshot[]): Bottleneck[] {
    const operationGroups = this.groupSnapshotsByOperation(snapshots);
    const bottlenecks: Bottleneck[] = [];

    Object.entries(operationGroups).forEach(([operation, operationSnapshots]) => {
      const avgResponseTime = this.calculateAverageResponseTime(operationSnapshots);
      const errorRate = this.calculateErrorRate(operationSnapshots);

      if (avgResponseTime > this.thresholds.responseTime.fair) {
        bottlenecks.push({
          type: 'slow_operation',
          operation,
          severity: avgResponseTime > this.thresholds.responseTime.poor ? 'high' : 'medium',
          description: `${operation} has slow average response time: ${avgResponseTime.toFixed(0)}ms`,
          recommendation: 'Consider optimizing this operation or adding caching',
        });
      }

      if (errorRate > 5) {
        bottlenecks.push({
          type: 'high_error_rate',
          operation,
          severity: errorRate > 10 ? 'high' : 'medium',
          description: `${operation} has high error rate: ${errorRate.toFixed(1)}%`,
          recommendation: 'Investigate error causes and improve error handling',
        });
      }
    });

    return bottlenecks;
  }

  private generateOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];
    const recentSnapshots = this.getRecentSnapshots(60);

    // Response time recommendations
    const avgResponseTime = this.calculateAverageResponseTime(recentSnapshots);
    if (avgResponseTime > this.thresholds.responseTime.fair) {
      recommendations.push('Consider implementing response caching for frequently accessed data');
      recommendations.push('Optimize database queries and add appropriate indexes');
    }

    // Cache recommendations
    if (this.systemMetrics.cacheHitRate < this.thresholds.cacheHitRate.fair) {
      recommendations.push('Improve cache hit rate by optimizing cache keys and TTL values');
    }

    // Memory recommendations
    if (this.systemMetrics.memoryUsage > this.thresholds.memoryUsage.fair) {
      recommendations.push('Consider implementing memory optimization and garbage collection tuning');
    }

    return recommendations;
  }

  private getSystemHealthScore(): number {
    let score = 100;

    // Response time impact
    if (this.systemMetrics.avgResponseTime > this.thresholds.responseTime.poor) {
      score -= 30;
    } else if (this.systemMetrics.avgResponseTime > this.thresholds.responseTime.fair) {
      score -= 15;
    }

    // Error rate impact
    const errorRate = (this.systemMetrics.failedRequests / this.systemMetrics.totalRequests) * 100;
    if (errorRate > 5) {
      score -= 25;
    } else if (errorRate > 2) {
      score -= 10;
    }

    // Memory usage impact
    if (this.systemMetrics.memoryUsage > this.thresholds.memoryUsage.poor) {
      score -= 20;
    } else if (this.systemMetrics.memoryUsage > this.thresholds.memoryUsage.fair) {
      score -= 10;
    }

    return Math.max(0, score);
  }

  private getActiveAlerts(): Alert[] {
    const alerts: Alert[] = [];
    const recentSnapshots = this.getRecentSnapshots(5);

    // High response time alert
    const avgResponseTime = this.calculateAverageResponseTime(recentSnapshots);
    if (avgResponseTime > this.thresholds.responseTime.poor) {
      alerts.push({
        type: 'performance',
        severity: 'high',
        message: `High average response time: ${avgResponseTime.toFixed(0)}ms`,
        timestamp: new Date(),
      });
    }

    // High error rate alert
    const errorRate = this.calculateErrorRate(recentSnapshots);
    if (errorRate > 5) {
      alerts.push({
        type: 'reliability',
        severity: errorRate > 10 ? 'critical' : 'high',
        message: `High error rate: ${errorRate.toFixed(1)}%`,
        timestamp: new Date(),
      });
    }

    return alerts;
  }

  // Utility calculation methods
  private calculateAverageResponseTime(snapshots: PerformanceSnapshot[]): number {
    if (snapshots.length === 0) return 0;
    return snapshots.reduce((sum, s) => sum + s.responseTime, 0) / snapshots.length;
  }

  private calculateThroughput(snapshots: PerformanceSnapshot[]): number {
    return snapshots.length; // Requests per time period
  }

  private calculateErrorRate(snapshots: PerformanceSnapshot[]): number {
    if (snapshots.length === 0) return 0;
    const errors = snapshots.filter(s => !s.success).length;
    return (errors / snapshots.length) * 100;
  }

  private calculateSuccessRate(snapshots: PerformanceSnapshot[]): number {
    return 100 - this.calculateErrorRate(snapshots);
  }

  private calculatePercentile(snapshots: PerformanceSnapshot[], percentile: number): number {
    if (snapshots.length === 0) return 0;
    
    const sorted = snapshots.map(s => s.responseTime).sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index];
  }

  private groupSnapshotsByHour(snapshots: PerformanceSnapshot[]): Array<{ hour: Date; snapshots: PerformanceSnapshot[] }> {
    const groups = new Map<string, PerformanceSnapshot[]>();
    
    snapshots.forEach(snapshot => {
      const hour = new Date(snapshot.timestamp);
      hour.setMinutes(0, 0, 0);
      const key = hour.toISOString();
      
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(snapshot);
    });

    return Array.from(groups.entries()).map(([hourStr, snapshots]) => ({
      hour: new Date(hourStr),
      snapshots,
    }));
  }

  private groupSnapshotsByOperation(snapshots: PerformanceSnapshot[]): Record<string, PerformanceSnapshot[]> {
    const groups: Record<string, PerformanceSnapshot[]> = {};
    
    snapshots.forEach(snapshot => {
      if (!groups[snapshot.operation]) {
        groups[snapshot.operation] = [];
      }
      groups[snapshot.operation].push(snapshot);
    });

    return groups;
  }

  private getOverallSystemStatus(): 'healthy' | 'degraded' | 'critical' {
    const healthScore = this.getSystemHealthScore();
    
    if (healthScore >= 80) return 'healthy';
    if (healthScore >= 60) return 'degraded';
    return 'critical';
  }

  private getCachePerformanceMetrics(): any {
    // This would integrate with the actual cache service
    return {
      hitRate: this.systemMetrics.cacheHitRate,
      size: 0,
      evictions: 0,
    };
  }

  private getSystemResourceMetrics(): any {
    const memUsage = process.memoryUsage();
    return {
      memoryUsage: memUsage.heapUsed / 1024 / 1024, // MB
      cpuUsage: process.cpuUsage(),
      uptime: process.uptime(),
    };
  }

  private calculateUptime(): number {
    return process.uptime();
  }

  private calculatePeriodMetrics(snapshots: PerformanceSnapshot[]): PeriodMetrics {
    return {
      totalRequests: snapshots.length,
      avgResponseTime: this.calculateAverageResponseTime(snapshots),
      successRate: this.calculateSuccessRate(snapshots),
      throughput: snapshots.length,
    };
  }

  private calculateMetricChanges(current: PerformanceSnapshot[], previous: PerformanceSnapshot[]): MetricChanges {
    const currentMetrics = this.calculatePeriodMetrics(current);
    const previousMetrics = this.calculatePeriodMetrics(previous);

    return {
      responseTime: this.calculatePercentageChange(currentMetrics.avgResponseTime, previousMetrics.avgResponseTime),
      throughput: this.calculatePercentageChange(currentMetrics.throughput, previousMetrics.throughput),
      successRate: this.calculatePercentageChange(currentMetrics.successRate, previousMetrics.successRate),
    };
  }

  private calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  }

  private generatePerformanceInsights(current: PerformanceSnapshot[], previous: PerformanceSnapshot[]): string[] {
    const insights: string[] = [];
    const changes = this.calculateMetricChanges(current, previous);

    if (changes.responseTime > 20) {
      insights.push('Response time has increased significantly');
    } else if (changes.responseTime < -20) {
      insights.push('Response time has improved significantly');
    }

    if (changes.throughput > 50) {
      insights.push('System throughput has increased substantially');
    } else if (changes.throughput < -30) {
      insights.push('System throughput has decreased');
    }

    return insights;
  }

  private calculateOperationTrends(snapshots: PerformanceSnapshot[]): TrendPoint[] {
    const hourlyGroups = this.groupSnapshotsByHour(snapshots);
    return hourlyGroups.map(group => ({
      timestamp: group.hour,
      value: this.calculateAverageResponseTime(group.snapshots),
    }));
  }

  private generateOperationRecommendations(snapshots: PerformanceSnapshot[]): string[] {
    const recommendations: string[] = [];
    const avgResponseTime = this.calculateAverageResponseTime(snapshots);
    const errorRate = this.calculateErrorRate(snapshots);

    if (avgResponseTime > 1000) {
      recommendations.push('Consider adding caching for this operation');
      recommendations.push('Optimize database queries and indexes');
    }

    if (errorRate > 5) {
      recommendations.push('Improve error handling and validation');
      recommendations.push('Add retry logic for transient failures');
    }

    return recommendations;
  }
}

// Supporting interfaces
interface PerformanceSnapshot {
  timestamp: Date;
  operation: string;
  responseTime: number;
  success: boolean;
  metadata?: any;
}

interface PerformanceAnalytics {
  overview: PerformanceOverview;
  trends: PerformanceTrends;
  bottlenecks: Bottleneck[];
  recommendations: string[];
  systemHealth: number;
  alerts: Alert[];
}

interface PerformanceOverview {
  totalRequests: number;
  successRate: number;
  avgResponseTime: number;
  peakResponseTime: number;
  systemLoad: number;
  memoryUsage: number;
}

interface PerformanceTrends {
  responseTime: TrendPoint[];
  throughput: TrendPoint[];
  errorRate: TrendPoint[];
}

interface TrendPoint {
  timestamp: Date;
  value: number;
}

interface Bottleneck {
  type: string;
  operation: string;
  severity: 'low' | 'medium' | 'high';
  description: string;
  recommendation: string;
}

interface Alert {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
}

interface SystemHealthDashboard {
  status: 'healthy' | 'degraded' | 'critical';
  metrics: {
    responseTime: number;
    throughput: number;
    errorRate: number;
    cachePerformance: any;
    systemResources: any;
  };
  alerts: Alert[];
  uptime: number;
  lastUpdated: Date;
}

interface PerformanceComparison {
  current: PeriodMetrics;
  previous: PeriodMetrics;
  changes: MetricChanges;
  insights: string[];
}

interface PeriodMetrics {
  totalRequests: number;
  avgResponseTime: number;
  successRate: number;
  throughput: number;
}

interface MetricChanges {
  responseTime: number;
  throughput: number;
  successRate: number;
}

interface OperationMetrics {
  operation: string;
  totalRequests: number;
  avgResponseTime: number;
  successRate: number;
  p95ResponseTime?: number;
  p99ResponseTime?: number;
  trends: TrendPoint[];
  recommendations: string[];
}
