import { Injectable, Logger } from '@nestjs/common';
import { ListEntitiesDto } from '../../entities/dto/list-entities.dto';

/**
 * Database Query Optimization Service
 * 
 * Provides intelligent query optimization for the enhanced filtering system:
 * - Query plan optimization
 * - Index usage optimization
 * - Filter ordering for performance
 * - Batch query optimization
 * - Connection pooling optimization
 */
@Injectable()
export class QueryOptimizationService {
  private readonly logger = new Logger(QueryOptimizationService.name);

  // Query performance tracking
  private readonly queryMetrics = new Map<string, {
    count: number;
    totalTime: number;
    avgTime: number;
    lastExecuted: Date;
  }>();

  /**
   * Optimize filter order for best query performance
   */
  optimizeFilterOrder(filters: ListEntitiesDto): ListEntitiesDto {
    const optimized = { ...filters };

    // Reorder filters based on selectivity (most selective first)
    const filterSelectivity = this.calculateFilterSelectivity(filters);
    
    this.logger.debug('Filter selectivity analysis', filterSelectivity);

    // Apply optimizations based on selectivity
    return this.applySelectivityOptimizations(optimized, filterSelectivity);
  }

  /**
   * Generate optimized query hints for complex filters
   */
  generateQueryHints(filters: ListEntitiesDto): QueryHints {
    const hints: QueryHints = {
      useIndex: [],
      joinOrder: [],
      filterStrategy: 'default',
      estimatedComplexity: 'low',
    };

    // Analyze filter complexity
    const complexity = this.analyzeQueryComplexity(filters);
    hints.estimatedComplexity = complexity;

    // Suggest optimal indexes
    hints.useIndex = this.suggestOptimalIndexes(filters);

    // Determine best filter strategy
    hints.filterStrategy = this.determineFilterStrategy(filters, complexity);

    // Optimize join order for multi-table queries
    hints.joinOrder = this.optimizeJoinOrder(filters);

    return hints;
  }

  /**
   * Optimize batch queries for multiple filter sets
   */
  optimizeBatchQueries(filterSets: ListEntitiesDto[]): BatchQueryPlan {
    const plan: BatchQueryPlan = {
      batches: [],
      estimatedTime: 0,
      cacheStrategy: 'none',
    };

    // Group similar queries together
    const groupedQueries = this.groupSimilarQueries(filterSets);
    
    // Create optimized batches
    plan.batches = groupedQueries.map(group => ({
      filters: group,
      priority: this.calculateBatchPriority(group),
      estimatedTime: this.estimateBatchTime(group),
    }));

    // Sort batches by priority
    plan.batches.sort((a, b) => b.priority - a.priority);

    // Calculate total estimated time
    plan.estimatedTime = plan.batches.reduce((sum, batch) => sum + batch.estimatedTime, 0);

    // Determine cache strategy
    plan.cacheStrategy = this.determineCacheStrategy(filterSets);

    return plan;
  }

  /**
   * Monitor and record query performance
   */
  recordQueryPerformance(querySignature: string, executionTime: number): void {
    const existing = this.queryMetrics.get(querySignature);
    
    if (existing) {
      existing.count++;
      existing.totalTime += executionTime;
      existing.avgTime = existing.totalTime / existing.count;
      existing.lastExecuted = new Date();
    } else {
      this.queryMetrics.set(querySignature, {
        count: 1,
        totalTime: executionTime,
        avgTime: executionTime,
        lastExecuted: new Date(),
      });
    }

    // Log slow queries
    if (executionTime > 1000) {
      this.logger.warn(`Slow query detected: ${querySignature} (${executionTime}ms)`);
    }
  }

  /**
   * Get query performance insights
   */
  getQueryPerformanceInsights(): QueryPerformanceInsights {
    const metrics = Array.from(this.queryMetrics.entries()).map(([signature, data]) => ({
      signature,
      ...data,
    }));

    // Sort by average execution time (slowest first)
    const slowestQueries = metrics
      .sort((a, b) => b.avgTime - a.avgTime)
      .slice(0, 10);

    // Find most frequent queries
    const mostFrequentQueries = metrics
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calculate overall statistics
    const totalQueries = metrics.reduce((sum, m) => sum + m.count, 0);
    const avgResponseTime = metrics.reduce((sum, m) => sum + m.avgTime, 0) / metrics.length;

    return {
      totalQueries,
      avgResponseTime,
      slowestQueries,
      mostFrequentQueries,
      recommendations: this.generateOptimizationRecommendations(metrics),
    };
  }

  // Private helper methods
  private calculateFilterSelectivity(filters: ListEntitiesDto): FilterSelectivity {
    const selectivity: FilterSelectivity = {};

    // Entity type filter (highly selective)
    if (filters.entityTypeIds?.length) {
      selectivity.entityTypeIds = filters.entityTypeIds.length === 1 ? 0.1 : 0.3;
    }

    // Search term (moderately selective)
    if (filters.searchTerm) {
      selectivity.searchTerm = 0.2;
    }

    // Category filters (moderately selective)
    if (filters.categoryIds?.length) {
      selectivity.categoryIds = 0.25;
    }

    // Technical level (less selective)
    if (filters.technical_levels?.length) {
      selectivity.technical_levels = 0.4;
    }

    // Boolean filters (varies)
    if (filters.has_free_tier !== undefined) {
      selectivity.has_free_tier = 0.5; // Roughly 50/50 split
    }

    if (filters.has_api !== undefined) {
      selectivity.has_api = 0.3; // Fewer entities have APIs
    }

    // Price range (moderately selective)
    if (filters.price_range) {
      selectivity.price_range = 0.35;
    }

    return selectivity;
  }

  private applySelectivityOptimizations(
    filters: ListEntitiesDto,
    selectivity: FilterSelectivity,
  ): ListEntitiesDto {
    // This would be used to reorder WHERE clauses in the actual query
    // For now, we'll just return the filters as-is since we can't add arbitrary properties
    // The optimization hints would be used internally by the query builder
    return filters;
  }

  private analyzeQueryComplexity(filters: ListEntitiesDto): QueryComplexity {
    let complexity = 0;

    // Count active filters
    const activeFilters = Object.values(filters).filter(v => 
      v !== undefined && v !== null && v !== ''
    ).length;

    complexity += activeFilters;

    // Array filters add complexity
    if (filters.entityTypeIds && filters.entityTypeIds.length > 1) complexity += 1;
    if (filters.categoryIds && filters.categoryIds.length > 1) complexity += 1;
    if (filters.technical_levels && filters.technical_levels.length > 1) complexity += 1;

    // Text search adds complexity
    if (filters.searchTerm) complexity += 2;

    // Range queries add complexity
    if (filters.price_min || filters.price_max) complexity += 1;
    if (filters.salary_min || filters.salary_max) complexity += 1;

    if (complexity <= 3) return 'low';
    if (complexity <= 6) return 'medium';
    if (complexity <= 10) return 'high';
    return 'very_high';
  }

  private suggestOptimalIndexes(filters: ListEntitiesDto): string[] {
    const indexes: string[] = [];

    if (filters.entityTypeIds) {
      indexes.push('idx_entities_entity_type_id');
    }

    if (filters.searchTerm) {
      indexes.push('idx_entities_fts_document');
    }

    if (filters.categoryIds) {
      indexes.push('idx_entity_categories_category_id');
    }

    if (filters.has_free_tier !== undefined) {
      indexes.push('idx_entities_has_free_tier');
    }

    if (filters.technical_levels) {
      indexes.push('idx_entity_details_technical_level');
    }

    return indexes;
  }

  private determineFilterStrategy(
    filters: ListEntitiesDto,
    complexity: QueryComplexity,
  ): FilterStrategy {
    if (complexity === 'low') {
      return 'simple_where';
    }

    if (filters.searchTerm && complexity === 'medium') {
      return 'fts_first';
    }

    if (complexity === 'high' || complexity === 'very_high') {
      return 'staged_filtering';
    }

    return 'default';
  }

  private optimizeJoinOrder(filters: ListEntitiesDto): string[] {
    const joins: string[] = ['entities']; // Base table

    // Add joins in order of selectivity
    if (filters.entityTypeIds) {
      joins.push('entity_types');
    }

    if (filters.categoryIds) {
      joins.push('entity_categories', 'categories');
    }

    if (filters.technical_levels || filters.skill_levels) {
      joins.push('entity_details');
    }

    return joins;
  }

  private groupSimilarQueries(filterSets: ListEntitiesDto[]): ListEntitiesDto[][] {
    // Group queries by similarity (simplified implementation)
    const groups: ListEntitiesDto[][] = [];
    
    filterSets.forEach(filters => {
      const existingGroup = groups.find(group => 
        this.queriesAreSimilar(group[0], filters)
      );
      
      if (existingGroup) {
        existingGroup.push(filters);
      } else {
        groups.push([filters]);
      }
    });

    return groups;
  }

  private queriesAreSimilar(a: ListEntitiesDto, b: ListEntitiesDto): boolean {
    // Simple similarity check based on entity types and search terms
    const aEntityTypes = a.entityTypeIds?.join(',') || '';
    const bEntityTypes = b.entityTypeIds?.join(',') || '';
    
    return aEntityTypes === bEntityTypes && 
           (a.searchTerm || '') === (b.searchTerm || '');
  }

  private calculateBatchPriority(group: ListEntitiesDto[]): number {
    // Higher priority for larger groups and simpler queries
    let priority = group.length * 10;
    
    const complexity = this.analyzeQueryComplexity(group[0]);
    switch (complexity) {
      case 'low': priority += 20; break;
      case 'medium': priority += 10; break;
      case 'high': priority += 5; break;
      case 'very_high': priority += 0; break;
    }

    return priority;
  }

  private estimateBatchTime(group: ListEntitiesDto[]): number {
    // Estimate execution time based on complexity and group size
    const baseTime = 100; // Base 100ms per query
    const complexity = this.analyzeQueryComplexity(group[0]);
    
    let multiplier = 1;
    switch (complexity) {
      case 'low': multiplier = 1; break;
      case 'medium': multiplier = 1.5; break;
      case 'high': multiplier = 2; break;
      case 'very_high': multiplier = 3; break;
    }

    return baseTime * multiplier * group.length;
  }

  private determineCacheStrategy(filterSets: ListEntitiesDto[]): CacheStrategy {
    const uniqueQueries = new Set(filterSets.map(f => JSON.stringify(f))).size;
    const totalQueries = filterSets.length;
    
    const duplicateRatio = (totalQueries - uniqueQueries) / totalQueries;
    
    if (duplicateRatio > 0.5) {
      return 'aggressive';
    } else if (duplicateRatio > 0.2) {
      return 'moderate';
    } else {
      return 'minimal';
    }
  }

  private generateOptimizationRecommendations(
    metrics: Array<{ signature: string; count: number; avgTime: number }>,
  ): string[] {
    const recommendations: string[] = [];

    // Find slow queries
    const slowQueries = metrics.filter(m => m.avgTime > 500);
    if (slowQueries.length > 0) {
      recommendations.push(`Consider optimizing ${slowQueries.length} slow queries`);
    }

    // Find frequent queries
    const frequentQueries = metrics.filter(m => m.count > 100);
    if (frequentQueries.length > 0) {
      recommendations.push(`Consider caching results for ${frequentQueries.length} frequent queries`);
    }

    // General recommendations
    if (metrics.length > 50) {
      recommendations.push('Consider implementing query result caching');
    }

    return recommendations;
  }
}

// Supporting interfaces
interface FilterSelectivity {
  [key: string]: number; // 0-1, where 0 is most selective
}

interface QueryHints {
  useIndex: string[];
  joinOrder: string[];
  filterStrategy: FilterStrategy;
  estimatedComplexity: QueryComplexity;
}

interface BatchQueryPlan {
  batches: Array<{
    filters: ListEntitiesDto[];
    priority: number;
    estimatedTime: number;
  }>;
  estimatedTime: number;
  cacheStrategy: CacheStrategy;
}

interface QueryPerformanceInsights {
  totalQueries: number;
  avgResponseTime: number;
  slowestQueries: Array<{
    signature: string;
    count: number;
    avgTime: number;
  }>;
  mostFrequentQueries: Array<{
    signature: string;
    count: number;
    avgTime: number;
  }>;
  recommendations: string[];
}

type QueryComplexity = 'low' | 'medium' | 'high' | 'very_high';
type FilterStrategy = 'simple_where' | 'fts_first' | 'staged_filtering' | 'default';
type CacheStrategy = 'aggressive' | 'moderate' | 'minimal' | 'none';
