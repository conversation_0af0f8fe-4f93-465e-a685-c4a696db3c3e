import { Injectable, Logger } from '@nestjs/common';
import { EnhancedUserIntent } from '../llm/interfaces/llm.service.interface';

/**
 * Advanced Entity Ranking Service
 * 
 * Provides sophisticated multi-factor ranking for entities based on:
 * - Vector similarity scores
 * - Filter match precision
 * - User preference alignment
 * - Entity quality metrics
 * - Contextual relevance
 * - Temporal factors
 * - Social proof indicators
 */
@Injectable()
export class AdvancedEntityRankingService {
  private readonly logger = new Logger(AdvancedEntityRankingService.name);

  /**
   * Rank entities using advanced multi-factor scoring
   */
  rankEntities(
    entities: any[],
    context: RankingContext,
  ): RankedEntity[] {
    this.logger.debug(`Ranking ${entities.length} entities with advanced scoring`);

    const rankedEntities = entities.map(entity => {
      const scores = this.calculateComprehensiveScores(entity, context);
      const finalScore = this.calculateWeightedFinalScore(scores, context);

      return {
        ...entity,
        rankingScore: finalScore,
        rankingBreakdown: scores,
        rankingReason: this.generateRankingReason(scores, context),
      };
    });

    // Sort by final score (highest first)
    const sorted = rankedEntities.sort((a, b) => b.rankingScore - a.rankingScore);

    this.logger.debug('Entity ranking completed', {
      topScore: sorted[0]?.rankingScore,
      averageScore: sorted.reduce((sum, e) => sum + e.rankingScore, 0) / sorted.length,
      entitiesRanked: sorted.length,
    });

    return sorted;
  }

  /**
   * Calculate comprehensive scoring across all factors
   */
  private calculateComprehensiveScores(entity: any, context: RankingContext): EntityScores {
    return {
      // Core relevance (40% weight)
      vectorSimilarity: this.calculateVectorSimilarityScore(entity, context),
      filterMatch: this.calculateFilterMatchScore(entity, context),
      
      // Quality indicators (25% weight)
      entityQuality: this.calculateEntityQualityScore(entity),
      socialProof: this.calculateSocialProofScore(entity),
      
      // User alignment (20% weight)
      userPreference: this.calculateUserPreferenceScore(entity, context),
      personalizedRelevance: this.calculatePersonalizedRelevanceScore(entity, context),
      
      // Contextual factors (10% weight)
      recency: this.calculateRecencyScore(entity),
      popularity: this.calculatePopularityScore(entity),
      
      // Advanced factors (5% weight)
      diversityBonus: this.calculateDiversityBonus(entity, context),
      trendingBonus: this.calculateTrendingBonus(entity),
    };
  }

  /**
   * Vector similarity score (from semantic search)
   */
  private calculateVectorSimilarityScore(entity: any, context: RankingContext): number {
    // Use existing similarity score from vector search, normalized to 0-1
    return Math.min(1, Math.max(0, entity.similarity || 0.5));
  }

  /**
   * Filter match precision score
   */
  private calculateFilterMatchScore(entity: any, context: RankingContext): number {
    if (!context.appliedFilters || Object.keys(context.appliedFilters).length === 0) {
      return 0.5; // Neutral score when no filters
    }

    let matchCount = 0;
    let totalFilters = 0;
    let weightedScore = 0;

    Object.entries(context.appliedFilters).forEach(([filterKey, filterValue]) => {
      if (filterValue === undefined || filterValue === null) return;

      totalFilters++;
      const confidence = context.filterConfidence?.[filterKey] || 0.7;
      
      if (this.entityMatchesFilter(entity, filterKey, filterValue)) {
        matchCount++;
        weightedScore += confidence;
      }
    });

    if (totalFilters === 0) return 0.5;

    // Combine match ratio with confidence-weighted score
    const matchRatio = matchCount / totalFilters;
    const avgConfidenceScore = weightedScore / totalFilters;
    
    return (matchRatio * 0.7) + (avgConfidenceScore * 0.3);
  }

  /**
   * Entity quality score based on intrinsic metrics
   */
  private calculateEntityQualityScore(entity: any): number {
    let score = 0.5; // Base score

    // Rating quality (40% of quality score)
    if (entity.avgRating) {
      score += (entity.avgRating / 5) * 0.4;
    }

    // Review volume (30% of quality score)
    if (entity.reviewCount) {
      const reviewScore = Math.min(1, Math.log(entity.reviewCount + 1) / 10);
      score += reviewScore * 0.3;
    }

    // Content completeness (20% of quality score)
    const completeness = this.calculateContentCompleteness(entity);
    score += completeness * 0.2;

    // Verification status (10% of quality score)
    if (entity.isVerified) {
      score += 0.1;
    }

    return Math.min(1, score);
  }

  /**
   * Social proof indicators
   */
  private calculateSocialProofScore(entity: any): number {
    let score = 0;

    // GitHub stars for tools
    if (entity.githubStars) {
      score += Math.min(0.3, Math.log(entity.githubStars + 1) / 30);
    }

    // Download/usage metrics
    if (entity.downloadCount || entity.usageCount) {
      const usage = entity.downloadCount || entity.usageCount;
      score += Math.min(0.3, Math.log(usage + 1) / 25);
    }

    // Community engagement
    if (entity.communitySize) {
      score += Math.min(0.2, Math.log(entity.communitySize + 1) / 20);
    }

    // Media mentions
    if (entity.mediaMentions) {
      score += Math.min(0.2, entity.mediaMentions / 50);
    }

    return Math.min(1, score);
  }

  /**
   * User preference alignment
   */
  private calculateUserPreferenceScore(entity: any, context: RankingContext): number {
    if (!context.userPreferences) return 0.5;

    let score = 0.5;
    const prefs = context.userPreferences;

    // Preferred categories
    if (prefs.preferred_categories?.length > 0) {
      const entityCategories = entity.categories?.map((c: any) => c.category.name) || [];
      const hasPreferredCategory = prefs.preferred_categories.some((pref: string) =>
        entityCategories.includes(pref)
      );
      if (hasPreferredCategory) score += 0.3;
    }

    // Excluded categories (penalty)
    if (prefs.excluded_categories?.length > 0) {
      const entityCategories = entity.categories?.map((c: any) => c.category.name) || [];
      const hasExcludedCategory = prefs.excluded_categories.some((excl: string) =>
        entityCategories.includes(excl)
      );
      if (hasExcludedCategory) score -= 0.4;
    }

    // Technical level alignment
    if (prefs.technical_level && entity.technicalLevel) {
      const levelMatch = this.calculateTechnicalLevelAlignment(
        prefs.technical_level,
        entity.technicalLevel
      );
      score += levelMatch * 0.2;
    }

    // Budget alignment
    if (prefs.budget && entity.pricing) {
      const budgetMatch = this.calculateBudgetAlignment(prefs.budget, entity.pricing);
      score += budgetMatch * 0.2;
    }

    return Math.max(0, Math.min(1, score));
  }

  /**
   * Personalized relevance based on user history
   */
  private calculatePersonalizedRelevanceScore(entity: any, context: RankingContext): number {
    let score = 0.5;

    // Previously viewed similar entities
    if (context.userHistory?.viewedEntities) {
      const similarViewed = this.findSimilarViewedEntities(entity, context.userHistory.viewedEntities);
      score += Math.min(0.3, similarViewed.length * 0.1);
    }

    // User's skill progression
    if (context.userHistory?.skillProgression) {
      const progressionMatch = this.calculateSkillProgressionMatch(entity, context.userHistory.skillProgression);
      score += progressionMatch * 0.2;
    }

    // Collaborative filtering (users with similar preferences)
    if (context.collaborativeSignals) {
      score += this.calculateCollaborativeScore(entity, context.collaborativeSignals) * 0.3;
    }

    return Math.min(1, score);
  }

  /**
   * Recency score for time-sensitive content
   */
  private calculateRecencyScore(entity: any): number {
    if (!entity.updatedAt && !entity.createdAt) return 0.5;

    const entityDate = new Date(entity.updatedAt || entity.createdAt);
    const now = new Date();
    const daysSinceUpdate = (now.getTime() - entityDate.getTime()) / (1000 * 60 * 60 * 24);

    // Decay function: newer is better, but levels off
    if (daysSinceUpdate < 30) return 1.0;
    if (daysSinceUpdate < 90) return 0.8;
    if (daysSinceUpdate < 180) return 0.6;
    if (daysSinceUpdate < 365) return 0.4;
    return 0.2;
  }

  /**
   * Popularity score based on recent engagement
   */
  private calculatePopularityScore(entity: any): number {
    let score = 0;

    // Recent views/downloads
    if (entity.recentViews) {
      score += Math.min(0.4, Math.log(entity.recentViews + 1) / 15);
    }

    // Trending indicators
    if (entity.trendingScore) {
      score += Math.min(0.3, entity.trendingScore);
    }

    // Search frequency
    if (entity.searchFrequency) {
      score += Math.min(0.3, entity.searchFrequency / 100);
    }

    return Math.min(1, score);
  }

  /**
   * Diversity bonus to avoid echo chambers
   */
  private calculateDiversityBonus(entity: any, context: RankingContext): number {
    if (!context.currentResults || context.currentResults.length === 0) return 0;

    // Check if this entity adds diversity to current results
    const entityType = entity.entityType?.slug;
    const entityCategory = entity.categories?.[0]?.category?.name;

    const typeCount = context.currentResults.filter(r => r.entityType?.slug === entityType).length;
    const categoryCount = context.currentResults.filter(r => 
      r.categories?.[0]?.category?.name === entityCategory
    ).length;

    // Bonus for underrepresented types/categories
    let bonus = 0;
    if (typeCount === 0) bonus += 0.3; // First of this type
    else if (typeCount === 1) bonus += 0.1; // Second of this type

    if (categoryCount === 0) bonus += 0.2; // First of this category
    else if (categoryCount === 1) bonus += 0.05; // Second of this category

    return Math.min(0.5, bonus);
  }

  /**
   * Trending bonus for hot/emerging entities
   */
  private calculateTrendingBonus(entity: any): number {
    let bonus = 0;

    // Growth rate in popularity
    if (entity.growthRate && entity.growthRate > 1.2) {
      bonus += Math.min(0.3, (entity.growthRate - 1) * 0.5);
    }

    // Featured/highlighted status
    if (entity.isFeatured) bonus += 0.2;
    if (entity.isEditorChoice) bonus += 0.15;
    if (entity.isNewRelease) bonus += 0.1;

    return Math.min(0.5, bonus);
  }

  /**
   * Calculate weighted final score
   */
  private calculateWeightedFinalScore(scores: EntityScores, context: RankingContext): number {
    const weights = context.customWeights || {
      // Core relevance (40%)
      vectorSimilarity: 0.25,
      filterMatch: 0.15,
      
      // Quality indicators (25%)
      entityQuality: 0.15,
      socialProof: 0.10,
      
      // User alignment (20%)
      userPreference: 0.12,
      personalizedRelevance: 0.08,
      
      // Contextual factors (10%)
      recency: 0.05,
      popularity: 0.05,
      
      // Advanced factors (5%)
      diversityBonus: 0.03,
      trendingBonus: 0.02,
    };

    let finalScore = 0;
    Object.entries(weights).forEach(([factor, weight]) => {
      finalScore += (scores[factor as keyof EntityScores] || 0) * weight;
    });

    return Math.max(0, Math.min(1, finalScore));
  }

  // Helper methods
  private calculateContentCompleteness(entity: any): number {
    let completeness = 0;
    const fields = ['name', 'description', 'shortDescription', 'website', 'pricing'];
    
    fields.forEach(field => {
      if (entity[field] && entity[field].length > 0) {
        completeness += 0.2;
      }
    });

    return completeness;
  }

  private entityMatchesFilter(entity: any, filterKey: string, filterValue: any): boolean {
    // Comprehensive filter matching logic
    switch (filterKey) {
      case 'entityTypeIds':
        return Array.isArray(filterValue) && filterValue.includes(entity.entityType?.slug);
      case 'technical_levels':
        return Array.isArray(filterValue) && entity.technicalLevel && 
               filterValue.includes(entity.technicalLevel);
      case 'has_free_tier':
        return entity.hasFreeTier === filterValue;
      case 'has_api':
        return entity.hasApi === filterValue;
      case 'platforms':
        return Array.isArray(filterValue) && entity.platforms &&
               filterValue.some(p => entity.platforms.includes(p));
      case 'price_ranges':
        return Array.isArray(filterValue) && entity.priceRange &&
               filterValue.includes(entity.priceRange);
      default:
        return false;
    }
  }

  private calculateTechnicalLevelAlignment(userLevel: string, entityLevel: string): number {
    const levels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
    const userIndex = levels.indexOf(userLevel);
    const entityIndex = levels.indexOf(entityLevel);
    
    if (userIndex === -1 || entityIndex === -1) return 0;
    
    const distance = Math.abs(userIndex - entityIndex);
    return Math.max(0, 1 - (distance * 0.3));
  }

  private calculateBudgetAlignment(userBudget: string, entityPricing: any): number {
    // Simplified budget alignment logic
    if (userBudget === 'free' && entityPricing?.hasFreeTier) return 1;
    if (userBudget === 'low' && entityPricing?.priceRange === 'LOW') return 1;
    if (userBudget === 'medium' && ['LOW', 'MEDIUM'].includes(entityPricing?.priceRange)) return 0.8;
    return 0.3;
  }

  private findSimilarViewedEntities(entity: any, viewedEntities: any[]): any[] {
    return viewedEntities.filter(viewed => 
      viewed.entityType?.slug === entity.entityType?.slug ||
      viewed.categories?.some((c: any) => 
        entity.categories?.some((ec: any) => ec.category.name === c.category.name)
      )
    );
  }

  private calculateSkillProgressionMatch(entity: any, skillProgression: any): number {
    // Match entity difficulty with user's skill progression
    if (!skillProgression.currentLevel || !entity.technicalLevel) return 0;
    
    return this.calculateTechnicalLevelAlignment(skillProgression.currentLevel, entity.technicalLevel);
  }

  private calculateCollaborativeScore(entity: any, collaborativeSignals: any): number {
    // Users with similar preferences also liked this entity
    return Math.min(1, (collaborativeSignals.similarUserLikes || 0) / 10);
  }

  private generateRankingReason(scores: EntityScores, context: RankingContext): string {
    const reasons: string[] = [];

    if (scores.vectorSimilarity > 0.8) reasons.push('highly relevant to your query');
    if (scores.filterMatch > 0.8) reasons.push('matches all your criteria');
    if (scores.entityQuality > 0.8) reasons.push('excellent quality and ratings');
    if (scores.userPreference > 0.7) reasons.push('aligns with your preferences');
    if (scores.socialProof > 0.7) reasons.push('popular in the community');
    if (scores.recency > 0.8) reasons.push('recently updated');
    if (scores.diversityBonus > 0.2) reasons.push('adds variety to recommendations');

    return reasons.length > 0 ? reasons.join(', ') : 'good overall match';
  }
}

// Supporting interfaces
export interface RankingContext {
  appliedFilters?: Record<string, any>;
  filterConfidence?: Record<string, number>;
  userPreferences?: any;
  userHistory?: {
    viewedEntities?: any[];
    skillProgression?: any;
  };
  collaborativeSignals?: any;
  currentResults?: any[];
  customWeights?: Record<string, number>;
  intent?: EnhancedUserIntent;
}

export interface EntityScores {
  vectorSimilarity: number;
  filterMatch: number;
  entityQuality: number;
  socialProof: number;
  userPreference: number;
  personalizedRelevance: number;
  recency: number;
  popularity: number;
  diversityBonus: number;
  trendingBonus: number;
}

export interface RankedEntity {
  rankingScore: number;
  rankingBreakdown: EntityScores;
  rankingReason: string;
  [key: string]: any; // Original entity properties
}
