import { Injectable, ExecutionContext } from '@nestjs/common';
import { ThrottlerGuard, ThrottlerModuleOptions, ThrottlerStorage } from '@nestjs/throttler';
import { Reflector } from '@nestjs/core';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class AdminThrottlerGuard extends ThrottlerGuard {
  constructor(
    options: ThrottlerModuleOptions,
    storageService: ThrottlerStorage,
    reflector: Reflector,
    private readonly prismaService: PrismaService,
  ) {
    super(options, storageService, reflector);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    // Check if this is an entities endpoint (POST or PATCH)
    const isEntitiesEndpoint = request.route?.path?.includes('/entities') && 
                              (request.method === 'POST' || request.method === 'PATCH');
    
    if (isEntitiesEndpoint && request.user) {
      try {
        // Check if user is admin
        const user = await this.prismaService.user.findUnique({
          where: { id: request.user.id },
          select: { role: true },
        });
        
        // If user is admin, bypass throttling
        if (user?.role === 'ADMIN') {
          return true;
        }
      } catch (error) {
        // If there's an error checking user role, fall back to normal throttling
        console.error('Error checking user role for throttling:', error);
      }
    }
    
    // For non-admin users or non-entities endpoints, apply normal throttling
    return super.canActivate(context);
  }
}
