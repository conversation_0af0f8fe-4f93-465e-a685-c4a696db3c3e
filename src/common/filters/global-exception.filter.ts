import { ExceptionFilter, Catch, ArgumentsHost, HttpStatus, HttpException } from '@nestjs/common';
import { Response, Request } from 'express';
import { HttpAdapterHost } from '@nestjs/core';
import { AppLoggerService } from '../logger/logger.service';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  constructor(
    private readonly httpAdapterHost: HttpAdapterHost,
    private readonly logger: AppLoggerService,
  ) {}

  catch(exception: any, host: ArgumentsHost) {
    // Add prominent logging to detect if this filter is being triggered
    this.logger.error('--- GLOBAL EXCEPTION FILTER CAUGHT AN ERROR ---');
    this.logger.error('Exception Type:', exception.constructor.name);
    this.logger.error('Exception Message:', exception.message);
    this.logger.error('Exception Details:', JSON.stringify(exception, null, 2));
    this.logger.error('Exception Stack:', exception.stack);

    // Additional detailed logging for debugging
    this.logger.error(`[GlobalExceptionFilter] Full exception object: ${exception}`);
    if (exception.cause) {
      this.logger.error(`[GlobalExceptionFilter] Exception cause: ${exception.cause}`);
    }

    const { httpAdapter } = this.httpAdapterHost;
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const correlationId = (request as any).correlationId;

    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'An unexpected internal server error occurred.';
    let errorType = 'InternalServerError';
    let errorDetails: any = {};

    if (exception instanceof HttpException) {
      statusCode = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
      } else if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
        message = (exceptionResponse as any).message || exception.message;
        errorDetails = exceptionResponse;
      }
      
      errorType = exception.constructor.name;
      
      // Log detailed information for debugging
      this.logger.error('HttpException caught:');
      this.logger.error(`CorrelationId: ${correlationId}, StatusCode: ${statusCode}, Message: ${message}, ErrorType: ${errorType}`);
      this.logger.error('ErrorDetails: ' + JSON.stringify(errorDetails));
      this.logger.error('OriginalException: ' + JSON.stringify(exception));
    } else {
      // Non-HTTP exceptions (unexpected errors)
      this.logger.error('Unexpected exception caught:');
      this.logger.error(`CorrelationId: ${correlationId}, ExceptionType: ${exception.constructor.name}, Message: ${exception.message}`);
      this.logger.error('Stack: ' + exception.stack);
      this.logger.error('OriginalException: ' + JSON.stringify(exception));
    }

    const responseBody = {
      statusCode,
      message,
      error: errorType,
      details: errorDetails,
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    httpAdapter.reply(response, responseBody, statusCode);
  }
}
