import { Test, TestingModule } from '@nestjs/testing';
import { RecommendationsService } from './recommendations.service';
import { EntitiesService } from '../entities/entities.service';
import { LlmFactoryService } from '../common/llm/services/llm-factory.service';
import { ILlmService } from '../common/llm/interfaces/llm.service.interface';

describe('RecommendationsService - Enhanced Filtering', () => {
  let service: RecommendationsService;
  let entitiesService: jest.Mocked<EntitiesService>;
  let llmService: jest.Mocked<ILlmService>;

  const mockVectorResults = [
    { id: '1', name: 'AI Tool 1', similarity: 0.9 },
    { id: '2', name: 'AI Tool 2', similarity: 0.8 },
    { id: '3', name: 'Course 1', similarity: 0.7 },
  ];

  const mockFilteredEntities = [
    {
      id: '1',
      name: 'AI Tool 1',
      shortDescription: 'Advanced AI tool',
      entityType: { name: 'AI Tool', slug: 'ai-tool' },
      avgRating: 4.5,
      reviewCount: 100,
    },
    {
      id: '3',
      name: 'Course 1',
      shortDescription: 'Beginner AI course',
      entityType: { name: 'Course', slug: 'course' },
      avgRating: 4.2,
      reviewCount: 50,
    },
  ];

  beforeEach(async () => {
    const mockEntitiesService = {
      vectorSearch: jest.fn(),
      findAll: jest.fn(),
    };

    const mockLlmService = {
      getRecommendation: jest.fn(),
    };

    const mockLlmFactoryService = {
      getAvailableProviders: jest.fn().mockReturnValue(['OPENAI']),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RecommendationsService,
        { provide: EntitiesService, useValue: mockEntitiesService },
        { provide: 'ILlmService', useValue: mockLlmService },
        { provide: LlmFactoryService, useValue: mockLlmFactoryService },
      ],
    }).compile();

    service = module.get<RecommendationsService>(RecommendationsService);
    entitiesService = module.get(EntitiesService);
    llmService = module.get('ILlmService');
  });

  describe('Enhanced Filter Integration', () => {
    it('should pass all tool-specific filters to findAll', async () => {
      // Arrange
      entitiesService.vectorSearch.mockResolvedValue(mockVectorResults);
      entitiesService.findAll.mockResolvedValue({
        data: mockFilteredEntities,
        total: 2,
        page: 1,
        limit: 20,
        totalPages: 1,
      });
      llmService.getRecommendation.mockResolvedValue({
        recommendedEntityIds: ['1'],
        explanation: 'Best tool for your needs',
      });

      const filters = {
        entityTypeIds: ['ai-tool'],
        technical_levels: ['BEGINNER', 'INTERMEDIATE'],
        has_api: true,
        has_free_tier: true,
        frameworks: ['TensorFlow', 'PyTorch'],
        platforms: ['Web', 'Linux'],
        use_cases_search: 'machine learning',
        max_candidates: 15,
      };

      // Act
      await service.getRecommendations({
        problem_description: 'I need an AI tool for machine learning',
        filters,
      });

      // Assert
      expect(entitiesService.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          entityTypeIds: ['ai-tool'],
          technical_levels: ['BEGINNER', 'INTERMEDIATE'],
          has_api: true,
          has_free_tier: true,
          frameworks: ['TensorFlow', 'PyTorch'],
          platforms: ['Web', 'Linux'],
          use_cases_search: 'machine learning',
          limit: 30, // maxCandidates * 2
          page: 1,
        })
      );
    });

    it('should pass all course-specific filters to findAll', async () => {
      // Arrange
      entitiesService.vectorSearch.mockResolvedValue(mockVectorResults);
      entitiesService.findAll.mockResolvedValue({
        data: mockFilteredEntities,
        total: 2,
        page: 1,
        limit: 20,
        totalPages: 1,
      });
      llmService.getRecommendation.mockResolvedValue({
        recommendedEntityIds: ['3'],
        explanation: 'Perfect course for beginners',
      });

      const filters = {
        entityTypeIds: ['course'],
        skill_levels: ['BEGINNER'],
        certificate_available: true,
        instructor_name: 'Andrew Ng',
        duration_text: '6 weeks',
        prerequisites: 'basic programming',
        max_candidates: 10,
      };

      // Act
      await service.getRecommendations({
        problem_description: 'I want to learn machine learning',
        filters,
      });

      // Assert
      expect(entitiesService.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          entityTypeIds: ['course'],
          skill_levels: ['BEGINNER'],
          certificate_available: true,
          instructor_name: 'Andrew Ng',
          duration_text: '6 weeks',
          prerequisites: 'basic programming',
          limit: 20, // maxCandidates * 2
          page: 1,
        })
      );
    });

    it('should pass all job-specific filters to findAll', async () => {
      // Arrange
      entitiesService.vectorSearch.mockResolvedValue(mockVectorResults);
      entitiesService.findAll.mockResolvedValue({
        data: mockFilteredEntities,
        total: 2,
        page: 1,
        limit: 20,
        totalPages: 1,
      });
      llmService.getRecommendation.mockResolvedValue({
        recommendedEntityIds: ['1'],
        explanation: 'Great job opportunity',
      });

      const filters = {
        entityTypeIds: ['job'],
        employment_types: ['FULL_TIME'],
        experience_levels: ['SENIOR'],
        location_types: ['Remote'],
        salary_min: 120,
        salary_max: 200,
        company_name: 'Google',
        job_description: 'machine learning engineer',
        max_candidates: 25,
      };

      // Act
      await service.getRecommendations({
        problem_description: 'I want a senior ML engineering job',
        filters,
      });

      // Assert
      expect(entitiesService.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          entityTypeIds: ['job'],
          employment_types: ['FULL_TIME'],
          experience_levels: ['SENIOR'],
          location_types: ['Remote'],
          salary_min: 120,
          salary_max: 200,
          company_name: 'Google',
          job_description: 'machine learning engineer',
          limit: 50, // maxCandidates * 2
          page: 1,
        })
      );
    });

    it('should handle mixed entity type filters correctly', async () => {
      // Arrange
      entitiesService.vectorSearch.mockResolvedValue(mockVectorResults);
      entitiesService.findAll.mockResolvedValue({
        data: mockFilteredEntities,
        total: 2,
        page: 1,
        limit: 20,
        totalPages: 1,
      });
      llmService.getRecommendation.mockResolvedValue({
        recommendedEntityIds: ['1', '3'],
        explanation: 'Great combination of tools and courses',
      });

      const filters = {
        entityTypeIds: ['ai-tool', 'course'],
        searchTerm: 'machine learning',
        has_free_tier: true,
        technical_levels: ['BEGINNER'],
        skill_levels: ['BEGINNER'],
        max_candidates: 20,
      };

      // Act
      await service.getRecommendations({
        problem_description: 'I need resources to learn ML',
        filters,
      });

      // Assert
      expect(entitiesService.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          entityTypeIds: ['ai-tool', 'course'],
          searchTerm: 'machine learning',
          has_free_tier: true,
          technical_levels: ['BEGINNER'],
          skill_levels: ['BEGINNER'],
          limit: 40, // maxCandidates * 2
          page: 1,
        })
      );
    });

    it('should maintain vector search order while applying filters', async () => {
      // Arrange
      entitiesService.vectorSearch.mockResolvedValue(mockVectorResults);
      entitiesService.findAll.mockResolvedValue({
        data: [mockFilteredEntities[1], mockFilteredEntities[0]], // Different order
        total: 2,
        page: 1,
        limit: 20,
        totalPages: 1,
      });
      llmService.getRecommendation.mockResolvedValue({
        recommendedEntityIds: ['1', '3'],
        explanation: 'Ordered by relevance',
      });

      // Act
      const result = await service.getRecommendations({
        problem_description: 'Test query',
        filters: { max_candidates: 10 },
      });

      // Assert - Should maintain vector search order (1, 3) not findAll order (3, 1)
      expect(result.candidates_analyzed).toBe(2);
      expect(llmService.getRecommendation).toHaveBeenCalledWith(
        'Test query',
        expect.arrayContaining([
          expect.objectContaining({ id: '1' }),
          expect.objectContaining({ id: '3' }),
        ])
      );
    });
  });
});
