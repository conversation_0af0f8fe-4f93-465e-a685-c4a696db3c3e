import { Module } from '@nestjs/common';
import { EntitiesService } from './entities.service';
import { EntitiesController } from './entities.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';
import { CategoriesModule } from '../categories/categories.module';
import { TagsModule } from '../tags/tags.module';
import { OpenaiModule } from '../openai/openai.module';
import { SupabaseModule } from '../supabase/supabase.module';

@Module({
  imports: [
    PrismaModule,
    AuthModule,
    SupabaseModule,   // For SupabaseAuthGuard dependency injection
    CategoriesModule, // For potential validation of category_ids
    TagsModule,       // For potential validation of tag_ids
    OpenaiModule,
  ],
  controllers: [EntitiesController],
  providers: [EntitiesService],
  exports: [EntitiesService], // If other modules might need EntitiesService
})
export class EntitiesModule {} 