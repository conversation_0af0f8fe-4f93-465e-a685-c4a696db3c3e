import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PricingModel, PriceRange } from '@generated-prisma';

export class SoftwareDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 't0u1v2w3-x4y5-z6a7-b8c9-d0e1f2g3h4i5',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'Key features of the software.',
    type: 'array',
    items: { type: 'string' },
    example: ['Data analysis', 'Automation', 'Reporting'],
  })
  keyFeatures?: any | null;

  @ApiPropertyOptional({
    description: 'Common use cases for the software.',
    type: 'array',
    items: { type: 'string' },
    example: ['Data analysis', 'Automation'],
  })
  useCases?: any | null;

  @ApiPropertyOptional({
    description: 'Known integrations with other tools or services.',
    type: 'array',
    items: { type: 'string' },
    example: ['Slack', 'Google Drive'],
  })
  integrations?: any | null;

  @ApiPropertyOptional({
    description: 'Target audience for the software.',
    type: 'array',
    items: { type: 'string' },
    example: ['Business Analysts', 'Developers'],
  })
  targetAudience?: any | null;

  @ApiPropertyOptional({
    description: 'Options for deploying the software.',
    type: 'array',
    items: { type: 'string' },
    example: ['Cloud', 'On-premise', 'Desktop'],
  })
  deploymentOptions?: any | null;

  @ApiPropertyOptional({
    description: 'Operating systems supported by the software.',
    type: 'array',
    items: { type: 'string' },
    example: ['Windows', 'macOS', 'Linux'],
  })
  supportedOs?: any | null;

  @ApiPropertyOptional({
    description: 'Indicates if the software has mobile support.',
    example: true,
  })
  mobileSupport?: boolean | null;

  @ApiPropertyOptional({
    description: 'Indicates if the software provides API access.',
    example: false,
  })
  apiAccess?: boolean | null;

  @ApiPropertyOptional({
    description: 'Indicates if the software has a free tier.',
    example: true,
  })
  hasFreeTier?: boolean | null;

  @ApiPropertyOptional({
    enum: PricingModel,
    description: 'The pricing model of the software.',
    example: PricingModel.SUBSCRIPTION,
  })
  pricingModel?: PricingModel | null;

  @ApiPropertyOptional({
    enum: PriceRange,
    description: 'The price range of the software.',
    example: PriceRange.LOW,
  })
  priceRange?: PriceRange | null;

  @ApiPropertyOptional({
    description: 'Specific details about pricing.',
    example: 'Starts at $10/month.',
  })
  pricingDetails?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the pricing page of the software.',
    example: 'https://example.com/software/pricing',
  })
  pricingUrl?: string | null;

  @ApiPropertyOptional({
    description: 'Available support channels.',
    type: 'array',
    items: { type: 'string' },
    example: ['Email', 'Helpdesk', 'Community Forum'],
  })
  supportChannels?: any | null;

  @ApiPropertyOptional({
    description: 'Contact email for support.',
    example: '<EMAIL>',
  })
  supportEmail?: string | null;

  @ApiPropertyOptional({
    description: 'Indicates if live chat support is available.',
    example: true,
  })
  hasLiveChat?: boolean | null;

  @ApiPropertyOptional({
    description: 'URL to the community forum or Discord.',
    example: 'https://example.com/software/community',
  })
  communityUrl?: string | null;

  // Missing fields from recent DTO updates
  @ApiPropertyOptional({
    description: 'URL of the software repository.',
    example: 'https://github.com/company/awesome-software',
  })
  repositoryUrl?: string | null;

  @ApiPropertyOptional({
    description: 'Type of license.',
    example: 'MIT',
  })
  licenseType?: string | null;

  @ApiPropertyOptional({
    description: 'Programming languages used.',
    type: 'array',
    items: { type: 'string' },
    example: ['Python', 'JavaScript', 'TypeScript'],
  })
  programmingLanguages?: any | null;

  @ApiPropertyOptional({
    description: 'Compatible platforms.',
    type: 'array',
    items: { type: 'string' },
    example: ['Windows', 'macOS', 'Linux', 'Web'],
  })
  platformCompatibility?: any | null;

  @ApiPropertyOptional({
    description: 'Current version of the software.',
    example: '2.1.0',
  })
  currentVersion?: string | null;

  @ApiPropertyOptional({
    description: 'Release date of the software.',
    example: '2024-01-15T00:00:00.000Z',
  })
  releaseDate?: Date | null;

  @ApiPropertyOptional({
    description: 'Whether the software is open source.',
    example: true,
  })
  openSource?: boolean | null;

  @ApiPropertyOptional({
    description: 'Level of customization available.',
    example: 'High',
  })
  customizationLevel?: string | null;

  @ApiPropertyOptional({
    description: 'Whether a demo is available.',
    example: true,
  })
  demoAvailable?: boolean | null;

  @ApiPropertyOptional({
    description: 'Frameworks used or supported.',
    type: 'array',
    items: { type: 'string' },
    example: ['React', 'Django'],
  })
  frameworks?: any | null;

  @ApiPropertyOptional({
    description: 'Whether the software has an API.',
    example: true,
  })
  hasApi?: boolean | null;

  @ApiPropertyOptional({
    description: 'Libraries used or supported.',
    type: 'array',
    items: { type: 'string' },
    example: ['TensorFlow', 'Pandas'],
  })
  libraries?: any | null;

  @ApiPropertyOptional({
    description: 'Whether a trial is available.',
    example: true,
  })
  trialAvailable?: boolean | null;

  @ApiProperty({
    description: 'Timestamp of when the software details were created',
    example: '2025-04-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the software details',
    example: '2025-04-10T10:00:00.000Z',
  })
  updatedAt: Date;
}