import { Injectable, Logger } from '@nestjs/common';
import { 
  ConversationContext, 
  ChatMessage, 
  UserIntent,
  ConversationMemory,
  DiscoveryProgress,
  QuestionHistory
} from '../../common/llm/interfaces/llm.service.interface';
import { IConversationStateService } from '../interfaces/conversation-state.interface';
import { v4 as uuidv4 } from 'uuid';

/**
 * Enhanced Conversation Manager Service
 * 
 * Provides advanced conversation state management with memory, progress tracking,
 * and intelligent question management to prevent repetitive conversations.
 */
@Injectable()
export class EnhancedConversationManagerService {
  private readonly logger = new Logger(EnhancedConversationManagerService.name);

  constructor(
    private readonly conversationStateService: IConversationStateService,
  ) {}

  /**
   * Initialize a new conversation with enhanced memory structures
   */
  async initializeEnhancedConversation(
    userId: string,
    sessionId?: string,
  ): Promise<ConversationContext> {
    const finalSessionId = sessionId || this.generateSessionId();
    
    const context: ConversationContext = {
      sessionId: finalSessionId,
      userId,
      messages: [],
      discoveredEntities: [],
      userPreferences: {},
      conversationStage: 'greeting',
      metadata: {
        startedAt: new Date(),
        lastActiveAt: new Date(),
        totalMessages: 0,
        entitiesShown: [],
      },
      // Initialize enhanced memory structures
      conversationMemory: this.initializeConversationMemory(),
      discoveryProgress: this.initializeDiscoveryProgress(),
      questionHistory: this.initializeQuestionHistory(),
    };

    await this.conversationStateService.setConversationContext(finalSessionId, context);
    
    this.logger.log(`Initialized enhanced conversation session ${finalSessionId} for user ${userId}`);
    return context;
  }

  /**
   * Get or create conversation with enhanced capabilities
   */
  async getOrCreateEnhancedConversation(
    userId: string,
    sessionId?: string,
  ): Promise<ConversationContext> {
    if (sessionId) {
      const existingContext = await this.conversationStateService.getConversationContext(sessionId);
      
      if (existingContext) {
        if (existingContext.userId !== userId) {
          this.logger.warn(`Session ${sessionId} belongs to different user. Creating new session.`);
          return this.initializeEnhancedConversation(userId);
        }
        
        // Ensure enhanced structures exist (for backward compatibility)
        const enhancedContext = this.ensureEnhancedStructures(existingContext);
        
        this.logger.debug(`Retrieved existing enhanced conversation session ${sessionId}`);
        return enhancedContext;
      }
    }

    return this.initializeEnhancedConversation(userId, sessionId);
  }

  /**
   * Add message with enhanced context tracking
   */
  async addMessageWithEnhancedTracking(
    sessionId: string,
    message: Omit<ChatMessage, 'id' | 'timestamp'>,
    intent?: UserIntent,
    extractedInfo?: {
      userProfile?: Partial<ConversationMemory['userProfile']>;
      requirements?: Partial<ConversationMemory['requirements']>;
      insights?: Partial<ConversationMemory['insights']>;
    },
  ): Promise<ConversationContext> {
    const context = await this.conversationStateService.getConversationContext(sessionId);
    
    if (!context) {
      throw new Error(`Conversation session ${sessionId} not found`);
    }

    const chatMessage: ChatMessage = {
      id: uuidv4(),
      timestamp: new Date(),
      ...message,
    };

    // Add message to conversation
    context.messages.push(chatMessage);
    
    // Update metadata
    context.metadata.lastActiveAt = new Date();
    context.metadata.totalMessages = context.messages.length;

    // Update intent if provided
    if (intent) {
      context.currentIntent = intent;
    }

    // Update enhanced memory structures
    if (extractedInfo) {
      this.updateConversationMemory(context, extractedInfo);
    }

    // Update discovery progress
    this.updateDiscoveryProgress(context, message, intent);

    // Auto-update conversation stage based on progress
    this.autoUpdateConversationStage(context, intent);

    await this.conversationStateService.setConversationContext(sessionId, context);
    
    this.logger.debug(`Added ${message.role} message to enhanced session ${sessionId}. Total messages: ${context.messages.length}`);
    return context;
  }

  /**
   * Track a question being asked to prevent repetition
   */
  trackQuestionAsked(
    context: ConversationContext,
    question: string,
    category: string,
  ): void {
    if (!context.questionHistory) {
      context.questionHistory = this.initializeQuestionHistory();
    }

    const questionEntry = {
      question,
      timestamp: new Date(),
      category,
      answered: false,
      effectiveness: 0.5, // Default, will be updated based on user response
    };

    context.questionHistory.askedQuestions.push(questionEntry);

    // Update category tracking
    if (!context.questionHistory.questionCategories[category]) {
      context.questionHistory.questionCategories[category] = {
        count: 0,
        lastAsked: new Date(),
        effectiveness: 0.5,
        shouldAvoid: false,
      };
    }

    const categoryData = context.questionHistory.questionCategories[category];
    categoryData.count++;
    categoryData.lastAsked = new Date();

    this.logger.debug(`Tracked question in category ${category}: ${question.substring(0, 50)}...`);
  }

  /**
   * Check if a question category should be avoided (asked too recently or ineffective)
   */
  shouldAvoidQuestionCategory(
    context: ConversationContext,
    category: string,
    minIntervalMinutes: number = 5,
  ): boolean {
    if (!context.questionHistory?.questionCategories[category]) {
      return false; // Never asked, so it's fine
    }

    const categoryData = context.questionHistory.questionCategories[category];
    
    // Avoid if marked as should avoid
    if (categoryData.shouldAvoid) {
      return true;
    }

    // Avoid if asked too recently
    const timeSinceLastAsked = Date.now() - categoryData.lastAsked.getTime();
    const minInterval = minIntervalMinutes * 60 * 1000;
    
    if (timeSinceLastAsked < minInterval) {
      return true;
    }

    // Avoid if asked too many times with low effectiveness
    if (categoryData.count >= 3 && categoryData.effectiveness < 0.3) {
      return true;
    }

    return false;
  }

  /**
   * Get conversation insights for intelligent response generation
   */
  getConversationInsights(context: ConversationContext): {
    readinessForRecommendations: number;
    missingInformation: string[];
    conversationEfficiency: number;
    suggestedNextSteps: string[];
  } {
    const progress = context.discoveryProgress || this.initializeDiscoveryProgress();
    const memory = context.conversationMemory || this.initializeConversationMemory();
    
    // Calculate readiness for recommendations
    const infoGathered = progress.informationGathered;
    const gatheredCount = Object.values(infoGathered).filter(Boolean).length;
    const totalInfo = Object.keys(infoGathered).length;
    const readinessForRecommendations = gatheredCount / totalInfo;

    // Identify missing information
    const missingInformation = Object.entries(infoGathered)
      .filter(([_, gathered]) => !gathered)
      .map(([key, _]) => key);

    // Calculate conversation efficiency
    const messageCount = context.messages.length;
    const progressScore = progress.confidence;
    const conversationEfficiency = messageCount > 0 ? progressScore / Math.log(messageCount + 1) : 0;

    // Suggest next steps
    const suggestedNextSteps = this.generateNextSteps(context, missingInformation);

    return {
      readinessForRecommendations,
      missingInformation,
      conversationEfficiency,
      suggestedNextSteps,
    };
  }

  // Private helper methods
  private generateSessionId(): string {
    return `chat_${uuidv4()}`;
  }

  private initializeConversationMemory(): ConversationMemory {
    return {
      userProfile: {},
      requirements: {
        mustHave: [],
        niceToHave: [],
        dealBreakers: [],
        specificFeatures: [],
        integrationNeeds: [],
        platformPreferences: [],
      },
      discussedTopics: {
        entityTypes: [],
        categories: [],
        features: [],
        useCases: [],
        competitors: [],
        concerns: [],
      },
      insights: {
        primaryGoal: '',
        urgency: 'medium',
        decisionMakers: [],
        evaluationCriteria: [],
        timeline: '',
      },
    };
  }

  private initializeDiscoveryProgress(): DiscoveryProgress {
    return {
      phase: 'initial',
      completedSteps: [],
      nextSteps: ['understand_use_case', 'identify_requirements'],
      confidence: 0.1,
      readinessScore: 0.0,
      informationGathered: {
        useCase: false,
        industry: false,
        technicalLevel: false,
        budget: false,
        teamSize: false,
        timeline: false,
        specificRequirements: false,
        integrationNeeds: false,
      },
    };
  }

  private initializeQuestionHistory(): QuestionHistory {
    return {
      askedQuestions: [],
      questionCategories: {},
      avoidedTopics: [],
      preferredTopics: [],
    };
  }

  private ensureEnhancedStructures(context: ConversationContext): ConversationContext {
    if (!context.conversationMemory) {
      context.conversationMemory = this.initializeConversationMemory();
    }
    if (!context.discoveryProgress) {
      context.discoveryProgress = this.initializeDiscoveryProgress();
    }
    if (!context.questionHistory) {
      context.questionHistory = this.initializeQuestionHistory();
    }
    return context;
  }

  private updateConversationMemory(
    context: ConversationContext,
    extractedInfo: {
      userProfile?: Partial<ConversationMemory['userProfile']>;
      requirements?: Partial<ConversationMemory['requirements']>;
      insights?: Partial<ConversationMemory['insights']>;
    },
  ): void {
    if (!context.conversationMemory) {
      context.conversationMemory = this.initializeConversationMemory();
    }

    const memory = context.conversationMemory;

    // Update user profile
    if (extractedInfo.userProfile) {
      Object.assign(memory.userProfile, extractedInfo.userProfile);
    }

    // Update requirements
    if (extractedInfo.requirements) {
      Object.keys(extractedInfo.requirements).forEach(key => {
        const value = extractedInfo.requirements![key as keyof ConversationMemory['requirements']];
        if (Array.isArray(value)) {
          const existingArray = memory.requirements[key as keyof ConversationMemory['requirements']] as string[];
          value.forEach(item => {
            if (!existingArray.includes(item)) {
              existingArray.push(item);
            }
          });
        }
      });
    }

    // Update insights
    if (extractedInfo.insights) {
      Object.assign(memory.insights, extractedInfo.insights);
    }
  }

  private updateDiscoveryProgress(
    context: ConversationContext,
    message: Omit<ChatMessage, 'id' | 'timestamp'>,
    intent?: UserIntent,
  ): void {
    if (!context.discoveryProgress) {
      context.discoveryProgress = this.initializeDiscoveryProgress();
    }

    const progress = context.discoveryProgress;
    const messageContent = message.content.toLowerCase();

    // Update information gathered based on message content
    if (messageContent.includes('education') || messageContent.includes('teaching') || messageContent.includes('school')) {
      progress.informationGathered.industry = true;
      progress.informationGathered.useCase = true;
    }

    if (messageContent.includes('beginner') || messageContent.includes('advanced') || messageContent.includes('expert')) {
      progress.informationGathered.technicalLevel = true;
    }

    if (messageContent.includes('free') || messageContent.includes('budget') || messageContent.includes('cost') || messageContent.includes('price')) {
      progress.informationGathered.budget = true;
    }

    // Update confidence based on information gathered
    const gatheredCount = Object.values(progress.informationGathered).filter(Boolean).length;
    const totalInfo = Object.keys(progress.informationGathered).length;
    progress.confidence = gatheredCount / totalInfo;

    // Update readiness score
    progress.readinessScore = Math.min(progress.confidence * 1.2, 1.0);

    // Update phase based on progress
    if (progress.confidence > 0.7) {
      progress.phase = 'evaluation';
    } else if (progress.confidence > 0.4) {
      progress.phase = 'refinement';
    } else if (progress.confidence > 0.2) {
      progress.phase = 'exploration';
    }
  }

  private autoUpdateConversationStage(context: ConversationContext, intent?: UserIntent): void {
    const progress = context.discoveryProgress;
    
    if (!progress) return;

    if (progress.readinessScore > 0.8) {
      context.conversationStage = 'recommendation';
    } else if (progress.confidence > 0.5) {
      context.conversationStage = 'refinement';
    } else if (context.messages.length > 1) {
      context.conversationStage = 'discovery';
    }
  }

  private generateNextSteps(context: ConversationContext, missingInformation: string[]): string[] {
    const steps: string[] = [];
    
    if (missingInformation.includes('useCase')) {
      steps.push('understand_primary_use_case');
    }
    if (missingInformation.includes('industry')) {
      steps.push('identify_industry_context');
    }
    if (missingInformation.includes('technicalLevel')) {
      steps.push('assess_technical_requirements');
    }
    if (missingInformation.includes('budget')) {
      steps.push('discuss_budget_constraints');
    }
    
    return steps;
  }
}
