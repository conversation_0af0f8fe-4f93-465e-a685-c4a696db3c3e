import { Injectable, Logger } from '@nestjs/common';
import { 
  EnhancedUserIntent, 
  SmartFollowUpQuestion, 
  ConversationContext,
  EnhancedConversationContext 
} from '../../common/llm/interfaces/llm.service.interface';
import { FilterExtractionService } from '../../recommendations/services/filter-extraction.service';

/**
 * Enhanced Intent Classification Service
 * 
 * Provides sophisticated intent classification with comprehensive filter extraction
 * and smart follow-up question generation for conversational AI.
 */
@Injectable()
export class EnhancedIntentClassificationService {
  private readonly logger = new Logger(EnhancedIntentClassificationService.name);

  constructor(
    private readonly filterExtractionService: FilterExtractionService,
  ) {}

  /**
   * Classify user intent with comprehensive filter extraction
   */
  async classifyIntentWithFilters(
    userMessage: string,
    context: ConversationContext,
  ): Promise<EnhancedUserIntent> {
    try {
      // Step 1: Basic intent classification
      const basicIntent = this.classifyBasicIntent(userMessage, context);

      // Step 2: Extract filters from the message
      const extractedFilters = await this.filterExtractionService.extractFiltersFromDescription(userMessage);

      // Step 3: Calculate confidence scores for extracted filters
      const filterConfidence = this.calculateFilterConfidence(extractedFilters, userMessage);

      // Step 4: Identify missing criteria
      const missingCriteria = this.identifyMissingCriteria(extractedFilters, context);

      // Step 5: Generate smart clarifying questions
      const clarifyingQuestions = this.generateClarifyingQuestions(
        extractedFilters,
        missingCriteria,
        context,
      );

      const enhancedIntent: EnhancedUserIntent = {
        ...basicIntent,
        extractedFilters,
        filterConfidence,
        missingCriteria,
        clarifyingQuestions,
      };

      this.logger.debug('Enhanced intent classified:', {
        type: enhancedIntent.type,
        confidence: enhancedIntent.confidence,
        extractedFilterKeys: Object.keys(extractedFilters),
        missingCriteriaKeys: Object.keys(missingCriteria),
        clarifyingQuestionsCount: clarifyingQuestions.length,
      });

      return enhancedIntent;
    } catch (error) {
      this.logger.error('Error in enhanced intent classification', error.stack);
      
      // Fallback to basic intent
      return {
        ...this.classifyBasicIntent(userMessage, context),
        extractedFilters: {},
        filterConfidence: {},
        missingCriteria: {},
        clarifyingQuestions: [],
      };
    }
  }

  /**
   * Update conversation context with accumulated filters
   */
  updateConversationFilters(
    context: ConversationContext,
    enhancedIntent: EnhancedUserIntent,
    messageIndex: number,
  ): EnhancedConversationContext {
    const enhancedContext = context as EnhancedConversationContext;

    // Initialize enhanced context if needed
    if (!enhancedContext.accumulatedFilters) {
      enhancedContext.accumulatedFilters = {
        filters: {},
        confidence: {},
        history: {},
        source: {},
      };
    }

    if (!enhancedContext.enhancedMetadata) {
      enhancedContext.enhancedMetadata = {
        filtersExtracted: 0,
        clarificationQuestions: 0,
        conversationQuality: 0.5,
        readyForRecommendations: false,
      };
    }

    // Merge new filters with accumulated ones
    Object.entries(enhancedIntent.extractedFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        // Update filter value
        enhancedContext.accumulatedFilters.filters[key] = value;
        
        // Update confidence (use higher confidence if filter already exists)
        const newConfidence = enhancedIntent.filterConfidence[key] || 0.7;
        const existingConfidence = enhancedContext.accumulatedFilters.confidence[key] || 0;
        enhancedContext.accumulatedFilters.confidence[key] = Math.max(newConfidence, existingConfidence);
        
        // Update history
        enhancedContext.accumulatedFilters.history[key] = messageIndex;
        
        // Update source
        enhancedContext.accumulatedFilters.source[key] = 'extracted';
      }
    });

    // Update metadata
    enhancedContext.enhancedMetadata.filtersExtracted = Object.keys(enhancedContext.accumulatedFilters.filters).length;
    enhancedContext.enhancedMetadata.conversationQuality = this.calculateConversationQuality(enhancedContext);
    enhancedContext.enhancedMetadata.readyForRecommendations = this.isReadyForRecommendations(enhancedContext);

    return enhancedContext;
  }

  /**
   * Basic intent classification logic
   */
  private classifyBasicIntent(userMessage: string, context: ConversationContext) {
    const message = userMessage.toLowerCase();
    
    // Discovery intent indicators
    if (this.matchesAny(message, [
      'need', 'want', 'looking for', 'find', 'search', 'help me', 'recommend'
    ])) {
      return {
        type: 'discovery' as const,
        confidence: 0.8,
        entities: [],
        categories: [],
        features: [],
        constraints: {},
      };
    }

    // Comparison intent indicators
    if (this.matchesAny(message, [
      'compare', 'vs', 'versus', 'difference', 'better', 'which one'
    ])) {
      return {
        type: 'comparison' as const,
        confidence: 0.8,
        entities: [],
        categories: [],
        features: [],
        constraints: {},
      };
    }

    // Refinement intent indicators
    if (this.matchesAny(message, [
      'also', 'but', 'however', 'actually', 'more specific', 'narrow down'
    ])) {
      return {
        type: 'refinement' as const,
        confidence: 0.7,
        entities: [],
        categories: [],
        features: [],
        constraints: {},
      };
    }

    // Default to discovery
    return {
      type: 'discovery' as const,
      confidence: 0.6,
      entities: [],
      categories: [],
      features: [],
      constraints: {},
    };
  }

  /**
   * Calculate confidence scores for extracted filters
   */
  private calculateFilterConfidence(
    extractedFilters: Record<string, any>,
    userMessage: string,
  ): Record<string, number> {
    const confidence: Record<string, number> = {};
    const message = userMessage.toLowerCase();

    Object.keys(extractedFilters).forEach(key => {
      // Base confidence
      let score = 0.7;

      // Increase confidence for explicit mentions
      if (key === 'entityTypeIds' && this.matchesAny(message, ['tool', 'course', 'job', 'event'])) {
        score = 0.9;
      }

      if (key === 'technical_levels' && this.matchesAny(message, ['beginner', 'advanced', 'expert'])) {
        score = 0.9;
      }

      if (key === 'has_free_tier' && this.matchesAny(message, ['free', 'no cost'])) {
        score = 0.95;
      }

      if (key === 'has_api' && this.matchesAny(message, ['api', 'integration'])) {
        score = 0.9;
      }

      // Decrease confidence for inferred filters
      if (key.includes('search') || key.includes('_text')) {
        score = Math.max(0.5, score - 0.2);
      }

      confidence[key] = score;
    });

    return confidence;
  }

  /**
   * Identify missing criteria that would improve recommendations
   */
  private identifyMissingCriteria(
    extractedFilters: Record<string, any>,
    context: ConversationContext,
  ) {
    const missing = {
      entityTypes: !extractedFilters.entityTypeIds || extractedFilters.entityTypeIds.length === 0,
      technicalLevel: !extractedFilters.technical_levels && !extractedFilters.skill_levels,
      budget: !extractedFilters.has_free_tier && !extractedFilters.price_ranges && !extractedFilters.salary_min,
      useCase: !extractedFilters.use_cases_search && !extractedFilters.key_features_search,
      platform: !extractedFilters.platforms && !extractedFilters.location_types,
      specificRequirements: Object.keys(extractedFilters).length < 3,
    };

    return missing;
  }

  /**
   * Generate smart clarifying questions based on missing criteria
   */
  private generateClarifyingQuestions(
    extractedFilters: Record<string, any>,
    missingCriteria: any,
    context: ConversationContext,
  ): SmartFollowUpQuestion[] {
    const questions: SmartFollowUpQuestion[] = [];

    if (missingCriteria.entityTypes) {
      questions.push({
        question: "What type of resource are you looking for?",
        purpose: 'entity_type',
        expectedFilterKeys: ['entityTypeIds'],
        priority: 10,
        suggestedAnswers: ['AI Tools', 'Courses', 'Jobs', 'Events', 'Hardware'],
      });
    }

    if (missingCriteria.technicalLevel) {
      questions.push({
        question: "What's your technical background or experience level?",
        purpose: 'technical_level',
        expectedFilterKeys: ['technical_levels', 'skill_levels', 'experience_levels'],
        priority: 8,
        suggestedAnswers: ['Beginner', 'Intermediate', 'Advanced', 'Expert'],
      });
    }

    if (missingCriteria.budget) {
      questions.push({
        question: "Do you have any budget constraints or preferences?",
        purpose: 'budget',
        expectedFilterKeys: ['has_free_tier', 'price_ranges', 'salary_min', 'salary_max'],
        priority: 7,
        suggestedAnswers: ['Free only', 'Under $50/month', 'Under $200/month', 'No budget limit'],
      });
    }

    if (missingCriteria.useCase) {
      questions.push({
        question: "What specific use case or problem are you trying to solve?",
        purpose: 'use_case',
        expectedFilterKeys: ['use_cases_search', 'key_features_search', 'job_description'],
        priority: 9,
      });
    }

    if (missingCriteria.platform) {
      questions.push({
        question: "Do you have any platform or location preferences?",
        purpose: 'platform',
        expectedFilterKeys: ['platforms', 'location_types', 'is_online'],
        priority: 6,
        suggestedAnswers: ['Windows', 'macOS', 'Linux', 'Web', 'Mobile', 'Remote', 'On-site'],
      });
    }

    // Sort by priority (highest first)
    return questions.sort((a, b) => b.priority - a.priority).slice(0, 3); // Limit to top 3
  }

  /**
   * Calculate overall conversation quality score
   */
  private calculateConversationQuality(context: EnhancedConversationContext): number {
    const filtersCount = Object.keys(context.accumulatedFilters.filters).length;
    const avgConfidence = Object.values(context.accumulatedFilters.confidence).reduce((a, b) => a + b, 0) / 
                         Math.max(1, Object.values(context.accumulatedFilters.confidence).length);
    
    // Quality based on number of filters and their confidence
    const filterScore = Math.min(1, filtersCount / 5); // 5 filters = perfect score
    const confidenceScore = avgConfidence;
    
    return (filterScore * 0.6 + confidenceScore * 0.4);
  }

  /**
   * Determine if the conversation has enough information for recommendations
   */
  private isReadyForRecommendations(context: EnhancedConversationContext): boolean {
    const filtersCount = Object.keys(context.accumulatedFilters.filters).length;
    const hasEntityType = context.accumulatedFilters.filters.entityTypeIds;
    const avgConfidence = Object.values(context.accumulatedFilters.confidence).reduce((a, b) => a + b, 0) / 
                         Math.max(1, Object.values(context.accumulatedFilters.confidence).length);
    
    return filtersCount >= 3 && hasEntityType && avgConfidence > 0.6;
  }

  private matchesAny(text: string, patterns: string[]): boolean {
    return patterns.some(pattern => text.includes(pattern));
  }
}
