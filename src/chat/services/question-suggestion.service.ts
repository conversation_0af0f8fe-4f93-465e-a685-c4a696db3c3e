import { Injectable, Logger } from '@nestjs/common';
import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';

/**
 * Simple service to suggest contextual follow-up questions
 * This is a minimal, working improvement that doesn't break existing functionality
 */
@Injectable()
export class QuestionSuggestionService {
  private readonly logger = new Logger(QuestionSuggestionService.name);

  /**
   * Generate contextual follow-up questions based on conversation state
   */
  generateFollowUpQuestions(
    context: ConversationContext,
    userMessage: string,
    maxQuestions: number = 2,
  ): string[] {
    try {
      // Handle null/undefined context gracefully
      if (!context) {
        this.logger.warn('Context is null, returning empty questions array');
        return [];
      }

      const questions: string[] = [];
      const messageCount = context.messages?.length || 0;
      const userPrefs = context.userPreferences || {};

      // 🎯 ENHANCED: Use new analysis for non-empty conversations, fallback for simple cases
      if (messageCount > 0 && context.messages) {
        // 🎯 ENHANCED: Comprehensive conversation analysis to prevent repetition
        const conversationAnalysis = this.analyzeConversation(context);
        const userMessageAnalysis = this.analyzeUserMessage(userMessage);

        this.logger.debug(`Enhanced question generation analysis:`, {
          discussedTopics: Array.from(conversationAnalysis.discussedTopics),
          askedQuestions: conversationAnalysis.askedQuestions.length,
          userIntent: userMessageAnalysis.intent,
          specificity: userMessageAnalysis.specificity,
          conversationPhase: conversationAnalysis.conversationPhase
        });

        // 🎯 ENHANCED: Smart question generation based on missing information
        const missingInfo = this.identifyMissingInformation(conversationAnalysis, userMessageAnalysis);

        // Generate questions for missing information only
        for (const infoType of missingInfo) {
          const newQuestions = this.generateQuestionsForInfoType(infoType, userMessageAnalysis, conversationAnalysis);
          questions.push(...newQuestions);

          // Limit to avoid overwhelming the user
          if (questions.length >= maxQuestions) break;
        }

        // 🎯 ENHANCED: If no specific missing info, generate progressive questions
        if (questions.length === 0) {
          const progressiveQuestions = this.generateProgressiveQuestions(
            messageCount,
            conversationAnalysis,
            userMessageAnalysis
          );
          questions.push(...progressiveQuestions);
        }
      } else {
        // Fallback to original logic for simple cases or tests
        if (messageCount <= 2) {
          questions.push(...this.getInitialQuestions(userMessage, userPrefs));
        } else if (messageCount <= 6) {
          questions.push(...this.getExplorationQuestions(userMessage, userPrefs));
        } else {
          questions.push(...this.getRefinementQuestions(userMessage, userPrefs));
        }
      }

      // 🎯 ENHANCED: Avoid asking the same type of questions repeatedly
      if (questions.length === 0) {
        // Fallback to context-aware questions
        if (messageCount <= 2) {
          questions.push(...this.getInitialQuestions(userMessage, userPrefs));
        } else if (messageCount <= 6) {
          questions.push(...this.getExplorationQuestions(userMessage, userPrefs));
        } else {
          questions.push(...this.getRefinementQuestions(userMessage, userPrefs));
        }
      }

      // Filter and return up to maxQuestions
      return this.filterAndRankQuestions(questions, context, maxQuestions);
    } catch (error) {
      this.logger.error('Error generating follow-up questions', error);
      return [];
    }
  }

  /**
   * Questions for early conversation
   */
  private getInitialQuestions(userMessage: string, userPrefs: any): string[] {
    const questions: string[] = [];
    const message = userMessage.toLowerCase();

    // Ask about work context if not known
    if (!userPrefs.industry && !userPrefs.work_context) {
      if (message.includes('work') || message.includes('business') || message.includes('company')) {
        questions.push("What industry do you work in?");
      } else {
        questions.push("What kind of work or projects are you looking to enhance with AI tools?");
      }
    }

    // Ask about specific use case
    if (message.includes('help') || message.includes('need') || message.includes('looking')) {
      questions.push("What specific task or challenge are you trying to solve?");
    }

    // Ask about experience level
    if (!userPrefs.technical_level) {
      questions.push("How comfortable are you with learning new AI tools?");
    }

    return questions;
  }

  /**
   * Questions for exploration phase
   */
  private getExplorationQuestions(userMessage: string, userPrefs: any): string[] {
    const questions: string[] = [];
    const message = userMessage.toLowerCase();

    // Industry-specific questions
    if (userPrefs.industry === 'education' || userPrefs.work_context === 'education') {
      questions.push("Are you looking for tools to help with lesson planning, student engagement, or assessment?");
    } else if (userPrefs.industry === 'marketing') {
      questions.push("Are you focusing on content creation, analytics, or customer engagement?");
    } else if (userPrefs.industry === 'healthcare') {
      questions.push("Are you looking for tools for patient care, research, or administrative tasks?");
    }

    // Budget questions
    if (!userPrefs.budget) {
      questions.push("Do you have a budget in mind, or are you open to both free and paid options?");
    }

    // Team size questions
    if (!userPrefs.team_size) {
      questions.push("Will this be for personal use, or do you need something that works for a team?");
    }

    // Feature-specific questions
    if (message.includes('video') || message.includes('image') || message.includes('content')) {
      questions.push("What type of content are you primarily working with?");
    }

    return questions;
  }

  /**
   * Questions for refinement phase
   */
  private getRefinementQuestions(userMessage: string, userPrefs: any): string[] {
    const questions: string[] = [];

    // Integration questions
    questions.push("Do you need the tool to integrate with any existing software you're already using?");

    // Timeline questions
    questions.push("How soon are you looking to implement this solution?");

    // Deal breaker questions
    questions.push("Are there any features or limitations that would be deal-breakers for you?");

    // Specific feature questions
    questions.push("What's the most important feature you need this tool to have?");

    return questions;
  }

  /**
   * Enhanced filter and rank questions based on relevance and conversation context
   */
  private filterAndRankQuestions(
    questions: string[],
    context: ConversationContext,
    maxQuestions: number,
  ): string[] {
    // Remove duplicates
    const uniqueQuestions = [...new Set(questions)];

    // Get all previous assistant messages to check for similar questions
    const assistantMessages = context.messages?.filter(m => m.role === 'assistant') || [];
    const allAssistantContent = assistantMessages.map(m => m.content.toLowerCase()).join(' ');

    // Enhanced ranking with multiple factors
    const rankedQuestions = uniqueQuestions
      .map(question => {
        const questionLower = question.toLowerCase();

        // Check if similar question was already asked
        const similarityPenalty = this.calculateQuestionSimilarity(questionLower, allAssistantContent);

        // Calculate relevance to recent conversation
        const recentMessages = context.messages?.slice(-4) || [];
        const recentContent = recentMessages.map(m => m.content.toLowerCase()).join(' ');
        const relevanceScore = this.calculateRelevance(question, recentContent);

        // Calculate specificity bonus (more specific questions are better)
        const specificityBonus = this.calculateSpecificityBonus(questionLower);

        // Calculate conversation stage appropriateness
        const stageBonus = this.calculateStageAppropriatenessBonus(questionLower, context);

        // Final score combines all factors
        const finalScore = relevanceScore + specificityBonus + stageBonus - similarityPenalty;

        return {
          question,
          score: finalScore,
          debug: {
            relevance: relevanceScore,
            specificity: specificityBonus,
            stage: stageBonus,
            similarity: similarityPenalty
          }
        };
      })
      .sort((a, b) => b.score - a.score)
      .slice(0, maxQuestions);

    // Log debug information
    this.logger.debug('Question ranking results:', rankedQuestions.map(q => ({
      question: q.question.substring(0, 50) + '...',
      score: q.score,
      debug: q.debug
    })));

    return rankedQuestions.map(item => item.question);
  }

  /**
   * Calculate relevance score for a question
   */
  private calculateRelevance(question: string, recentContent: string): number {
    let score = 1.0;

    // Lower score if similar question was asked recently
    const questionWords = question.toLowerCase().split(' ');
    const keyWords = questionWords.filter(word =>
      word.length > 3 && !['what', 'how', 'are', 'you', 'the', 'for', 'with'].includes(word)
    );

    for (const word of keyWords) {
      if (recentContent.includes(word)) {
        score -= 0.3;
      }
    }

    return Math.max(score, 0.1);
  }

  /**
   * Calculate similarity penalty for questions already asked
   */
  private calculateQuestionSimilarity(question: string, allAssistantContent: string): number {
    const questionKeywords = question.split(' ').filter(word => word.length > 3);
    let similarityScore = 0;

    questionKeywords.forEach(keyword => {
      if (allAssistantContent.includes(keyword)) {
        similarityScore += 0.3; // Penalty for each matching keyword
      }
    });

    // Check for semantic similarity
    const semanticPatterns = [
      { pattern: /what.*language/, keywords: ['programming', 'code', 'language'] },
      { pattern: /what.*industry/, keywords: ['work', 'industry', 'business'] },
      { pattern: /what.*budget/, keywords: ['budget', 'cost', 'price'] },
      { pattern: /what.*level/, keywords: ['level', 'experience', 'technical'] },
    ];

    semanticPatterns.forEach(({ pattern, keywords }) => {
      if (pattern.test(question) && keywords.some(keyword => allAssistantContent.includes(keyword))) {
        similarityScore += 0.5; // Higher penalty for semantic similarity
      }
    });

    return Math.min(similarityScore, 2.0); // Cap the penalty
  }

  /**
   * Calculate specificity bonus for more specific questions
   */
  private calculateSpecificityBonus(question: string): number {
    const specificityIndicators = [
      'specific', 'exactly', 'particular', 'which', 'how many', 'what type',
      'programming language', 'grade level', 'industry', 'budget range'
    ];

    let bonus = 0;
    specificityIndicators.forEach(indicator => {
      if (question.includes(indicator)) {
        bonus += 0.2;
      }
    });

    return Math.min(bonus, 1.0); // Cap the bonus
  }

  /**
   * Calculate conversation stage appropriateness bonus
   */
  private calculateStageAppropriatenessBonus(question: string, context: ConversationContext): number {
    const messageCount = context.messages?.length || 0;
    let bonus = 0;

    // Early conversation (1-3 messages) - prefer broad questions
    if (messageCount <= 3) {
      const broadQuestions = ['what', 'industry', 'work', 'field', 'type'];
      if (broadQuestions.some(keyword => question.includes(keyword))) {
        bonus += 0.3;
      }
    }

    // Mid conversation (4-6 messages) - prefer specific questions
    else if (messageCount <= 6) {
      const specificQuestions = ['specific', 'exactly', 'particular', 'features'];
      if (specificQuestions.some(keyword => question.includes(keyword))) {
        bonus += 0.3;
      }
    }

    // Late conversation (7+ messages) - prefer action-oriented questions
    else {
      const actionQuestions = ['ready', 'show', 'recommend', 'compare', 'help'];
      if (actionQuestions.some(keyword => question.includes(keyword))) {
        bonus += 0.3;
      }
    }

    return bonus;
  }

  /**
   * Check if a question category has been covered recently
   */
  isQuestionCategoryRecent(context: ConversationContext, category: string): boolean {
    const recentMessages = context.messages?.slice(-6) || [];
    const recentContent = recentMessages.map(m => m.content.toLowerCase()).join(' ');

    const categoryKeywords: Record<string, string[]> = {
      industry: ['industry', 'work', 'business', 'company'],
      budget: ['budget', 'cost', 'price', 'free', 'paid'],
      team: ['team', 'personal', 'individual', 'group'],
      technical: ['comfortable', 'experience', 'technical', 'beginner'],
      timeline: ['soon', 'timeline', 'when', 'deadline'],
      integration: ['integrate', 'software', 'existing', 'api'],
    };

    const keywords = categoryKeywords[category] || [];
    return keywords.some((keyword: string) => recentContent.includes(keyword));
  }

  /**
   * Generate questions for specific topics
   */
  generateTopicQuestions(topic: string, context: ConversationContext): string[] {
    const topicQuestions: Record<string, string[]> = {
      budget: [
        "What's your budget range for AI tools?",
        "Are you looking for free options, or are you open to paid solutions?",
        "Do you need enterprise-level features, or would basic plans work?",
      ],
      industry: [
        "What industry do you work in?",
        "What type of business or organization are you with?",
        "What's your primary work focus?",
      ],
      technical_level: [
        "How comfortable are you with learning new technology?",
        "Do you prefer simple, user-friendly tools or are you okay with more complex solutions?",
        "What's your experience level with AI tools?",
      ],
      team_size: [
        "Is this for personal use or for a team?",
        "How many people would be using this tool?",
        "Do you need collaboration features?",
      ],
      use_case: [
        "What specific task are you trying to accomplish?",
        "What's the main challenge you're facing?",
        "What would success look like for you?",
      ],
    };

    return topicQuestions[topic] || [];
  }

  /**
   * Extract topics from previous messages to avoid repetition
   */
  private extractPreviousTopics(messages: any[]): string[] {
    const topics: string[] = [];
    messages.forEach(message => {
      if (message.content && typeof message.content === 'string') {
        topics.push(message.content.toLowerCase());
      }
    });
    return topics;
  }

  /**
   * Check if a topic has already been discussed
   */
  private hasDiscussedTopic(previousMessages: string[], keywords: string[]): boolean {
    const allText = previousMessages.join(' ').toLowerCase();
    return keywords.some(keyword => allText.includes(keyword));
  }

  /**
   * Comprehensive conversation analysis to prevent repetition
   */
  private analyzeConversation(context: ConversationContext): {
    discussedTopics: Set<string>;
    askedQuestions: string[];
    userNeeds: Record<string, any>;
    conversationPhase: 'initial' | 'discovery' | 'refinement' | 'recommendation';
  } {
    const messages = context.messages || [];
    const discussedTopics = new Set<string>();
    const askedQuestions: string[] = [];
    const userNeeds: Record<string, any> = {};

    // Analyze all messages for topics and questions
    messages.forEach(msg => {
      const content = msg.content.toLowerCase();

      // Track topics discussed
      const topicKeywords = {
        'industry': ['work', 'job', 'industry', 'business', 'company', 'profession'],
        'technical_level': ['beginner', 'intermediate', 'advanced', 'expert', 'technical'],
        'budget': ['budget', 'cost', 'price', 'expensive', 'cheap', 'free', 'paid'],
        'programming': ['code', 'coding', 'programming', 'development', 'developer'],
        'education': ['education', 'teaching', 'learning', 'student', 'school'],
        'content_creation': ['content', 'writing', 'video', 'image', 'design'],
        'automation': ['automation', 'automate', 'workflow', 'process'],
        'data_analysis': ['data', 'analysis', 'analytics', 'insights'],
        'specific_tools': ['specific', 'particular', 'exactly', 'precisely'],
        'use_case': ['use', 'case', 'scenario', 'situation', 'need'],
      };

      Object.entries(topicKeywords).forEach(([topic, keywords]) => {
        if (keywords.some(keyword => content.includes(keyword))) {
          discussedTopics.add(topic);
        }
      });

      // Extract questions asked by assistant
      if (msg.role === 'assistant' && content.includes('?')) {
        const questions = content.match(/[^.!]*\?/g);
        if (questions) {
          askedQuestions.push(...questions.map(q => q.trim()));
        }
      }

      // Extract user needs and preferences
      if (msg.role === 'user') {
        if (content.includes('need') || content.includes('want') || content.includes('looking for')) {
          userNeeds.hasExpressedNeed = true;
        }
        if (content.includes('budget') || content.includes('cost')) {
          userNeeds.hasBudgetConcern = true;
        }
        if (content.includes('beginner') || content.includes('advanced')) {
          userNeeds.hasExpressedLevel = true;
        }
      }
    });

    // Determine conversation phase
    let conversationPhase: 'initial' | 'discovery' | 'refinement' | 'recommendation' = 'initial';
    if (messages.length > 6) conversationPhase = 'recommendation';
    else if (messages.length > 4) conversationPhase = 'refinement';
    else if (messages.length > 2) conversationPhase = 'discovery';

    return {
      discussedTopics,
      askedQuestions,
      userNeeds,
      conversationPhase
    };
  }

  /**
   * Analyze the current user message for intent and content
   */
  private analyzeUserMessage(userMessage: string): {
    intent: 'vague' | 'specific' | 'clarification' | 'new_topic';
    topics: string[];
    specificity: number;
  } {
    const content = userMessage.toLowerCase();
    const topics: string[] = [];

    // Detect topics in current message
    const topicPatterns = {
      'programming': ['code', 'coding', 'programming', 'development'],
      'education': ['education', 'teaching', 'learning', 'student'],
      'business': ['business', 'work', 'company', 'industry'],
      'content': ['content', 'writing', 'video', 'image'],
      'data': ['data', 'analysis', 'analytics'],
    };

    Object.entries(topicPatterns).forEach(([topic, keywords]) => {
      if (keywords.some(keyword => content.includes(keyword))) {
        topics.push(topic);
      }
    });

    // Determine intent
    let intent: 'vague' | 'specific' | 'clarification' | 'new_topic' = 'vague';
    if (content.includes('specifically') || content.includes('exactly') || topics.length > 0) {
      intent = 'specific';
    } else if (content.includes('what') || content.includes('how') || content.includes('?')) {
      intent = 'clarification';
    } else if (topics.length > 0) {
      intent = 'new_topic';
    }

    // Calculate specificity score
    const specificityIndicators = ['specific', 'exactly', 'particular', 'need', 'want', 'looking for'];
    const specificity = specificityIndicators.filter(indicator => content.includes(indicator)).length / specificityIndicators.length;

    return { intent, topics, specificity };
  }

  /**
   * Identify what information is still missing from the conversation
   */
  private identifyMissingInformation(
    conversationAnalysis: any,
    userMessageAnalysis: any
  ): string[] {
    const missing: string[] = [];
    const { discussedTopics, userNeeds } = conversationAnalysis;

    // Check for missing critical information
    if (!discussedTopics.has('industry') && !userNeeds.hasExpressedNeed) {
      missing.push('industry_context');
    }

    if (!discussedTopics.has('specific_tools') && userMessageAnalysis.specificity < 0.3) {
      missing.push('specific_needs');
    }

    if (!discussedTopics.has('technical_level') && !userNeeds.hasExpressedLevel) {
      missing.push('technical_level');
    }

    if (!discussedTopics.has('budget') && !userNeeds.hasBudgetConcern) {
      missing.push('budget_constraints');
    }

    if (!discussedTopics.has('use_case') && userMessageAnalysis.intent === 'vague') {
      missing.push('use_case');
    }

    return missing;
  }

  /**
   * Generate questions for specific missing information types
   */
  private generateQuestionsForInfoType(
    infoType: string,
    userMessageAnalysis: any,
    conversationAnalysis: any
  ): string[] {
    const questions: string[] = [];

    switch (infoType) {
      case 'industry_context':
        if (userMessageAnalysis.topics.includes('education')) {
          questions.push("What grade level or subject area do you teach?");
          questions.push("Are you looking for tools to help with lesson planning, grading, or student engagement?");
        } else if (userMessageAnalysis.topics.includes('programming')) {
          questions.push("What programming language are you primarily working with?");
          questions.push("Are you looking for tools to help with debugging, testing, or code generation?");
        } else if (userMessageAnalysis.topics.includes('business')) {
          questions.push("What industry does your business operate in?");
          questions.push("Are you looking to automate specific business processes?");
        } else {
          questions.push("What field or industry are you working in?");
        }
        break;

      case 'specific_needs':
        if (userMessageAnalysis.topics.includes('education')) {
          questions.push("What specific teaching challenges are you trying to solve?");
          questions.push("Are you looking for tools for lesson planning, grading, or student engagement?");
        } else if (userMessageAnalysis.topics.includes('programming')) {
          questions.push("What specific coding challenges are you facing?");
          questions.push("Are you looking for tools for code review, documentation, or testing?");
        } else {
          questions.push("What specific challenge are you trying to solve?");
          questions.push("What would be the ideal outcome for you?");
        }
        break;

      case 'technical_level':
        questions.push("How would you describe your technical expertise level?");
        break;

      case 'budget_constraints':
        questions.push("Do you have any budget considerations for AI tools?");
        break;

      case 'use_case':
        if (userMessageAnalysis.topics.includes('education')) {
          questions.push("What specific educational tasks are you looking to enhance?");
        } else if (userMessageAnalysis.topics.includes('programming')) {
          questions.push("What type of development tasks are you working on?");
        } else {
          questions.push("What type of tasks are you looking to accomplish?");
        }
        break;
    }

    return questions;
  }

  /**
   * Generate progressive questions based on conversation stage
   */
  private generateProgressiveQuestions(
    messageCount: number,
    conversationAnalysis: any,
    userMessageAnalysis: any
  ): string[] {
    const questions: string[] = [];
    const { conversationPhase, discussedTopics } = conversationAnalysis;

    switch (conversationPhase) {
      case 'initial':
        questions.push("What brings you here today? What are you hoping to accomplish?");
        break;

      case 'discovery':
        if (!discussedTopics.has('specific_tools')) {
          questions.push("Are there any specific features that are must-haves for you?");
        }
        break;

      case 'refinement':
        questions.push("Would you like me to show you some specific recommendations based on what we've discussed?");
        questions.push("Are you ready to see some AI tools that match your needs?");
        break;

      case 'recommendation':
        questions.push("Would you like to compare a few options, or do you need help with implementation?");
        questions.push("Should I show you some specific tools that match your requirements?");
        break;
    }

    // Add action-oriented questions for later stages
    if (messageCount > 6) {
      questions.push("Are you ready to see some recommendations?");
      questions.push("Would you like me to show you some specific options?");
      questions.push("Should we compare a few tools that might work for you?");
    }

    return questions;
  }
}
