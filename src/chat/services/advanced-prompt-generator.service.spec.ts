import { Test, TestingModule } from '@nestjs/testing';
import { AdvancedPromptGeneratorService } from './advanced-prompt-generator.service';

describe('AdvancedPromptGeneratorService', () => {
  let service: AdvancedPromptGeneratorService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AdvancedPromptGeneratorService],
    }).compile();

    service = module.get<AdvancedPromptGeneratorService>(AdvancedPromptGeneratorService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateAdvancedChatPrompt', () => {
    it('should generate a comprehensive prompt with all context', () => {
      const userMessage = 'I need help finding AI tools for education';
      const context = {
        sessionId: 'test-session',
        messages: [
          { role: 'user', content: 'Hello', timestamp: new Date() },
          { role: 'assistant', content: 'Hi! How can I help you?', timestamp: new Date() },
        ],
        conversationMemory: {
          userProfile: {
            industry: 'education',
            experienceLevel: 'beginner',
            workContext: 'education',
          },
          requirements: {
            mustHave: ['easy to use'],
            niceToHave: ['affordable'],
            dealBreakers: ['too complex'],
          },
          discussedTopics: {
            entityTypes: ['ai-tool'],
            categories: ['Education'],
          },
          insights: {
            primaryGoal: 'find educational AI tools',
            urgency: 'medium',
          },
        },
        discoveryProgress: {
          phase: 'exploration',
          confidence: 0.6,
        },
        questionHistory: {
          askedQuestions: [
            {
              question: 'What industry do you work in?',
              timestamp: new Date(),
              category: 'industry',
              answered: true,
            },
          ],
        },
        metadata: {
          lastActiveAt: new Date(),
        },
      } as any;

      const intent = {
        type: 'discovery',
        confidence: 0.8,
        entities: [],
        categories: ['Education'],
        features: [],
      } as any;

      const candidateEntities = [
        {
          id: '1',
          name: 'Gradescope',
          description: 'AI-powered grading platform',
          categories: [{ category: { name: 'Education' } }],
          features: [{ feature: { name: 'Auto-grading' } }],
          has_free_tier: true,
          avgRating: 4.5,
        },
      ];

      const suggestedQuestions = ['What specific educational tasks do you need help with?'];

      const prompt = service.generateAdvancedChatPrompt(
        userMessage,
        context,
        intent,
        candidateEntities,
        suggestedQuestions
      );

      expect(prompt).toBeDefined();
      expect(typeof prompt).toBe('string');
      expect(prompt.length).toBeGreaterThan(100);

      // Check that key sections are included
      expect(prompt).toContain('You are an expert AI tool discovery assistant');
      expect(prompt).toContain('Current User Message');
      expect(prompt).toContain(userMessage);
      expect(prompt).toContain('education'); // User context
      expect(prompt).toContain('Gradescope'); // Entity information
      expect(prompt).toContain('exploration'); // Current phase
      expect(prompt).toContain('Response Format (JSON)'); // Response format
    });

    it('should handle minimal context gracefully', () => {
      const userMessage = 'Hello';
      const context = {
        sessionId: 'test-session',
        messages: [],
        metadata: {
          lastActiveAt: new Date(),
        },
      } as any;

      const intent = {
        type: 'general_question',
        confidence: 0.5,
      } as any;

      const prompt = service.generateAdvancedChatPrompt(userMessage, context, intent);

      expect(prompt).toBeDefined();
      expect(typeof prompt).toBe('string');
      expect(prompt).toContain('You are an expert AI tool discovery assistant');
      expect(prompt).toContain(userMessage);
      expect(prompt).toContain('Not yet established'); // For missing user profile
    });

    it('should include entity information when provided', () => {
      const userMessage = 'Tell me about grading tools';
      const context = {
        sessionId: 'test-session',
        messages: [],
        metadata: { lastActiveAt: new Date() },
      } as any;

      const intent = { type: 'specific_tool', confidence: 0.9 } as any;

      const candidateEntities = [
        {
          id: '1',
          name: 'Gradescope',
          description: 'AI-powered grading platform',
          categories: [{ category: { name: 'Education' } }],
          features: [
            { feature: { name: 'Auto-grading' } },
            { feature: { name: 'Rubric-based grading' } },
            { feature: { name: 'Plagiarism detection' } },
          ],
          has_free_tier: true,
          avgRating: 4.5,
        },
        {
          id: '2',
          name: 'Turnitin',
          description: 'Plagiarism detection and grading',
          categories: [{ category: { name: 'Education' } }],
          features: [{ feature: { name: 'Plagiarism detection' } }],
          has_free_tier: false,
          avgRating: 4.2,
        },
      ];

      const prompt = service.generateAdvancedChatPrompt(
        userMessage,
        context,
        intent,
        candidateEntities
      );

      expect(prompt).toContain('Gradescope');
      expect(prompt).toContain('Turnitin');
      expect(prompt).toContain('Auto-grading');
      expect(prompt).toContain('Has free tier');
      expect(prompt).toContain('4.5/5'); // Rating
    });

    it('should provide phase-specific guidance', () => {
      const userMessage = 'I need AI tools';
      
      // Test initial phase
      const initialContext = {
        sessionId: 'test',
        messages: [],
        discoveryProgress: { phase: 'initial', confidence: 0.1 },
        metadata: { lastActiveAt: new Date() },
      } as any;

      const intent = { type: 'discovery', confidence: 0.5 } as any;

      const initialPrompt = service.generateAdvancedChatPrompt(userMessage, initialContext, intent);
      expect(initialPrompt).toContain('early discovery');
      expect(initialPrompt).toContain('understanding their primary use case');

      // Test evaluation phase
      const evaluationContext = {
        sessionId: 'test',
        messages: [],
        discoveryProgress: { phase: 'evaluation', confidence: 0.8 },
        metadata: { lastActiveAt: new Date() },
      } as any;

      const evaluationPrompt = service.generateAdvancedChatPrompt(userMessage, evaluationContext, intent);
      expect(evaluationPrompt).toContain('ready to evaluate options');
      expect(evaluationPrompt).toContain('specific recommendations');
    });

    it('should include conversation flow guidelines based on message count', () => {
      const userMessage = 'I need help';
      const intent = { type: 'discovery', confidence: 0.5 } as any;

      // Test early conversation (few messages)
      const earlyContext = {
        sessionId: 'test',
        messages: [{ role: 'user', content: 'Hello' }],
        discoveryProgress: { confidence: 0.2 },
        metadata: { lastActiveAt: new Date() },
      } as any;

      const earlyPrompt = service.generateAdvancedChatPrompt(userMessage, earlyContext, intent);
      expect(earlyPrompt).toContain('Early conversation');
      expect(earlyPrompt).toContain('ask ONE good question');

      // Test later conversation (many messages)
      const laterContext = {
        sessionId: 'test',
        messages: Array(10).fill({ role: 'user', content: 'test' }),
        discoveryProgress: { confidence: 0.8 },
        metadata: { lastActiveAt: new Date() },
      } as any;

      const laterPrompt = service.generateAdvancedChatPrompt(userMessage, laterContext, intent);
      expect(laterPrompt).toContain('Strong understanding');
      expect(laterPrompt).toContain('specific recommendations');
    });
  });

  describe('generateAdvancedIntentPrompt', () => {
    it('should generate intent classification prompt with context', () => {
      const userMessage = 'I work in education and need grading tools';
      const context = {
        messages: [
          { role: 'user', content: 'Hello', timestamp: new Date() },
          { role: 'assistant', content: 'Hi there!', timestamp: new Date() },
          { role: 'user', content: userMessage, timestamp: new Date() },
        ],
        conversationMemory: {
          userProfile: {
            industry: 'education',
          },
        },
        discoveryProgress: {
          phase: 'exploration',
          confidence: 0.5,
        },
      } as any;

      const prompt = service.generateAdvancedIntentPrompt(userMessage, context);

      expect(prompt).toBeDefined();
      expect(typeof prompt).toBe('string');
      expect(prompt).toContain('intent classification');
      expect(prompt).toContain(userMessage);
      expect(prompt).toContain('education'); // Known industry
      expect(prompt).toContain('exploration'); // Current phase
      expect(prompt).toContain('Response Format (JSON)');
      expect(prompt).toContain('discovery|comparison|specific_tool'); // Intent types
    });

    it('should handle minimal context for intent classification', () => {
      const userMessage = 'Hello';
      const context = {
        messages: [],
        discoveryProgress: { phase: 'initial', confidence: 0.1 },
      } as any;

      const prompt = service.generateAdvancedIntentPrompt(userMessage, context);

      expect(prompt).toBeDefined();
      expect(prompt).toContain(userMessage);
      expect(prompt).toContain('Unknown'); // For missing information
      expect(prompt).toContain('initial'); // Phase
    });

    it('should include recent conversation context', () => {
      const userMessage = 'What about pricing?';
      const context = {
        messages: [
          { role: 'user', content: 'I need video editing tools' },
          { role: 'assistant', content: 'I can help with that!' },
          { role: 'user', content: 'Show me some options' },
        ],
        discoveryProgress: { phase: 'refinement', confidence: 0.6 },
      } as any;

      const prompt = service.generateAdvancedIntentPrompt(userMessage, context);

      expect(prompt).toContain('video editing tools');
      expect(prompt).toContain('Show me some options');
      expect(prompt).toContain('refinement');
    });
  });

  describe('prompt structure validation', () => {
    it('should always include critical instructions', () => {
      const userMessage = 'Test message';
      const context = { sessionId: 'test', messages: [], metadata: { lastActiveAt: new Date() } } as any;
      const intent = { type: 'discovery', confidence: 0.5 } as any;

      const prompt = service.generateAdvancedChatPrompt(userMessage, context, intent);

      // Check for critical instructions
      expect(prompt).toContain('Make your message conversational');
      expect(prompt).toContain('Only ask ONE follow-up question');
      expect(prompt).toContain('Build on what you already know');
      expect(prompt).toContain('Provide value in every response');
      expect(prompt).toContain('Only mention tools if they\'re truly relevant');
    });

    it('should include proper response format specification', () => {
      const userMessage = 'Test message';
      const context = { sessionId: 'test', messages: [], metadata: { lastActiveAt: new Date() } } as any;
      const intent = { type: 'discovery', confidence: 0.5 } as any;

      const prompt = service.generateAdvancedChatPrompt(userMessage, context, intent);

      expect(prompt).toContain('Response Format (JSON)');
      expect(prompt).toContain('"message":');
      expect(prompt).toContain('"discoveredEntities":');
      expect(prompt).toContain('"followUpQuestions":');
      expect(prompt).toContain('"suggestedActions":');
      expect(prompt).toContain('"shouldTransitionToRecommendations":');
      expect(prompt).toContain('"conversationStage":');
      expect(prompt).toContain('"insights":');
    });
  });
});
