import { Test, TestingModule } from '@nestjs/testing';
import { QuestionSuggestionService } from './question-suggestion.service';

describe('QuestionSuggestionService', () => {
  let service: QuestionSuggestionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [QuestionSuggestionService],
    }).compile();

    service = module.get<QuestionSuggestionService>(QuestionSuggestionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateFollowUpQuestions', () => {
    it('should generate initial questions for new conversation', () => {
      const context = {
        sessionId: 'test-session',
        userId: 'test-user',
        messages: [
          { id: '1', role: 'user', content: 'I need help finding AI tools', timestamp: new Date() },
        ],
        discoveredEntities: [],
        userPreferences: {},
        conversationStage: 'greeting',
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 1,
          entitiesShown: [],
        },
      } as any;

      const questions = service.generateFollowUpQuestions(context, 'I need help finding AI tools', 2);

      expect(questions).toBeDefined();
      expect(Array.isArray(questions)).toBe(true);
      expect(questions.length).toBeLessThanOrEqual(2);
      expect(questions.length).toBeGreaterThan(0);

      // Should ask about work context or specific task
      const hasWorkQuestion = questions.some(q => 
        q.includes('work') || q.includes('industry') || q.includes('task') || q.includes('challenge')
      );
      expect(hasWorkQuestion).toBe(true);
    });

    it('should generate exploration questions for mid-conversation', () => {
      const context = {
        sessionId: 'test-session',
        userId: 'test-user',
        messages: [
          { id: '1', role: 'user', content: 'I need help finding AI tools', timestamp: new Date() },
          { id: '2', role: 'assistant', content: 'I can help with that!', timestamp: new Date() },
          { id: '3', role: 'user', content: 'I work in education', timestamp: new Date() },
          { id: '4', role: 'assistant', content: 'Great!', timestamp: new Date() },
        ],
        discoveredEntities: [],
        userPreferences: {
          industry: 'education',
        },
        conversationStage: 'discovery',
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 4,
          entitiesShown: [],
        },
      } as any;

      const questions = service.generateFollowUpQuestions(context, 'I need grading tools', 2);

      expect(questions).toBeDefined();
      expect(questions.length).toBeLessThanOrEqual(2);

      // Should ask education-specific questions
      const hasEducationQuestion = questions.some(q => 
        q.includes('lesson planning') || q.includes('student engagement') || q.includes('assessment') ||
        q.includes('budget') || q.includes('team')
      );
      expect(hasEducationQuestion).toBe(true);
    });

    it('should generate refinement questions for later conversation', () => {
      const context = {
        sessionId: 'test-session',
        userId: 'test-user',
        messages: Array(8).fill(null).map((_, i) => ({
          id: `${i + 1}`,
          role: i % 2 === 0 ? 'user' : 'assistant',
          content: `Message ${i + 1}`,
          timestamp: new Date(),
        })),
        discoveredEntities: [],
        userPreferences: {
          industry: 'education',
          budget: 'medium',
        },
        conversationStage: 'refinement',
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 8,
          entitiesShown: [],
        },
      } as any;

      const questions = service.generateFollowUpQuestions(context, 'Tell me more about pricing', 2);

      expect(questions).toBeDefined();
      expect(questions.length).toBeLessThanOrEqual(2);

      // Should ask refinement questions
      const hasRefinementQuestion = questions.some(q => 
        q.includes('integrate') || q.includes('timeline') || q.includes('deal-breaker') || q.includes('important')
      );
      expect(hasRefinementQuestion).toBe(true);
    });

    it('should handle empty context gracefully', () => {
      const context = {
        sessionId: 'test-session',
        userId: 'test-user',
        messages: [],
        discoveredEntities: [],
        userPreferences: {},
        conversationStage: 'greeting',
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 0,
          entitiesShown: [],
        },
      } as any;

      const questions = service.generateFollowUpQuestions(context, 'Hello', 2);

      expect(questions).toBeDefined();
      expect(Array.isArray(questions)).toBe(true);
      // Should still generate some questions
      expect(questions.length).toBeGreaterThanOrEqual(0);
    });

    it('should not exceed maxQuestions limit', () => {
      const context = {
        sessionId: 'test-session',
        userId: 'test-user',
        messages: [
          { id: '1', role: 'user', content: 'I need help', timestamp: new Date() },
        ],
        discoveredEntities: [],
        userPreferences: {},
        conversationStage: 'greeting',
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 1,
          entitiesShown: [],
        },
      } as any;

      const questions = service.generateFollowUpQuestions(context, 'I need help finding AI tools', 1);

      expect(questions.length).toBeLessThanOrEqual(1);
    });
  });

  describe('generateTopicQuestions', () => {
    it('should generate budget questions', () => {
      const context = {} as any;
      const questions = service.generateTopicQuestions('budget', context);

      expect(questions).toBeDefined();
      expect(questions.length).toBeGreaterThan(0);
      expect(questions.some(q => q.includes('budget') || q.includes('cost'))).toBe(true);
    });

    it('should generate industry questions', () => {
      const context = {} as any;
      const questions = service.generateTopicQuestions('industry', context);

      expect(questions).toBeDefined();
      expect(questions.length).toBeGreaterThan(0);
      expect(questions.some(q => q.includes('industry') || q.includes('work'))).toBe(true);
    });

    it('should generate technical level questions', () => {
      const context = {} as any;
      const questions = service.generateTopicQuestions('technical_level', context);

      expect(questions).toBeDefined();
      expect(questions.length).toBeGreaterThan(0);
      expect(questions.some(q => q.includes('comfortable') || q.includes('experience'))).toBe(true);
    });

    it('should return empty array for unknown topic', () => {
      const context = {} as any;
      const questions = service.generateTopicQuestions('unknown_topic', context);

      expect(questions).toBeDefined();
      expect(Array.isArray(questions)).toBe(true);
      expect(questions.length).toBe(0);
    });
  });

  describe('isQuestionCategoryRecent', () => {
    it('should detect recent budget questions', () => {
      const context = {
        messages: [
          { content: 'What is your budget for this project?', timestamp: new Date() },
          { content: 'I have a medium budget', timestamp: new Date() },
        ],
      } as any;

      const isRecent = service.isQuestionCategoryRecent(context, 'budget');
      expect(isRecent).toBe(true);
    });

    it('should return false for non-recent categories', () => {
      const context = {
        messages: [
          { content: 'Hello there', timestamp: new Date() },
          { content: 'I need help with AI tools', timestamp: new Date() },
        ],
      } as any;

      const isRecent = service.isQuestionCategoryRecent(context, 'budget');
      expect(isRecent).toBe(false);
    });

    it('should handle empty messages', () => {
      const context = {
        messages: [],
      } as any;

      const isRecent = service.isQuestionCategoryRecent(context, 'budget');
      expect(isRecent).toBe(false);
    });
  });

  describe('error handling', () => {
    it('should handle errors gracefully', () => {
      const invalidContext = null as any;

      const questions = service.generateFollowUpQuestions(invalidContext, 'test message', 2);

      expect(questions).toBeDefined();
      expect(Array.isArray(questions)).toBe(true);
      expect(questions.length).toBe(0);
    });
  });
});
