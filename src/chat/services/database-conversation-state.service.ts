import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import {
  IConversationStateService,
  ConversationStateConfig,
} from '../interfaces/conversation-state.interface';
import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';

/**
 * Database-backed implementation of conversation state service
 * Persists conversations across server restarts
 */
@Injectable()
export class DatabaseConversationStateService implements IConversationStateService {
  private readonly logger = new Logger(DatabaseConversationStateService.name);

  constructor(private readonly prisma: PrismaService) {}

  async setConversationContext(
    sessionId: string,
    context: ConversationContext,
    ttlSeconds?: number,
  ): Promise<void> {
    try {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + (ttlSeconds || 3600) * 1000);

      // Store conversation context in database using Prisma's upsert instead of raw SQL
      await this.prisma.conversationSession.upsert({
        where: { sessionId: sessionId },
        update: {
          contextData: context as any,
          updatedAt: now,
          expiresAt: expiresAt,
        },
        create: {
          sessionId: sessionId,
          userId: context.userId,
          contextData: context as any,
          createdAt: now,
          updatedAt: now,
          expiresAt: expiresAt,
        },
      });

      this.logger.debug(
        `Stored conversation context for session ${sessionId}, user ${context.userId}, expires at ${expiresAt.toISOString()}`,
      );
    } catch (error) {
      this.logger.error(`Failed to store conversation context for session ${sessionId}`, error);
      throw error;
    }
  }

  async getConversationContext(sessionId: string): Promise<ConversationContext | null> {
    try {
      const session = await this.prisma.conversationSession.findFirst({
        where: {
          sessionId: sessionId,
          expiresAt: {
            gt: new Date()
          }
        }
      });

      if (!session) {
        this.logger.debug(`No conversation context found for session ${sessionId}`);
        return null;
      }

      // Update last accessed time
      await this.prisma.conversationSession.update({
        where: { sessionId: sessionId },
        data: { updatedAt: new Date() }
      });

      this.logger.debug(`Retrieved conversation context for session ${sessionId}`);
      return session.contextData as unknown as ConversationContext;
    } catch (error) {
      this.logger.error(`Failed to retrieve conversation context for session ${sessionId}`, error);
      return null;
    }
  }

  async deleteConversationContext(sessionId: string): Promise<void> {
    try {
      await this.prisma.$executeRaw`
        DELETE FROM conversation_sessions 
        WHERE session_id = ${sessionId}
      `;
      
      this.logger.debug(`Deleted conversation context for session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Failed to delete conversation context for session ${sessionId}`, error);
      throw error;
    }
  }

  async getUserActiveSessions(userId: string): Promise<string[]> {
    try {
      const sessions = await this.prisma.conversationSession.findMany({
        where: {
          userId: userId,
          expiresAt: {
            gt: new Date()
          }
        },
        orderBy: {
          updatedAt: 'desc'
        },
        select: {
          sessionId: true
        }
      });

      return sessions.map(session => session.sessionId);
    } catch (error) {
      this.logger.error(`Failed to get active sessions for user ${userId}`, error);
      return [];
    }
  }

  async hasConversationContext(sessionId: string): Promise<boolean> {
    try {
      const session = await this.prisma.conversationSession.findFirst({
        where: {
          sessionId: sessionId,
          expiresAt: {
            gt: new Date()
          }
        }
      });

      return session !== null;
    } catch (error) {
      this.logger.error(`Failed to check if conversation context exists for session ${sessionId}`, error);
      return false;
    }
  }

  async cleanupExpiredConversations(): Promise<number> {
    try {
      const result = await this.prisma.conversationSession.deleteMany({
        where: {
          expiresAt: {
            lte: new Date()
          }
        }
      });

      this.logger.debug(`Cleaned up ${result.count} expired conversation sessions`);
      return result.count;
    } catch (error) {
      this.logger.error('Failed to cleanup expired conversations', error);
      return 0;
    }
  }

  async getStats(): Promise<{
    totalSessions: number;
    activeSessions: number;
    memoryUsage?: number;
  }> {
    try {
      const [totalResult, activeResult] = await Promise.all([
        this.prisma.$queryRaw<Array<{ count: bigint }>>`
          SELECT COUNT(*) as count FROM conversation_sessions
        `,
        this.prisma.$queryRaw<Array<{ count: bigint }>>`
          SELECT COUNT(*) as count FROM conversation_sessions WHERE expires_at > NOW()
        `,
      ]);

      return {
        totalSessions: Number(totalResult[0]?.count || 0),
        activeSessions: Number(activeResult[0]?.count || 0),
        // memoryUsage is not applicable for database storage
      };
    } catch (error) {
      this.logger.error('Failed to get conversation stats', error);
      return { totalSessions: 0, activeSessions: 0 };
    }
  }
}
