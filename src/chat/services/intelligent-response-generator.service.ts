import { Injectable, Logger } from '@nestjs/common';
import { 
  EnhancedConversationContext, 
  EnhancedUserIntent,
  SmartFollowUpQuestion 
} from '../../common/llm/interfaces/llm.service.interface';
import { 
  ConversationAction, 
  FilterCorrectionResult,
  ConversationOptimization 
} from './conversation-flow-manager.service';

/**
 * Intelligent Response Generator Service
 * 
 * Generates contextual, intelligent responses based on conversation state,
 * filter analysis, and strategic conversation flow optimization.
 */
@Injectable()
export class IntelligentResponseGeneratorService {
  private readonly logger = new Logger(IntelligentResponseGeneratorService.name);

  /**
   * Generate intelligent response based on conversation analysis
   */
  generateIntelligentResponse(
    context: EnhancedConversationContext,
    currentIntent: EnhancedUserIntent,
    action: ConversationAction,
    entities: any[],
    questions: SmartFollowUpQuestion[],
    correctionResult?: FilterCorrectionResult,
    optimization?: ConversationOptimization,
  ): IntelligentChatResponse {
    const response = this.buildBaseResponse(context, currentIntent, action);

    // Handle corrections first
    if (correctionResult?.isCorrection) {
      response.message = this.generateCorrectionResponse(correctionResult) + '\n\n' + response.message;
      response.metadata.correctionHandled = true;
    }

    // Handle conflicts
    if (correctionResult?.conflicts && correctionResult.conflicts.length > 0) {
      response.conflictResolution = this.generateConflictResolution(correctionResult.conflicts);
    }

    // Add entity discoveries
    if (entities.length > 0) {
      response.discoveredEntities = this.formatDiscoveredEntities(entities, currentIntent);
      response.message += this.generateEntityDiscoveryMessage(entities, currentIntent);
    }

    // Add strategic questions
    if (questions.length > 0) {
      response.strategicQuestions = questions;
      response.message += '\n\n' + this.generateQuestionMessage(questions, context);
    }

    // Add conversation guidance
    if (optimization) {
      response.conversationGuidance = this.generateConversationGuidance(optimization);
    }

    // Determine next steps
    response.suggestedActions = this.generateSuggestedActions(action, entities, questions);
    response.readyForRecommendations = optimization?.readyForRecommendations || false;

    this.logger.debug('Generated intelligent response:', {
      actionType: action.type,
      entitiesCount: entities.length,
      questionsCount: questions.length,
      readyForRecommendations: response.readyForRecommendations,
    });

    return response;
  }

  /**
   * Generate response for filter corrections
   */
  private generateCorrectionResponse(correctionResult: FilterCorrectionResult): string {
    if (correctionResult.corrections.length === 0) return '';

    const corrections = correctionResult.corrections;
    
    if (corrections.length === 1) {
      const correction = corrections[0];
      return `✅ Got it! I've updated your ${this.humanizeFilterKey(correction.filterKey)} preference.`;
    }

    return `✅ Thanks for the clarification! I've updated ${corrections.length} of your preferences.`;
  }

  /**
   * Generate conflict resolution options
   */
  private generateConflictResolution(conflicts: any[]): ConflictResolutionOption[] {
    return conflicts.map(conflict => ({
      filterKey: conflict.filterKey,
      question: `I noticed you mentioned ${this.humanizeFilterKey(conflict.filterKey)} differently. Which do you prefer?`,
      options: [
        {
          label: `${conflict.existingValue} (from earlier)`,
          value: conflict.existingValue,
          confidence: conflict.existingConfidence,
        },
        {
          label: `${conflict.newValue} (just mentioned)`,
          value: conflict.newValue,
          confidence: conflict.newConfidence,
        },
      ],
    }));
  }

  /**
   * Build base response based on conversation action
   */
  private buildBaseResponse(
    context: EnhancedConversationContext,
    currentIntent: EnhancedUserIntent,
    action: ConversationAction,
  ): IntelligentChatResponse {
    const filtersCount = Object.keys(context.accumulatedFilters?.filters || {}).length;
    const userName = this.extractUserName(context) || 'there';

    let message = '';
    let conversationStage: string = 'discovery';

    switch (action.type) {
      case 'provide_recommendations':
        message = `Perfect! I have enough information to provide you with some great recommendations. Let me find the best options for you...`;
        conversationStage = 'recommendation';
        break;

      case 'ask_entity_type':
        message = `Hi ${userName}! I'd love to help you find the perfect resource. What type of resource are you looking for?`;
        conversationStage = 'discovery';
        break;

      case 'gather_more_info':
        message = `Great start! I have ${filtersCount} criteria so far. Let me ask a few more questions to find you the perfect match.`;
        conversationStage = 'refinement';
        break;

      case 'clarify_uncertain_filters':
        message = `I want to make sure I understand your needs correctly. Let me clarify a few details...`;
        conversationStage = 'refinement';
        break;

      case 'continue_discovery':
        message = `Excellent! I'm building a good picture of what you need. Let me gather a bit more information...`;
        conversationStage = 'discovery';
        break;

      default:
        message = `Thanks for that information! Let me help you find what you're looking for.`;
        conversationStage = 'discovery';
    }

    return {
      message,
      conversationStage,
      metadata: {
        actionType: action.type,
        actionReason: action.reason,
        filtersAccumulated: filtersCount,
        correctionHandled: false,
      },
      discoveredEntities: [],
      strategicQuestions: [],
      suggestedActions: [],
      readyForRecommendations: false,
    };
  }

  /**
   * Generate message about discovered entities
   */
  private generateEntityDiscoveryMessage(entities: any[], currentIntent: EnhancedUserIntent): string {
    if (entities.length === 0) return '';

    const entityTypes = [...new Set(entities.map(e => e.entityType?.name || 'resource'))];
    const typeText = entityTypes.length === 1 ? entityTypes[0] : 'resources';

    if (entities.length === 1) {
      return `\n\n🎯 I found a great ${typeText.toLowerCase()} that matches your criteria: **${entities[0].name}**`;
    }

    if (entities.length <= 3) {
      const names = entities.map(e => `**${e.name}**`).join(', ');
      return `\n\n🎯 I found ${entities.length} excellent ${typeText.toLowerCase()}s: ${names}`;
    }

    return `\n\n🎯 I found ${entities.length} ${typeText.toLowerCase()}s that match your criteria. Here are the top options...`;
  }

  /**
   * Generate strategic question message
   */
  private generateQuestionMessage(questions: SmartFollowUpQuestion[], context: EnhancedConversationContext): string {
    if (questions.length === 0) return '';

    const question = questions[0]; // Use the highest priority question
    
    let message = `❓ ${question.question}`;
    
    if (question.suggestedAnswers && question.suggestedAnswers.length > 0) {
      message += `\n\n${question.suggestedAnswers.join(' • ')}`;
    }

    if (question.followUpContext) {
      message += `\n\n💡 ${question.followUpContext}`;
    }

    return message;
  }

  /**
   * Format discovered entities for response
   */
  private formatDiscoveredEntities(entities: any[], currentIntent: EnhancedUserIntent): FormattedEntity[] {
    return entities.slice(0, 5).map(entity => ({
      id: entity.id,
      name: entity.name,
      description: entity.shortDescription || entity.description,
      type: entity.entityType?.name || 'Resource',
      rating: entity.avgRating,
      reviewCount: entity.reviewCount,
      relevanceScore: this.calculateRelevanceScore(entity, currentIntent),
      matchedFilters: this.getMatchedFilters(entity, currentIntent),
    }));
  }

  /**
   * Generate conversation guidance based on optimization analysis
   */
  private generateConversationGuidance(optimization: ConversationOptimization): ConversationGuidance {
    const guidance: ConversationGuidance = {
      efficiency: optimization.efficiency,
      suggestions: [],
      nextSteps: [],
    };

    if (optimization.efficiency < 0.5) {
      guidance.suggestions.push('Let\'s focus on the most important criteria first');
    }

    if (optimization.filtersCount < 3) {
      guidance.nextSteps.push('Gather 2-3 more key requirements');
    }

    if (optimization.avgConfidence < 0.7) {
      guidance.nextSteps.push('Clarify uncertain criteria');
    }

    if (optimization.readyForRecommendations) {
      guidance.nextSteps.push('Provide personalized recommendations');
    }

    return guidance;
  }

  /**
   * Generate suggested actions for the user
   */
  private generateSuggestedActions(
    action: ConversationAction,
    entities: any[],
    questions: SmartFollowUpQuestion[],
  ): SuggestedAction[] {
    const actions: SuggestedAction[] = [];

    if (entities.length > 0) {
      actions.push({
        type: 'view_entities',
        label: `View ${entities.length} matching options`,
        description: 'See detailed information about the resources I found',
        priority: 8,
      });
    }

    if (questions.length > 0) {
      actions.push({
        type: 'answer_question',
        label: 'Answer my question',
        description: 'Help me understand your needs better',
        priority: 9,
      });
    }

    if (action.type === 'provide_recommendations') {
      actions.push({
        type: 'get_recommendations',
        label: 'Get my recommendations',
        description: 'See personalized recommendations based on your criteria',
        priority: 10,
      });
    }

    actions.push({
      type: 'refine_search',
      label: 'Add more criteria',
      description: 'Provide additional requirements to narrow down options',
      priority: 6,
    });

    return actions.sort((a, b) => b.priority - a.priority);
  }

  // Helper methods
  private humanizeFilterKey(key: string): string {
    const humanized: Record<string, string> = {
      entityTypeIds: 'resource type',
      technical_levels: 'technical level',
      skill_levels: 'skill level',
      has_free_tier: 'budget preference',
      price_ranges: 'price range',
      platforms: 'platform',
      use_cases_search: 'use case',
      location_types: 'location preference',
      employment_types: 'employment type',
      experience_levels: 'experience level',
      certificate_available: 'certification requirement',
      is_online: 'format preference',
      event_types: 'event type',
      hardware_types: 'hardware type',
    };

    return humanized[key] || key.replace(/_/g, ' ');
  }

  private extractUserName(context: EnhancedConversationContext): string | null {
    // Try to extract name from conversation history
    const messages = context.messages || [];
    for (const message of messages) {
      if (message.role === 'user' && message.content.toLowerCase().includes('my name is')) {
        const match = message.content.match(/my name is (\w+)/i);
        if (match) return match[1];
      }
    }
    return null;
  }

  private calculateRelevanceScore(entity: any, currentIntent: EnhancedUserIntent): number {
    let score = 0.5; // Base score

    // Boost based on extracted filters
    Object.entries(currentIntent.extractedFilters).forEach(([key, value]) => {
      if (this.entityMatchesFilter(entity, key, value)) {
        score += 0.1;
      }
    });

    // Boost based on rating
    if (entity.avgRating) {
      score += (entity.avgRating / 5) * 0.2;
    }

    // Boost based on review count
    if (entity.reviewCount) {
      score += Math.min(0.1, Math.log(entity.reviewCount) / 100);
    }

    return Math.min(1, score);
  }

  private getMatchedFilters(entity: any, currentIntent: EnhancedUserIntent): string[] {
    const matched: string[] = [];

    Object.entries(currentIntent.extractedFilters).forEach(([key, value]) => {
      if (this.entityMatchesFilter(entity, key, value)) {
        matched.push(this.humanizeFilterKey(key));
      }
    });

    return matched;
  }

  private entityMatchesFilter(entity: any, filterKey: string, filterValue: any): boolean {
    // Simplified matching logic - in practice, this would be more sophisticated
    switch (filterKey) {
      case 'entityTypeIds':
        return Array.isArray(filterValue) && filterValue.includes(entity.entityType?.slug);
      case 'has_free_tier':
        return entity.hasFreeTier === filterValue;
      case 'has_api':
        return entity.hasApi === filterValue;
      default:
        return false;
    }
  }
}

// Supporting interfaces
export interface IntelligentChatResponse {
  message: string;
  conversationStage: string;
  metadata: {
    actionType: string;
    actionReason: string;
    filtersAccumulated: number;
    correctionHandled: boolean;
  };
  discoveredEntities: FormattedEntity[];
  strategicQuestions: SmartFollowUpQuestion[];
  suggestedActions: SuggestedAction[];
  readyForRecommendations: boolean;
  conflictResolution?: ConflictResolutionOption[];
  conversationGuidance?: ConversationGuidance;
}

export interface FormattedEntity {
  id: string;
  name: string;
  description: string;
  type: string;
  rating?: number;
  reviewCount?: number;
  relevanceScore: number;
  matchedFilters: string[];
}

export interface SuggestedAction {
  type: 'view_entities' | 'answer_question' | 'get_recommendations' | 'refine_search';
  label: string;
  description: string;
  priority: number;
}

export interface ConflictResolutionOption {
  filterKey: string;
  question: string;
  options: {
    label: string;
    value: any;
    confidence: number;
  }[];
}

export interface ConversationGuidance {
  efficiency: number;
  suggestions: string[];
  nextSteps: string[];
}
