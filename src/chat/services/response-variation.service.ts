import { Injectable, Logger } from '@nestjs/common';
import { ConversationContext, ChatResponse } from '../../common/llm/interfaces/llm.service.interface';

/**
 * Service to add variation to chat responses and prevent repetitive behavior
 */
@Injectable()
export class ResponseVariationService {
  private readonly logger = new Logger(ResponseVariationService.name);

  /**
   * Add variation to a chat response to prevent repetitive behavior
   */
  addVariationToResponse(
    response: ChatResponse,
    userMessage: string,
    context: ConversationContext
  ): ChatResponse {
    try {
      // Check if this is a repeated question
      const isRepeatedQuestion = this.isRepeatedQuestion(userMessage, context);
      
      if (isRepeatedQuestion) {
        this.logger.log(`Detected repeated question: "${userMessage.substring(0, 50)}..."`);
        return this.varyResponseForRepeatedQuestion(response, userMessage, context);
      }

      // Check if response is too similar to previous responses
      const isSimilarResponse = this.isSimilarToPreviousResponses(response.message, context);
      
      if (isSimilarResponse) {
        this.logger.log('Detected similar response, adding variation');
        return this.addResponseVariation(response, context);
      }

      return response;
    } catch (error) {
      this.logger.error('Error adding response variation', error);
      return response; // Return original response on error
    }
  }

  /**
   * Check if the user is asking a question they've asked before
   */
  private isRepeatedQuestion(userMessage: string, context: ConversationContext): boolean {
    const userMessages = context.messages?.filter(m => m.role === 'user') || [];
    const currentMessageLower = userMessage.toLowerCase().trim();
    
    return userMessages.some(msg => {
      const previousMessageLower = msg.content.toLowerCase().trim();
      return previousMessageLower === currentMessageLower && msg.content !== userMessage;
    });
  }

  /**
   * Check if the response is too similar to previous assistant responses
   */
  private isSimilarToPreviousResponses(responseMessage: string, context: ConversationContext): boolean {
    const assistantMessages = context.messages?.filter(m => m.role === 'assistant') || [];
    const currentResponseLower = responseMessage.toLowerCase();
    
    // Check for high similarity (more than 70% word overlap)
    return assistantMessages.some(msg => {
      const similarity = this.calculateTextSimilarity(currentResponseLower, msg.content.toLowerCase());
      return similarity > 0.7;
    });
  }

  /**
   * Calculate text similarity between two strings
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    const words1 = text1.split(/\s+/).filter(w => w.length > 3);
    const words2 = text2.split(/\s+/).filter(w => w.length > 3);
    
    if (words1.length === 0 || words2.length === 0) return 0;
    
    const commonWords = words1.filter(word => words2.includes(word));
    return commonWords.length / Math.max(words1.length, words2.length);
  }

  /**
   * Vary the response when user asks a repeated question
   */
  private varyResponseForRepeatedQuestion(
    response: ChatResponse,
    userMessage: string,
    context: ConversationContext
  ): ChatResponse {
    // TEMPORARILY DISABLED - Just return original response to avoid unnatural prefixes
    // The real issue is likely conversation memory not working
    this.logger.log('Repeated question detected but not adding variation prefix to avoid unnatural responses');

    return {
      ...response,
      followUpQuestions: this.generateAlternativeQuestions(response.followUpQuestions || [], context),
    };
  }

  /**
   * Add general variation to a response
   */
  private addResponseVariation(response: ChatResponse, context: ConversationContext): ChatResponse {
    // TEMPORARILY DISABLED - Just return original response to avoid unnatural prefixes
    this.logger.log('Similar response detected but not adding variation phrase to avoid unnatural responses');

    return {
      ...response,
      followUpQuestions: this.generateAlternativeQuestions(response.followUpQuestions || [], context),
    };
  }

  /**
   * Generate alternative follow-up questions
   */
  private generateAlternativeQuestions(originalQuestions: string[], context: ConversationContext): string[] {
    const messageCount = context.messages?.length || 0;
    
    // Alternative question sets based on conversation stage
    const alternativeQuestions = {
      early: [
        "What specific outcome are you hoping to achieve?",
        "What's driving your interest in AI tools right now?",
        "What challenges are you currently facing that AI might help with?",
      ],
      mid: [
        "What features would make the biggest difference for you?",
        "Are there any deal-breakers or must-have requirements?",
        "How do you envision integrating this into your workflow?",
      ],
      late: [
        "Would you like to see some specific recommendations?",
        "Should we compare a few options that might work for you?",
        "Are you ready to explore some concrete solutions?",
      ]
    };

    let questionSet: string[];
    if (messageCount <= 3) {
      questionSet = alternativeQuestions.early;
    } else if (messageCount <= 6) {
      questionSet = alternativeQuestions.mid;
    } else {
      questionSet = alternativeQuestions.late;
    }

    // Return 2 random questions from the appropriate set
    const shuffled = [...questionSet].sort(() => Math.random() - 0.5);
    return shuffled.slice(0, 2);
  }

  /**
   * Add contextual variation based on user's previous responses
   */
  addContextualVariation(response: ChatResponse, context: ConversationContext): ChatResponse {
    const userMessages = context.messages?.filter(m => m.role === 'user') || [];
    
    // Detect user's communication style
    const isVeryBrief = userMessages.some(m => m.content.length < 20);
    const isDetailed = userMessages.some(m => m.content.length > 100);
    const mentionsSpecifics = userMessages.some(m => 
      m.content.includes('specific') || m.content.includes('exactly') || m.content.includes('particular')
    );

    let styleAdjustment = '';
    
    if (isVeryBrief) {
      styleAdjustment = ' I\'ll keep this concise since you prefer brief responses.';
    } else if (isDetailed) {
      styleAdjustment = ' Let me provide some detailed insights since you appreciate thorough information.';
    } else if (mentionsSpecifics) {
      styleAdjustment = ' I\'ll focus on specific, actionable recommendations.';
    }

    if (styleAdjustment) {
      return {
        ...response,
        message: response.message + styleAdjustment,
      };
    }

    return response;
  }

  /**
   * Ensure response uniqueness by checking against conversation history
   */
  ensureResponseUniqueness(response: ChatResponse, context: ConversationContext): ChatResponse {
    const assistantMessages = context.messages?.filter(m => m.role === 'assistant') || [];
    
    // If we've given too many similar responses, force a different approach
    const similarResponseCount = assistantMessages.filter(msg => {
      const similarity = this.calculateTextSimilarity(
        response.message.toLowerCase(),
        msg.content.toLowerCase()
      );
      return similarity > 0.5;
    }).length;

    if (similarResponseCount > 1) {
      this.logger.warn(`Detected ${similarResponseCount} similar responses, forcing variation`);
      
      const forceVariationPrefixes = [
        "Let me try a completely different approach:",
        "Here's a fresh perspective on your question:",
        "Taking a step back, let me reframe this:",
        "Let me tackle this from a new angle:",
      ];

      const randomPrefix = forceVariationPrefixes[Math.floor(Math.random() * forceVariationPrefixes.length)];
      
      return {
        ...response,
        message: `${randomPrefix} ${response.message}`,
        followUpQuestions: this.generateAlternativeQuestions(response.followUpQuestions || [], context),
      };
    }

    return response;
  }
}
