import { Test, TestingModule } from '@nestjs/testing';
import { QuestionSuggestionService } from './services/question-suggestion.service';
import { ConversationContext } from '../common/llm/interfaces/llm.service.interface';

/**
 * Validation tests for anti-repetition functionality
 */
describe('Anti-Repetition Validation', () => {
  let service: QuestionSuggestionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [QuestionSuggestionService],
    }).compile();

    service = module.get<QuestionSuggestionService>(QuestionSuggestionService);
  });

  describe('Question Repetition Prevention', () => {
    it('should not ask about programming language after user mentions it', () => {
      const context: ConversationContext = {
        sessionId: 'test-session',
        userId: 'test-user',
        messages: [
          { id: '1', role: 'user', content: 'I need help with coding tools', timestamp: new Date() },
          { id: '2', role: 'assistant', content: 'What programming language are you working with?', timestamp: new Date() },
          { id: '3', role: 'user', content: 'I work with Python and JavaScript', timestamp: new Date() },
        ],
        discoveredEntities: [],
        userPreferences: {},
        conversationStage: 'discovery',
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 3,
          entitiesShown: [],
        },
      };

      const questions = service.generateFollowUpQuestions(context, 'I need help with code documentation', 3);

      // Should not ask about programming language again
      const hasLanguageQuestion = questions.some(q => 
        q.toLowerCase().includes('programming language') || 
        q.toLowerCase().includes('what language')
      );
      expect(hasLanguageQuestion).toBe(false);

      console.log('Questions after language mentioned:', questions);
    });

    it('should not ask about industry after user mentions their work', () => {
      const context: ConversationContext = {
        sessionId: 'test-session',
        userId: 'test-user',
        messages: [
          { id: '1', role: 'user', content: 'I\'m a school teacher looking for AI tools', timestamp: new Date() },
          { id: '2', role: 'assistant', content: 'What grade level do you teach?', timestamp: new Date() },
          { id: '3', role: 'user', content: 'I teach 5th grade', timestamp: new Date() },
        ],
        discoveredEntities: [],
        userPreferences: {},
        conversationStage: 'discovery',
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 3,
          entitiesShown: [],
        },
      };

      const questions = service.generateFollowUpQuestions(context, 'I need help with lesson planning', 3);

      // Should not ask about industry/work again
      const hasIndustryQuestion = questions.some(q => 
        q.toLowerCase().includes('industry') || 
        q.toLowerCase().includes('what field') ||
        q.toLowerCase().includes('what do you do')
      );
      expect(hasIndustryQuestion).toBe(false);

      console.log('Questions after industry mentioned:', questions);
    });

    it('should generate different questions for different conversation stages', () => {
      const baseContext = {
        sessionId: 'test-session',
        userId: 'test-user',
        discoveredEntities: [],
        userPreferences: {},
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 0,
          entitiesShown: [],
        },
      };

      // Early conversation
      const earlyContext: ConversationContext = {
        ...baseContext,
        messages: [
          { id: '1', role: 'user', content: 'I need AI tools', timestamp: new Date() },
        ],
        conversationStage: 'greeting',
        metadata: { ...baseContext.metadata, totalMessages: 1 },
      };

      // Later conversation
      const laterContext: ConversationContext = {
        ...baseContext,
        messages: [
          { id: '1', role: 'user', content: 'I need AI tools', timestamp: new Date() },
          { id: '2', role: 'assistant', content: 'What field do you work in?', timestamp: new Date() },
          { id: '3', role: 'user', content: 'I work in marketing', timestamp: new Date() },
          { id: '4', role: 'assistant', content: 'What specific marketing tasks?', timestamp: new Date() },
          { id: '5', role: 'user', content: 'Content creation and social media', timestamp: new Date() },
          { id: '6', role: 'assistant', content: 'What\'s your budget range?', timestamp: new Date() },
          { id: '7', role: 'user', content: 'Around $100 per month', timestamp: new Date() },
        ],
        conversationStage: 'refinement',
        metadata: { ...baseContext.metadata, totalMessages: 7 },
      };

      const earlyQuestions = service.generateFollowUpQuestions(earlyContext, 'I need AI tools', 2);
      const laterQuestions = service.generateFollowUpQuestions(laterContext, 'Show me some options', 2);

      // Questions should be different
      expect(earlyQuestions).not.toEqual(laterQuestions);

      // Later questions should be more action-oriented
      const hasActionQuestion = laterQuestions.some(q => 
        q.toLowerCase().includes('show') || 
        q.toLowerCase().includes('recommend') ||
        q.toLowerCase().includes('ready') ||
        q.toLowerCase().includes('compare')
      );
      expect(hasActionQuestion).toBe(true);

      console.log('Early questions:', earlyQuestions);
      console.log('Later questions:', laterQuestions);
    });

    it('should avoid asking the same question multiple times in a conversation', () => {
      const context: ConversationContext = {
        sessionId: 'test-session',
        userId: 'test-user',
        messages: [
          { id: '1', role: 'user', content: 'I need help', timestamp: new Date() },
          { id: '2', role: 'assistant', content: 'What specific challenge are you trying to solve?', timestamp: new Date() },
          { id: '3', role: 'user', content: 'I\'m not sure', timestamp: new Date() },
          { id: '4', role: 'assistant', content: 'What type of work do you do?', timestamp: new Date() },
          { id: '5', role: 'user', content: 'I work in healthcare', timestamp: new Date() },
        ],
        discoveredEntities: [],
        userPreferences: {},
        conversationStage: 'discovery',
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 5,
          entitiesShown: [],
        },
      };

      const questions = service.generateFollowUpQuestions(context, 'I still need help', 3);

      // Should not ask about work/industry again
      const hasWorkQuestion = questions.some(q => 
        q.toLowerCase().includes('what type of work') ||
        q.toLowerCase().includes('what do you do') ||
        q.toLowerCase().includes('industry')
      );
      expect(hasWorkQuestion).toBe(false);

      // Should not ask the same specific challenge question
      const hasSpecificQuestion = questions.some(q => 
        q.toLowerCase().includes('what specific challenge are you trying to solve')
      );
      expect(hasSpecificQuestion).toBe(false);

      console.log('Questions avoiding repetition:', questions);
    });
  });

  describe('Context-Aware Question Generation', () => {
    it('should generate education-specific questions when user mentions teaching', () => {
      const context: ConversationContext = {
        sessionId: 'test-session',
        userId: 'test-user',
        messages: [
          { id: '1', role: 'user', content: 'I\'m a teacher and need AI tools', timestamp: new Date() },
        ],
        discoveredEntities: [],
        userPreferences: {},
        conversationStage: 'discovery',
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 1,
          entitiesShown: [],
        },
      };

      const questions = service.generateFollowUpQuestions(context, 'I need help with grading', 3);

      // Should generate education-relevant questions
      const hasEducationQuestion = questions.some(q => 
        q.toLowerCase().includes('grade') ||
        q.toLowerCase().includes('student') ||
        q.toLowerCase().includes('lesson') ||
        q.toLowerCase().includes('teaching')
      );
      expect(hasEducationQuestion).toBe(true);

      console.log('Education-specific questions:', questions);
    });

    it('should generate programming-specific questions when user mentions coding', () => {
      const context: ConversationContext = {
        sessionId: 'test-session',
        userId: 'test-user',
        messages: [
          { id: '1', role: 'user', content: 'I need AI tools for coding', timestamp: new Date() },
        ],
        discoveredEntities: [],
        userPreferences: {},
        conversationStage: 'discovery',
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 1,
          entitiesShown: [],
        },
      };

      const questions = service.generateFollowUpQuestions(context, 'I need help with code documentation', 3);

      // Should generate programming-relevant questions
      const hasProgrammingQuestion = questions.some(q => 
        q.toLowerCase().includes('programming') ||
        q.toLowerCase().includes('language') ||
        q.toLowerCase().includes('code') ||
        q.toLowerCase().includes('development')
      );
      expect(hasProgrammingQuestion).toBe(true);

      console.log('Programming-specific questions:', questions);
    });
  });
});
