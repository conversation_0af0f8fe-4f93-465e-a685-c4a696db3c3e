import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../app.module';
import { AuthService } from '../auth/auth.service';

/**
 * Integration tests to validate that the chat service no longer exhibits repetitive behavior
 * These tests simulate real user conversations to ensure the chatbot provides varied, contextual responses
 */
describe('Chat Anti-Repetition Integration Tests', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get auth token for testing
    const authService = moduleFixture.get<AuthService>(AuthService);
    const testUser = await authService.createTestUser();
    authToken = testUser.token;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('School Teacher Scenario', () => {
    it('should handle "I\'m a school teacher, what tools should I be using?" without repetition', async () => {
      const sessionId = `teacher-test-${Date.now()}`;
      const responses: any[] = [];

      // Initial message
      const response1 = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: "I'm a school teacher, what tools should I be using?",
          session_id: sessionId,
        })
        .expect(200);

      responses.push(response1.body);

      // Follow-up messages to test for repetition
      const followUpMessages = [
        "I teach 5th grade",
        "I need help with lesson planning",
        "What about grading tools?",
        "I also need help with student engagement",
        "Are there any free options?"
      ];

      for (const message of followUpMessages) {
        const response = await request(app.getHttpServer())
          .post('/chat')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            message,
            session_id: sessionId,
          })
          .expect(200);

        responses.push(response.body);
      }

      // Validate no repetitive questions
      const allQuestions = responses.flatMap(r => r.follow_up_questions || []);
      const uniqueQuestions = new Set(allQuestions);
      
      expect(uniqueQuestions.size).toBe(allQuestions.length);
      expect(allQuestions).not.toContain('What grade level do you teach?'); // Should not ask after user said 5th grade
      expect(allQuestions).not.toContain('What subject do you teach?'); // Should not repeat similar questions

      // Validate responses build upon previous knowledge
      const laterResponses = responses.slice(2);
      laterResponses.forEach(response => {
        expect(response.message).not.toMatch(/what grade.*teach/i);
        expect(response.message).not.toMatch(/what subject.*teach/i);
      });

      console.log('Teacher scenario responses:', responses.map(r => ({
        message: r.message.substring(0, 100) + '...',
        questions: r.follow_up_questions
      })));
    });
  });

  describe('Developer Scenario', () => {
    it('should handle coding-related queries without asking the same programming questions repeatedly', async () => {
      const sessionId = `dev-test-${Date.now()}`;
      const responses: any[] = [];

      // Initial coding message
      const response1 = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: "I need help finding AI tools for code documentation",
          session_id: sessionId,
        })
        .expect(200);

      responses.push(response1.body);

      // Follow-up messages
      const followUpMessages = [
        "I work with Python and JavaScript",
        "I need automated documentation generation",
        "What about code review tools?",
        "I'm looking for something that integrates with VS Code"
      ];

      for (const message of followUpMessages) {
        const response = await request(app.getHttpServer())
          .post('/chat')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            message,
            session_id: sessionId,
          })
          .expect(200);

        responses.push(response.body);
      }

      // Validate no repetitive programming questions
      const allQuestions = responses.flatMap(r => r.follow_up_questions || []);
      const programmingQuestions = allQuestions.filter(q => 
        q.toLowerCase().includes('programming language') || 
        q.toLowerCase().includes('what language')
      );

      expect(programmingQuestions.length).toBeLessThanOrEqual(1); // Should only ask once
      
      // After user mentions Python/JavaScript, should not ask about programming language again
      const laterQuestions = responses.slice(2).flatMap(r => r.follow_up_questions || []);
      expect(laterQuestions).not.toContain('What programming language are you primarily working with?');

      console.log('Developer scenario responses:', responses.map(r => ({
        message: r.message.substring(0, 100) + '...',
        questions: r.follow_up_questions
      })));
    });
  });

  describe('Business User Scenario', () => {
    it('should handle business queries without repetitive industry questions', async () => {
      const sessionId = `business-test-${Date.now()}`;
      const responses: any[] = [];

      // Initial business message
      const response1 = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: "I work in marketing and need AI tools for my business",
          session_id: sessionId,
        })
        .expect(200);

      responses.push(response1.body);

      // Follow-up messages
      const followUpMessages = [
        "I need help with content creation",
        "What about social media automation?",
        "I'm looking for analytics tools",
        "Budget is around $200 per month"
      ];

      for (const message of followUpMessages) {
        const response = await request(app.getHttpServer())
          .post('/chat')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            message,
            session_id: sessionId,
          })
          .expect(200);

        responses.push(response.body);
      }

      // Validate no repetitive industry questions
      const allQuestions = responses.flatMap(r => r.follow_up_questions || []);
      const industryQuestions = allQuestions.filter(q => 
        q.toLowerCase().includes('industry') || 
        q.toLowerCase().includes('what field')
      );

      expect(industryQuestions.length).toBeLessThanOrEqual(1); // Should only ask once
      
      // After user mentions marketing, should not ask about industry again
      const laterQuestions = responses.slice(1).flatMap(r => r.follow_up_questions || []);
      expect(laterQuestions).not.toContain('What industry do you work in?');
      expect(laterQuestions).not.toContain('What field are you working in?');

      console.log('Business scenario responses:', responses.map(r => ({
        message: r.message.substring(0, 100) + '...',
        questions: r.follow_up_questions
      })));
    });
  });

  describe('Conversation Progression', () => {
    it('should show progressive conversation without loops', async () => {
      const sessionId = `progression-test-${Date.now()}`;
      const responses: any[] = [];

      const conversationFlow = [
        "I need help finding AI tools",
        "I work in healthcare",
        "I'm a nurse",
        "I need help with patient documentation",
        "I want something easy to use",
        "What are my options?",
        "Show me some recommendations"
      ];

      for (const message of conversationFlow) {
        const response = await request(app.getHttpServer())
          .post('/chat')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            message,
            session_id: sessionId,
          })
          .expect(200);

        responses.push(response.body);
      }

      // Validate conversation progresses logically
      expect(responses[0].conversation_stage).toBe('greeting');
      expect(responses[responses.length - 1].conversation_stage).toMatch(/recommendation|refinement/);

      // Validate no question repetition across the entire conversation
      const allQuestions = responses.flatMap(r => r.follow_up_questions || []);
      const uniqueQuestions = new Set(allQuestions);
      expect(uniqueQuestions.size).toBe(allQuestions.length);

      // Validate responses acknowledge previous information
      const laterResponses = responses.slice(3);
      laterResponses.forEach(response => {
        expect(response.message).not.toMatch(/what.*industry/i);
        expect(response.message).not.toMatch(/what.*field/i);
        expect(response.message).not.toMatch(/what.*work/i);
      });

      console.log('Conversation progression:', responses.map((r, i) => ({
        step: i + 1,
        stage: r.conversation_stage,
        message: r.message.substring(0, 80) + '...',
        questions: r.follow_up_questions?.length || 0
      })));
    });
  });

  describe('Edge Cases', () => {
    it('should handle vague responses without infinite question loops', async () => {
      const sessionId = `vague-test-${Date.now()}`;
      const responses: any[] = [];

      const vagueConversation = [
        "I need help",
        "With AI stuff",
        "I don't know",
        "Maybe tools",
        "I'm not sure what I need"
      ];

      for (const message of vagueConversation) {
        const response = await request(app.getHttpServer())
          .post('/chat')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            message,
            session_id: sessionId,
          })
          .expect(200);

        responses.push(response.body);
      }

      // Should eventually provide guidance or suggestions rather than endless questions
      const lastResponse = responses[responses.length - 1];
      expect(lastResponse.message).toMatch(/let me suggest|here are some|perhaps|maybe/i);
      
      // Should not ask the same clarifying questions repeatedly
      const allQuestions = responses.flatMap(r => r.follow_up_questions || []);
      const clarifyingQuestions = allQuestions.filter(q => 
        q.toLowerCase().includes('specific') || 
        q.toLowerCase().includes('what type')
      );
      expect(clarifyingQuestions.length).toBeLessThanOrEqual(2);

      console.log('Vague conversation handling:', responses.map(r => ({
        message: r.message.substring(0, 80) + '...',
        questions: r.follow_up_questions
      })));
    });
  });
});
