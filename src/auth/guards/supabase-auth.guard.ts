import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { SupabaseService } from '../../supabase/supabase.service';
import { PrismaService } from '../../prisma/prisma.service';
import { User as PublicUserModel } from '../../../generated/prisma';

export interface ReqUserObject {
  authData: any; // Supabase user data
  dbProfile: PublicUserModel | null;
  profileExistsInDb: boolean;
}

@Injectable()
export class SupabaseAuthGuard implements CanActivate {
  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly prismaService: PrismaService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    console.log('--- SUPABASE AUTH GUARD ACTIVATED ---');
    
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;
    
    console.log('Authorization header:', authHeader ? 'Present' : 'Missing');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.error('Missing or invalid authorization header');
      throw new UnauthorizedException('Missing or invalid authorization header.');
    }
    
    const jwtToken = authHeader.substring(7); // Remove 'Bearer ' prefix
    console.log('JWT Token extracted from header');

    try {
      // Use Supabase client to validate the JWT token
      const supabase = this.supabaseService.getClient();
      const { data: { user }, error } = await supabase.auth.getUser(jwtToken);

      if (error) {
        console.error('[SupabaseAuthGuard] Supabase JWT validation error:', error);
        throw new UnauthorizedException('Invalid JWT token: ' + error.message);
      }

      if (!user) {
        console.error('[SupabaseAuthGuard] No user returned from Supabase JWT validation');
        throw new UnauthorizedException('Invalid JWT token: no user found');
      }

      console.log(`[SupabaseAuthGuard] Supabase JWT validation successful for user: ${user.id}`);

      // Find the user in our public.users table using the authUserId
      console.log(`[SupabaseAuthGuard] Attempting to find user in database...`);
      const userProfile = await this.prismaService.user.findUnique({
        where: { authUserId: user.id },
      });

      if (userProfile) {
        console.log(`[SupabaseAuthGuard] User profile found in DB for authUserId: ${user.id}, public.users ID: ${userProfile.id}`);
      } else {
        console.log(`[SupabaseAuthGuard] User profile NOT found in DB for authUserId: ${user.id}. This is okay for initial sync.`);
      }

      // Attach user data to request
      const userObject: ReqUserObject = {
        authData: user,
        dbProfile: userProfile,
        profileExistsInDb: !!userProfile,
      };
      
      request.user = userObject;
      
      console.log('--- SUPABASE AUTH GUARD SUCCESS ---');
      return true;
      
    } catch (error) {
      console.error('--- SUPABASE AUTH GUARD ERROR ---');
      console.error('Error details:', error);
      console.error('Error stack:', error.stack);
      
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      
      throw new UnauthorizedException('Authentication failed: ' + error.message);
    }
  }
}
