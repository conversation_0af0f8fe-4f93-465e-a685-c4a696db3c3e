"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatisticsCalculatorService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let StatisticsCalculatorService = class StatisticsCalculatorService {
    constructor(prismaService) {
        this.prismaService = prismaService;
    }
    calculateReputationScore(stats) {
        const submissionPoints = stats.toolsSubmitted * 5;
        const approvalPoints = stats.toolsApproved * 10;
        const reviewPoints = stats.reviewsCount * 3;
        const requestPoints = stats.requestsFulfilled * 2;
        return submissionPoints + approvalPoints + reviewPoints + requestPoints;
    }
    async recalculateUserStatistics(userId) {
        const [bookmarksCount, reviewsCount, toolsSubmitted, toolsApproved, requestsMade, requestsFulfilled,] = await Promise.all([
            this.prismaService.userSavedEntity.count({
                where: { userId },
            }),
            this.prismaService.review.count({
                where: { userId },
            }),
            this.prismaService.userSubmittedTool.count({
                where: { userId },
            }),
            this.prismaService.userSubmittedTool.count({
                where: {
                    userId,
                    submissionStatus: { in: ['APPROVED', 'PUBLISHED'] },
                },
            }),
            this.prismaService.toolRequest.count({
                where: { userId },
            }),
            this.prismaService.toolRequest.count({
                where: {
                    userId,
                    status: 'COMPLETED',
                },
            }),
        ]);
        const stats = {
            bookmarksCount,
            reviewsCount,
            toolsSubmitted,
            toolsApproved,
            requestsMade,
            requestsFulfilled,
        };
        const reputationScore = this.calculateReputationScore(stats);
        return {
            ...stats,
            reputationScore,
        };
    }
    async updateUserStatistics(userId, stats) {
        await this.prismaService.user.update({
            where: { id: userId },
            data: {
                bookmarksCount: stats.bookmarksCount,
                reviewsCount: stats.reviewsCount,
                toolsSubmitted: stats.toolsSubmitted,
                toolsApproved: stats.toolsApproved,
                requestsMade: stats.requestsMade,
                requestsFulfilled: stats.requestsFulfilled,
                reputationScore: stats.reputationScore,
            },
        });
    }
    async recalculateAndUpdateUserStatistics(userId) {
        const stats = await this.recalculateUserStatistics(userId);
        await this.updateUserStatistics(userId, stats);
        return stats;
    }
    async recalculateAllUserStatistics() {
        const users = await this.prismaService.user.findMany({
            select: { id: true },
        });
        for (const user of users) {
            try {
                await this.recalculateAndUpdateUserStatistics(user.id);
            }
            catch (error) {
                console.error(`Failed to recalculate statistics for user ${user.id}:`, error);
            }
        }
    }
    async getUserStatistics(userId) {
        const user = await this.prismaService.user.findUnique({
            where: { id: userId },
            select: {
                bookmarksCount: true,
                reviewsCount: true,
                toolsSubmitted: true,
                toolsApproved: true,
                requestsMade: true,
                requestsFulfilled: true,
                reputationScore: true,
            },
        });
        return user;
    }
    async getReputationLeaderboard(limit = 10) {
        return this.prismaService.user.findMany({
            select: {
                id: true,
                username: true,
                displayName: true,
                reputationScore: true,
                profilePictureUrl: true,
            },
            orderBy: {
                reputationScore: 'desc',
            },
            take: limit,
            where: {
                reputationScore: {
                    gt: 0,
                },
            },
        });
    }
    async getUserReputationRank(userId) {
        const user = await this.prismaService.user.findUnique({
            where: { id: userId },
            select: { reputationScore: true },
        });
        if (!user)
            return null;
        const rank = await this.prismaService.user.count({
            where: {
                reputationScore: {
                    gt: user.reputationScore,
                },
            },
        });
        return rank + 1;
    }
};
exports.StatisticsCalculatorService = StatisticsCalculatorService;
exports.StatisticsCalculatorService = StatisticsCalculatorService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], StatisticsCalculatorService);
//# sourceMappingURL=statistics-calculator.service.js.map