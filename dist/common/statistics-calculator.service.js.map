{"version": 3, "file": "statistics-calculator.service.js", "sourceRoot": "", "sources": ["../../src/common/statistics-calculator.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6DAAyD;AAalD,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IACtC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAU7D,wBAAwB,CAAC,KAA8C;QACrE,MAAM,gBAAgB,GAAG,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC;QAClD,MAAM,cAAc,GAAG,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;QAC5C,MAAM,aAAa,GAAG,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAElD,OAAO,gBAAgB,GAAG,cAAc,GAAG,YAAY,GAAG,aAAa,CAAC;IAC1E,CAAC;IAMD,KAAK,CAAC,yBAAyB,CAAC,MAAc;QAC5C,MAAM,CACJ,cAAc,EACd,YAAY,EACZ,cAAc,EACd,aAAa,EACb,YAAY,EACZ,iBAAiB,EAClB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACpB,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,KAAK,CAAC;gBACvC,KAAK,EAAE,EAAE,MAAM,EAAE;aAClB,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC9B,KAAK,EAAE,EAAE,MAAM,EAAE;aAClB,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,KAAK,CAAC;gBACzC,KAAK,EAAE,EAAE,MAAM,EAAE;aAClB,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,KAAK,CAAC;gBACzC,KAAK,EAAE;oBACL,MAAM;oBACN,gBAAgB,EAAE,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE;iBACpD;aACF,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC;gBACnC,KAAK,EAAE,EAAE,MAAM,EAAE;aAClB,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC;gBACnC,KAAK,EAAE;oBACL,MAAM;oBACN,MAAM,EAAE,WAAW;iBACpB;aACF,CAAC;SACH,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG;YACZ,cAAc;YACd,YAAY;YACZ,cAAc;YACd,aAAa;YACb,YAAY;YACZ,iBAAiB;SAClB,CAAC;QAEF,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAE7D,OAAO;YACL,GAAG,KAAK;YACR,eAAe;SAChB,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,KAAqB;QAC9D,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC;YACnC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,IAAI,EAAE;gBACJ,cAAc,EAAE,KAAK,CAAC,cAAc;gBACpC,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,cAAc,EAAE,KAAK,CAAC,cAAc;gBACpC,aAAa,EAAE,KAAK,CAAC,aAAa;gBAClC,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,iBAAiB,EAAE,KAAK,CAAC,iBAAiB;gBAC1C,eAAe,EAAE,KAAK,CAAC,eAAe;aACvC;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,kCAAkC,CAAC,MAAc;QACrD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC/C,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,KAAK,CAAC,4BAA4B;QAChC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC;YACnD,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;SACrB,CAAC,CAAC;QAEH,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,6CAA6C,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAChF,CAAC;QACH,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,MAAc;QACpC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC;YACpD,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,MAAM,EAAE;gBACN,cAAc,EAAE,IAAI;gBACpB,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,IAAI;gBACpB,aAAa,EAAE,IAAI;gBACnB,YAAY,EAAE,IAAI;gBAClB,iBAAiB,EAAE,IAAI;gBACvB,eAAe,EAAE,IAAI;aACtB;SACF,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAAC,QAAgB,EAAE;QAO/C,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC;YACtC,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,IAAI;gBACjB,eAAe,EAAE,IAAI;gBACrB,iBAAiB,EAAE,IAAI;aACxB;YACD,OAAO,EAAE;gBACP,eAAe,EAAE,MAAM;aACxB;YACD,IAAI,EAAE,KAAK;YACX,KAAK,EAAE;gBACL,eAAe,EAAE;oBACf,EAAE,EAAE,CAAC;iBACN;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,MAAc;QACxC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC;YACpD,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,MAAM,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE;SAClC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAEvB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;YAC/C,KAAK,EAAE;gBACL,eAAe,EAAE;oBACf,EAAE,EAAE,IAAI,CAAC,eAAe;iBACzB;aACF;SACF,CAAC,CAAC;QAEH,OAAO,IAAI,GAAG,CAAC,CAAC;IAClB,CAAC;CACF,CAAA;AA/LY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;qCAEiC,8BAAa;GAD9C,2BAA2B,CA+LvC"}