import { ListEntitiesDto } from '../../entities/dto/list-entities.dto';
export declare class QueryOptimizationService {
    private readonly logger;
    private readonly queryMetrics;
    optimizeFilterOrder(filters: ListEntitiesDto): ListEntitiesDto;
    generateQueryHints(filters: ListEntitiesDto): QueryHints;
    optimizeBatchQueries(filterSets: ListEntitiesDto[]): BatchQueryPlan;
    recordQueryPerformance(querySignature: string, executionTime: number): void;
    getQueryPerformanceInsights(): QueryPerformanceInsights;
    private calculateFilterSelectivity;
    private applySelectivityOptimizations;
    private analyzeQueryComplexity;
    private suggestOptimalIndexes;
    private determineFilterStrategy;
    private optimizeJoinOrder;
    private groupSimilarQueries;
    private queriesAreSimilar;
    private calculateBatchPriority;
    private estimateBatchTime;
    private determineCacheStrategy;
    private generateOptimizationRecommendations;
}
interface QueryHints {
    useIndex: string[];
    joinOrder: string[];
    filterStrategy: FilterStrategy;
    estimatedComplexity: QueryComplexity;
}
interface BatchQueryPlan {
    batches: Array<{
        filters: ListEntitiesDto[];
        priority: number;
        estimatedTime: number;
    }>;
    estimatedTime: number;
    cacheStrategy: CacheStrategy;
}
interface QueryPerformanceInsights {
    totalQueries: number;
    avgResponseTime: number;
    slowestQueries: Array<{
        signature: string;
        count: number;
        avgTime: number;
    }>;
    mostFrequentQueries: Array<{
        signature: string;
        count: number;
        avgTime: number;
    }>;
    recommendations: string[];
}
type QueryComplexity = 'low' | 'medium' | 'high' | 'very_high';
type FilterStrategy = 'simple_where' | 'fts_first' | 'staged_filtering' | 'default';
type CacheStrategy = 'aggressive' | 'moderate' | 'minimal' | 'none';
export {};
