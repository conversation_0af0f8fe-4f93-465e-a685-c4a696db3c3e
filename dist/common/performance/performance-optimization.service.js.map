{"version": 3, "file": "performance-optimization.service.js", "sourceRoot": "", "sources": ["../../../src/common/performance/performance-optimization.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAcxC,IAAM,8BAA8B,sCAApC,MAAM,8BAA8B;IA4BzC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QA3BxC,WAAM,GAAG,IAAI,eAAM,CAAC,gCAA8B,CAAC,IAAI,CAAC,CAAC;QAGzD,0BAAqB,GAAG,IAAI,GAAG,EAAe,CAAC;QAC/C,uBAAkB,GAAG,IAAI,GAAG,EAAe,CAAC;QAC5C,sBAAiB,GAAG,IAAI,GAAG,EAAe,CAAC;QAC3C,2BAAsB,GAAG,IAAI,GAAG,EAAe,CAAC;QAGhD,uBAAkB,GAAG;YACpC,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,eAAe,EAAE,CAAC;YAClB,aAAa,EAAE,CAAC;YAChB,kBAAkB,EAAE,CAAC;YACrB,WAAW,EAAE,CAAC;SACf,CAAC;QAGe,gBAAW,GAAG;YAC7B,mBAAmB,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;YAClC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;YAC/B,eAAe,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;YAC/B,oBAAoB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;YACpC,YAAY,EAAE,IAAI;SACnB,CAAC;QAGA,IAAI,CAAC,+BAA+B,EAAE,CAAC;IACzC,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAC7B,WAAmB,EACnB,YAAgC;QAEhC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAG9D,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;QACvE,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;YACpC,OAAO,MAAM,CAAC;QAChB,CAAC;QAGD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,YAAY,EAAE,CAAC;YACpC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAG7C,IAAI,CAAC,UAAU,CACb,IAAI,CAAC,qBAAqB,EAC1B,QAAQ,EACR,MAAM,EACN,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACrC,CAAC;YAEF,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;YACtC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;YAEvC,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAC1B,QAAe,EACf,OAAY,EACZ,SAAsB;QAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAGjE,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QACpE,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;YACpC,OAAO,MAAM,CAAC;QAChB,CAAC;QAGD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;YAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAG7C,IAAI,CAAC,UAAU,CACb,IAAI,CAAC,kBAAkB,EACvB,QAAQ,EACR,MAAM,EACN,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAClC,CAAC;YAEF,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;YACtC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;YAEvC,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,qBAAqB,CACzB,KAAa,EACb,QAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAGxD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QACnE,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;YACpC,OAAO,MAAM,CAAC;QAChB,CAAC;QAGD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,QAAQ,EAAE,CAAC;YAChC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAG7C,IAAI,CAAC,UAAU,CACb,IAAI,CAAC,iBAAiB,EACtB,QAAQ,EACR,MAAM,EACN,IAAI,CAAC,WAAW,CAAC,eAAe,CACjC,CAAC;YAEF,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;YACtC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;YAEvC,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,0BAA0B,CAC9B,SAAiB,EACjB,OAA2B;QAE3B,MAAM,QAAQ,GAAG,gBAAgB,SAAS,EAAE,CAAC;QAG7C,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;QACxE,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;YACpC,OAAO,MAAM,CAAC;QAChB,CAAC;QAGD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,OAAO,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAG7C,IAAI,CAAC,UAAU,CACb,IAAI,CAAC,sBAAsB,EAC3B,QAAQ,EACR,MAAM,EACN,IAAI,CAAC,WAAW,CAAC,oBAAoB,CACtC,CAAC;YAEF,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;YACtC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;YAEvC,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACpE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAC5B,KAAU,EACV,SAAkC,EAClC,YAAoB,EAAE;QAEtB,MAAM,OAAO,GAAQ,EAAE,CAAC;QAGxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YACjD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;YAC5C,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;YAEzD,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBACtD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;YAChC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uDAAuD,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAE7F,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,mBAAmB;QACjB,MAAM,MAAM,GAAG;YACb,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,sBAAsB;SAC5B,CAAC;QAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;gBAE/C,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC5C,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;gBAClF,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;gBAE/C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,QAAQ,CAAC,MAAM,gBAAgB,CAAC,CAAC;YACnE,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,IAAI,CAAC,kBAAkB,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;IACrF,CAAC;IAKD,qBAAqB;QACnB,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS;YAClC,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC;QAEpG,OAAO;YACL,GAAG,IAAI,CAAC,kBAAkB;YAC1B,YAAY,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY;YACpD,UAAU,EAAE;gBACV,gBAAgB,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI;gBACjD,aAAa,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI;gBAC3C,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI;gBACzC,iBAAiB,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI;aACpD;YACD,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;SACrC,CAAC;IACJ,CAAC;IAKD,cAAc;QACZ,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAChC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QAEpC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IACpD,CAAC;IAGO,gBAAgB,CAAC,IAAY,EAAE,OAAe;QAEpD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,OAAO,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC;IAC3B,CAAC;IAEO,uBAAuB,CAAC,QAAe,EAAE,OAAY;QAC3D,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACzD,OAAO,WAAW,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,WAAW,EAAE,CAAC;IAC5D,CAAC;IAEO,YAAY,CAAC,KAAuB,EAAE,GAAW;QACvD,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC;QAGxB,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAC9B,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC;IACpB,CAAC;IAEO,UAAU,CAAC,KAAuB,EAAE,GAAW,EAAE,IAAS,EAAE,GAAW;QAC7E,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;YACb,IAAI;YACJ,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG;SACzB,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,aAAqB;QAC9C,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;QACxC,IAAI,CAAC,kBAAkB,CAAC,eAAe;YACrC,CAAC,IAAI,CAAC,kBAAkB,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;gBACvG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC;IAC1C,CAAC;IAEO,+BAA+B;QAErC,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAGlB,WAAW,CAAC,GAAG,EAAE;YACf,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;QACpD,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACrB,CAAC;IAEO,eAAe;QACrB,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAExC,IAAI,OAAO,CAAC,eAAe,GAAG,GAAG,IAAI,OAAO,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YAC/D,OAAO,WAAW,CAAC;QACrB,CAAC;aAAM,IAAI,OAAO,CAAC,eAAe,GAAG,IAAI,IAAI,OAAO,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC;YACxE,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,IAAI,OAAO,CAAC,eAAe,GAAG,IAAI,IAAI,OAAO,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC;YACxE,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAC5B,SAA2B,EAC3B,gBAAwB,EAAE;QAG1B,OAAO,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,IAAI,aAAa,EAAE,CAAC;YACnE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,CAAC;QAE7C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YACjC,OAAO,MAAM,CAAC;QAChB,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,CAAC;QAC/C,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS,CACb,iBAAqE;QAErE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAE7C,MAAM,eAAe,GAAG,iBAAiB,CAAC,GAAG,CAAC,KAAK,EAAC,QAAQ,EAAC,EAAE;YAC7D,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACrC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;gBAC9F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;YAC7D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,QAAQ,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACnF,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;QAC1C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;CACF,CAAA;AApYY,wEAA8B;yCAA9B,8BAA8B;IAD1C,IAAA,mBAAU,GAAE;qCA6BiC,sBAAa;GA5B9C,8BAA8B,CAoY1C"}