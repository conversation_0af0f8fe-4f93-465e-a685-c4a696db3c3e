"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PerformanceOptimizationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceOptimizationService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let PerformanceOptimizationService = PerformanceOptimizationService_1 = class PerformanceOptimizationService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(PerformanceOptimizationService_1.name);
        this.filterExtractionCache = new Map();
        this.entityRankingCache = new Map();
        this.vectorSearchCache = new Map();
        this.conversationStateCache = new Map();
        this.performanceMetrics = {
            cacheHits: 0,
            cacheMisses: 0,
            avgResponseTime: 0,
            totalRequests: 0,
            concurrentRequests: 0,
            memoryUsage: 0,
        };
        this.cacheConfig = {
            filterExtractionTTL: 5 * 60 * 1000,
            entityRankingTTL: 2 * 60 * 1000,
            vectorSearchTTL: 10 * 60 * 1000,
            conversationStateTTL: 30 * 60 * 1000,
            maxCacheSize: 1000,
        };
        this.initializePerformanceMonitoring();
    }
    async optimizedFilterExtraction(description, extractionFn) {
        const cacheKey = this.generateCacheKey('filter', description);
        const cached = this.getFromCache(this.filterExtractionCache, cacheKey);
        if (cached) {
            this.performanceMetrics.cacheHits++;
            return cached;
        }
        const startTime = Date.now();
        try {
            const result = await extractionFn();
            const executionTime = Date.now() - startTime;
            this.setInCache(this.filterExtractionCache, cacheKey, result, this.cacheConfig.filterExtractionTTL);
            this.performanceMetrics.cacheMisses++;
            this.updateResponseTime(executionTime);
            return result;
        }
        catch (error) {
            this.logger.error('Filter extraction failed', error.stack);
            throw error;
        }
    }
    async optimizedEntityRanking(entities, context, rankingFn) {
        const cacheKey = this.generateRankingCacheKey(entities, context);
        const cached = this.getFromCache(this.entityRankingCache, cacheKey);
        if (cached) {
            this.performanceMetrics.cacheHits++;
            return cached;
        }
        const startTime = Date.now();
        try {
            const result = rankingFn();
            const executionTime = Date.now() - startTime;
            this.setInCache(this.entityRankingCache, cacheKey, result, this.cacheConfig.entityRankingTTL);
            this.performanceMetrics.cacheMisses++;
            this.updateResponseTime(executionTime);
            return result;
        }
        catch (error) {
            this.logger.error('Entity ranking failed', error.stack);
            throw error;
        }
    }
    async optimizedVectorSearch(query, searchFn) {
        const cacheKey = this.generateCacheKey('vector', query);
        const cached = this.getFromCache(this.vectorSearchCache, cacheKey);
        if (cached) {
            this.performanceMetrics.cacheHits++;
            return cached;
        }
        const startTime = Date.now();
        try {
            const result = await searchFn();
            const executionTime = Date.now() - startTime;
            this.setInCache(this.vectorSearchCache, cacheKey, result, this.cacheConfig.vectorSearchTTL);
            this.performanceMetrics.cacheMisses++;
            this.updateResponseTime(executionTime);
            return result;
        }
        catch (error) {
            this.logger.error('Vector search failed', error.stack);
            throw error;
        }
    }
    async optimizedConversationState(sessionId, stateFn) {
        const cacheKey = `conversation:${sessionId}`;
        const cached = this.getFromCache(this.conversationStateCache, cacheKey);
        if (cached) {
            this.performanceMetrics.cacheHits++;
            return cached;
        }
        const startTime = Date.now();
        try {
            const result = await stateFn();
            const executionTime = Date.now() - startTime;
            this.setInCache(this.conversationStateCache, cacheKey, result, this.cacheConfig.conversationStateTTL);
            this.performanceMetrics.cacheMisses++;
            this.updateResponseTime(executionTime);
            return result;
        }
        catch (error) {
            this.logger.error('Conversation state loading failed', error.stack);
            throw error;
        }
    }
    async optimizedBatchProcessing(items, processFn, batchSize = 10) {
        const results = [];
        for (let i = 0; i < items.length; i += batchSize) {
            const batch = items.slice(i, i + batchSize);
            const batchPromises = batch.map(item => processFn(item));
            try {
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
            }
            catch (error) {
                this.logger.error(`Batch processing failed for batch starting at index ${i}`, error.stack);
            }
        }
        return results;
    }
    optimizeMemoryUsage() {
        const caches = [
            this.filterExtractionCache,
            this.entityRankingCache,
            this.vectorSearchCache,
            this.conversationStateCache,
        ];
        caches.forEach(cache => {
            if (cache.size > this.cacheConfig.maxCacheSize) {
                const entries = Array.from(cache.entries());
                const toRemove = entries.slice(0, entries.length - this.cacheConfig.maxCacheSize);
                toRemove.forEach(([key]) => cache.delete(key));
                this.logger.debug(`Cleaned up ${toRemove.length} cache entries`);
            }
        });
        this.performanceMetrics.memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024;
    }
    getPerformanceMetrics() {
        const cacheHitRate = this.performanceMetrics.cacheHits /
            (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses) * 100;
        return {
            ...this.performanceMetrics,
            cacheHitRate: isNaN(cacheHitRate) ? 0 : cacheHitRate,
            cacheStats: {
                filterExtraction: this.filterExtractionCache.size,
                entityRanking: this.entityRankingCache.size,
                vectorSearch: this.vectorSearchCache.size,
                conversationState: this.conversationStateCache.size,
            },
            systemHealth: this.getSystemHealth(),
        };
    }
    clearAllCaches() {
        this.filterExtractionCache.clear();
        this.entityRankingCache.clear();
        this.vectorSearchCache.clear();
        this.conversationStateCache.clear();
        this.logger.log('All performance caches cleared');
    }
    generateCacheKey(type, content) {
        const hash = Buffer.from(content).toString('base64').slice(0, 16);
        return `${type}:${hash}`;
    }
    generateRankingCacheKey(entities, context) {
        const entityIds = entities.map(e => e.id).sort().join(',');
        const contextHash = JSON.stringify(context).slice(0, 50);
        return `ranking:${entityIds.slice(0, 50)}:${contextHash}`;
    }
    getFromCache(cache, key) {
        const entry = cache.get(key);
        if (!entry)
            return null;
        if (Date.now() > entry.expiry) {
            cache.delete(key);
            return null;
        }
        return entry.data;
    }
    setInCache(cache, key, data, ttl) {
        cache.set(key, {
            data,
            expiry: Date.now() + ttl,
        });
    }
    updateResponseTime(executionTime) {
        this.performanceMetrics.totalRequests++;
        this.performanceMetrics.avgResponseTime =
            (this.performanceMetrics.avgResponseTime * (this.performanceMetrics.totalRequests - 1) + executionTime) /
                this.performanceMetrics.totalRequests;
    }
    initializePerformanceMonitoring() {
        setInterval(() => {
            this.optimizeMemoryUsage();
        }, 5 * 60 * 1000);
        setInterval(() => {
            const metrics = this.getPerformanceMetrics();
            this.logger.debug('Performance metrics', metrics);
        }, 10 * 60 * 1000);
    }
    getSystemHealth() {
        const metrics = this.performanceMetrics;
        if (metrics.avgResponseTime < 500 && metrics.memoryUsage < 512) {
            return 'EXCELLENT';
        }
        else if (metrics.avgResponseTime < 1000 && metrics.memoryUsage < 1024) {
            return 'GOOD';
        }
        else if (metrics.avgResponseTime < 2000 && metrics.memoryUsage < 2048) {
            return 'FAIR';
        }
        else {
            return 'POOR';
        }
    }
    async manageConcurrentRequests(requestFn, maxConcurrent = 10) {
        while (this.performanceMetrics.concurrentRequests >= maxConcurrent) {
            await new Promise(resolve => setTimeout(resolve, 10));
        }
        this.performanceMetrics.concurrentRequests++;
        try {
            const result = await requestFn();
            return result;
        }
        finally {
            this.performanceMetrics.concurrentRequests--;
        }
    }
    async warmCache(warmingStrategies) {
        this.logger.log('Starting cache warming...');
        const warmingPromises = warmingStrategies.map(async (strategy) => {
            try {
                const data = await strategy.loadFn();
                this.setInCache(this.vectorSearchCache, strategy.key, data, this.cacheConfig.vectorSearchTTL);
                this.logger.debug(`Cache warmed for key: ${strategy.key}`);
            }
            catch (error) {
                this.logger.warn(`Cache warming failed for key: ${strategy.key}`, error.message);
            }
        });
        await Promise.allSettled(warmingPromises);
        this.logger.log('Cache warming completed');
    }
};
exports.PerformanceOptimizationService = PerformanceOptimizationService;
exports.PerformanceOptimizationService = PerformanceOptimizationService = PerformanceOptimizationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], PerformanceOptimizationService);
//# sourceMappingURL=performance-optimization.service.js.map