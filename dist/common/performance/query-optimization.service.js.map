{"version": 3, "file": "query-optimization.service.js", "sourceRoot": "", "sources": ["../../../src/common/performance/query-optimization.service.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAoD;AAc7C,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAA9B;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;QAGnD,iBAAY,GAAG,IAAI,GAAG,EAKnC,CAAC;IAuXP,CAAC;IAlXC,mBAAmB,CAAC,OAAwB;QAC1C,MAAM,SAAS,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QAGjC,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAEnE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,iBAAiB,CAAC,CAAC;QAGpE,OAAO,IAAI,CAAC,6BAA6B,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;IAC1E,CAAC;IAKD,kBAAkB,CAAC,OAAwB;QACzC,MAAM,KAAK,GAAe;YACxB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,EAAE;YACb,cAAc,EAAE,SAAS;YACzB,mBAAmB,EAAE,KAAK;SAC3B,CAAC;QAGF,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACxD,KAAK,CAAC,mBAAmB,GAAG,UAAU,CAAC;QAGvC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAGrD,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAGzE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAElD,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,oBAAoB,CAAC,UAA6B;QAChD,MAAM,IAAI,GAAmB;YAC3B,OAAO,EAAE,EAAE;YACX,aAAa,EAAE,CAAC;YAChB,aAAa,EAAE,MAAM;SACtB,CAAC;QAGF,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAG5D,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC1C,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;YAC5C,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;SAC7C,CAAC,CAAC,CAAC;QAGJ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAGrD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QAGvF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAE7D,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,sBAAsB,CAAC,cAAsB,EAAE,aAAqB;QAClE,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAEvD,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,KAAK,EAAE,CAAC;YACjB,QAAQ,CAAC,SAAS,IAAI,aAAa,CAAC;YACpC,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;YACvD,QAAQ,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,EAAE;gBACpC,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,aAAa;gBACxB,OAAO,EAAE,aAAa;gBACtB,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,aAAa,GAAG,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,cAAc,KAAK,aAAa,KAAK,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAKD,2BAA2B;QACzB,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAClF,SAAS;YACT,GAAG,IAAI;SACR,CAAC,CAAC,CAAC;QAGJ,MAAM,cAAc,GAAG,OAAO;aAC3B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;aACrC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAGhB,MAAM,mBAAmB,GAAG,OAAO;aAChC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;aACjC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAGhB,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAClE,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;QAExF,OAAO;YACL,YAAY;YACZ,eAAe;YACf,cAAc;YACd,mBAAmB;YACnB,eAAe,EAAE,IAAI,CAAC,mCAAmC,CAAC,OAAO,CAAC;SACnE,CAAC;IACJ,CAAC;IAGO,0BAA0B,CAAC,OAAwB;QACzD,MAAM,WAAW,GAAsB,EAAE,CAAC;QAG1C,IAAI,OAAO,CAAC,aAAa,EAAE,MAAM,EAAE,CAAC;YAClC,WAAW,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC7E,CAAC;QAGD,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,WAAW,CAAC,UAAU,GAAG,GAAG,CAAC;QAC/B,CAAC;QAGD,IAAI,OAAO,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC;YAChC,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC;QACjC,CAAC;QAGD,IAAI,OAAO,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAC;YACrC,WAAW,CAAC,gBAAgB,GAAG,GAAG,CAAC;QACrC,CAAC;QAGD,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACxC,WAAW,CAAC,aAAa,GAAG,GAAG,CAAC;QAClC,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAClC,WAAW,CAAC,OAAO,GAAG,GAAG,CAAC;QAC5B,CAAC;QAGD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,6BAA6B,CACnC,OAAwB,EACxB,WAA8B;QAK9B,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,sBAAsB,CAAC,OAAwB;QACrD,IAAI,UAAU,GAAG,CAAC,CAAC;QAGnB,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACtD,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,CAC1C,CAAC,MAAM,CAAC;QAET,UAAU,IAAI,aAAa,CAAC;QAG5B,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC;YAAE,UAAU,IAAI,CAAC,CAAC;QAC/E,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;YAAE,UAAU,IAAI,CAAC,CAAC;QAC3E,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC;YAAE,UAAU,IAAI,CAAC,CAAC;QAGrF,IAAI,OAAO,CAAC,UAAU;YAAE,UAAU,IAAI,CAAC,CAAC;QAGxC,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS;YAAE,UAAU,IAAI,CAAC,CAAC;QAC5D,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU;YAAE,UAAU,IAAI,CAAC,CAAC;QAE9D,IAAI,UAAU,IAAI,CAAC;YAAE,OAAO,KAAK,CAAC;QAClC,IAAI,UAAU,IAAI,CAAC;YAAE,OAAO,QAAQ,CAAC;QACrC,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QACpC,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,qBAAqB,CAAC,OAAwB;QACpD,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,uBAAuB,CAC7B,OAAwB,EACxB,UAA2B;QAE3B,IAAI,UAAU,KAAK,KAAK,EAAE,CAAC;YACzB,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;YAClD,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,IAAI,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,WAAW,EAAE,CAAC;YACxD,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,iBAAiB,CAAC,OAAwB;QAChD,MAAM,KAAK,GAAa,CAAC,UAAU,CAAC,CAAC;QAGrC,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,KAAK,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACrD,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,mBAAmB,CAAC,UAA6B;QAEvD,MAAM,MAAM,GAAwB,EAAE,CAAC;QAEvC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC3B,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACxC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAC1C,CAAC;YAEF,IAAI,aAAa,EAAE,CAAC;gBAClB,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACzB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,iBAAiB,CAAC,CAAkB,EAAE,CAAkB;QAE9D,MAAM,YAAY,GAAG,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACtD,MAAM,YAAY,GAAG,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEtD,OAAO,YAAY,KAAK,YAAY;YAC7B,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;IACvD,CAAC;IAEO,sBAAsB,CAAC,KAAwB;QAErD,IAAI,QAAQ,GAAG,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjC,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,KAAK;gBAAE,QAAQ,IAAI,EAAE,CAAC;gBAAC,MAAM;YAClC,KAAK,QAAQ;gBAAE,QAAQ,IAAI,EAAE,CAAC;gBAAC,MAAM;YACrC,KAAK,MAAM;gBAAE,QAAQ,IAAI,CAAC,CAAC;gBAAC,MAAM;YAClC,KAAK,WAAW;gBAAE,QAAQ,IAAI,CAAC,CAAC;gBAAC,MAAM;QACzC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,iBAAiB,CAAC,KAAwB;QAEhD,MAAM,QAAQ,GAAG,GAAG,CAAC;QACrB,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzD,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,KAAK;gBAAE,UAAU,GAAG,CAAC,CAAC;gBAAC,MAAM;YAClC,KAAK,QAAQ;gBAAE,UAAU,GAAG,GAAG,CAAC;gBAAC,MAAM;YACvC,KAAK,MAAM;gBAAE,UAAU,GAAG,CAAC,CAAC;gBAAC,MAAM;YACnC,KAAK,WAAW;gBAAE,UAAU,GAAG,CAAC,CAAC;gBAAC,MAAM;QAC1C,CAAC;QAED,OAAO,QAAQ,GAAG,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;IAC9C,CAAC;IAEO,sBAAsB,CAAC,UAA6B;QAC1D,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3E,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC;QAEvC,MAAM,cAAc,GAAG,CAAC,YAAY,GAAG,aAAa,CAAC,GAAG,YAAY,CAAC;QAErE,IAAI,cAAc,GAAG,GAAG,EAAE,CAAC;YACzB,OAAO,YAAY,CAAC;QACtB,CAAC;aAAM,IAAI,cAAc,GAAG,GAAG,EAAE,CAAC;YAChC,OAAO,UAAU,CAAC;QACpB,CAAC;aAAM,CAAC;YACN,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAEO,mCAAmC,CACzC,OAAqE;QAErE,MAAM,eAAe,GAAa,EAAE,CAAC;QAGrC,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC;QACzD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,eAAe,CAAC,IAAI,CAAC,uBAAuB,WAAW,CAAC,MAAM,eAAe,CAAC,CAAC;QACjF,CAAC;QAGD,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;QAC3D,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,eAAe,CAAC,IAAI,CAAC,gCAAgC,eAAe,CAAC,MAAM,mBAAmB,CAAC,CAAC;QAClG,CAAC;QAGD,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;CACF,CAAA;AAhYY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;GACA,wBAAwB,CAgYpC"}