import { ConfigService } from '@nestjs/config';
export declare class PerformanceOptimizationService {
    private readonly configService;
    private readonly logger;
    private readonly filterExtractionCache;
    private readonly entityRankingCache;
    private readonly vectorSearchCache;
    private readonly conversationStateCache;
    private readonly performanceMetrics;
    private readonly cacheConfig;
    constructor(configService: ConfigService);
    optimizedFilterExtraction(description: string, extractionFn: () => Promise<any>): Promise<any>;
    optimizedEntityRanking(entities: any[], context: any, rankingFn: () => any[]): Promise<any[]>;
    optimizedVectorSearch(query: string, searchFn: () => Promise<any[]>): Promise<any[]>;
    optimizedConversationState(sessionId: string, stateFn: () => Promise<any>): Promise<any>;
    optimizedBatchProcessing<T, R>(items: T[], processFn: (item: T) => Promise<R>, batchSize?: number): Promise<R[]>;
    optimizeMemoryUsage(): void;
    getPerformanceMetrics(): any;
    clearAllCaches(): void;
    private generateCacheKey;
    private generateRankingCacheKey;
    private getFromCache;
    private setInCache;
    private updateResponseTime;
    private initializePerformanceMonitoring;
    private getSystemHealth;
    manageConcurrentRequests<T>(requestFn: () => Promise<T>, maxConcurrent?: number): Promise<T>;
    warmCache(warmingStrategies: Array<{
        key: string;
        loadFn: () => Promise<any>;
    }>): Promise<void>;
}
