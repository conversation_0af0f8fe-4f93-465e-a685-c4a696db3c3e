import { EnhancedUserIntent } from '../llm/interfaces/llm.service.interface';
export declare class AdvancedEntityRankingService {
    private readonly logger;
    rankEntities(entities: any[], context: RankingContext): RankedEntity[];
    private calculateComprehensiveScores;
    private calculateVectorSimilarityScore;
    private calculateFilterMatchScore;
    private calculateEntityQualityScore;
    private calculateSocialProofScore;
    private calculateUserPreferenceScore;
    private calculatePersonalizedRelevanceScore;
    private calculateRecencyScore;
    private calculatePopularityScore;
    private calculateDiversityBonus;
    private calculateTrendingBonus;
    private calculateWeightedFinalScore;
    private calculateContentCompleteness;
    private entityMatchesFilter;
    private calculateTechnicalLevelAlignment;
    private calculateBudgetAlignment;
    private findSimilarViewedEntities;
    private calculateSkillProgressionMatch;
    private calculateCollaborativeScore;
    private generateRankingReason;
}
export interface RankingContext {
    appliedFilters?: Record<string, any>;
    filterConfidence?: Record<string, number>;
    userPreferences?: any;
    userHistory?: {
        viewedEntities?: any[];
        skillProgression?: any;
    };
    collaborativeSignals?: any;
    currentResults?: any[];
    customWeights?: Record<string, number>;
    intent?: EnhancedUserIntent;
}
export interface EntityScores {
    vectorSimilarity: number;
    filterMatch: number;
    entityQuality: number;
    socialProof: number;
    userPreference: number;
    personalizedRelevance: number;
    recency: number;
    popularity: number;
    diversityBonus: number;
    trendingBonus: number;
}
export interface RankedEntity {
    rankingScore: number;
    rankingBreakdown: EntityScores;
    rankingReason: string;
    [key: string]: any;
}
