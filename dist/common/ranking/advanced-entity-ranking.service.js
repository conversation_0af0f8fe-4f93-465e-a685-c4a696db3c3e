"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AdvancedEntityRankingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedEntityRankingService = void 0;
const common_1 = require("@nestjs/common");
let AdvancedEntityRankingService = AdvancedEntityRankingService_1 = class AdvancedEntityRankingService {
    constructor() {
        this.logger = new common_1.Logger(AdvancedEntityRankingService_1.name);
    }
    rankEntities(entities, context) {
        this.logger.debug(`Ranking ${entities.length} entities with advanced scoring`);
        const rankedEntities = entities.map(entity => {
            const scores = this.calculateComprehensiveScores(entity, context);
            const finalScore = this.calculateWeightedFinalScore(scores, context);
            return {
                ...entity,
                rankingScore: finalScore,
                rankingBreakdown: scores,
                rankingReason: this.generateRankingReason(scores, context),
            };
        });
        const sorted = rankedEntities.sort((a, b) => b.rankingScore - a.rankingScore);
        this.logger.debug('Entity ranking completed', {
            topScore: sorted[0]?.rankingScore,
            averageScore: sorted.reduce((sum, e) => sum + e.rankingScore, 0) / sorted.length,
            entitiesRanked: sorted.length,
        });
        return sorted;
    }
    calculateComprehensiveScores(entity, context) {
        return {
            vectorSimilarity: this.calculateVectorSimilarityScore(entity, context),
            filterMatch: this.calculateFilterMatchScore(entity, context),
            entityQuality: this.calculateEntityQualityScore(entity),
            socialProof: this.calculateSocialProofScore(entity),
            userPreference: this.calculateUserPreferenceScore(entity, context),
            personalizedRelevance: this.calculatePersonalizedRelevanceScore(entity, context),
            recency: this.calculateRecencyScore(entity),
            popularity: this.calculatePopularityScore(entity),
            diversityBonus: this.calculateDiversityBonus(entity, context),
            trendingBonus: this.calculateTrendingBonus(entity),
        };
    }
    calculateVectorSimilarityScore(entity, context) {
        return Math.min(1, Math.max(0, entity.similarity || 0.5));
    }
    calculateFilterMatchScore(entity, context) {
        if (!context.appliedFilters || Object.keys(context.appliedFilters).length === 0) {
            return 0.5;
        }
        let matchCount = 0;
        let totalFilters = 0;
        let weightedScore = 0;
        Object.entries(context.appliedFilters).forEach(([filterKey, filterValue]) => {
            if (filterValue === undefined || filterValue === null)
                return;
            totalFilters++;
            const confidence = context.filterConfidence?.[filterKey] || 0.7;
            if (this.entityMatchesFilter(entity, filterKey, filterValue)) {
                matchCount++;
                weightedScore += confidence;
            }
        });
        if (totalFilters === 0)
            return 0.5;
        const matchRatio = matchCount / totalFilters;
        const avgConfidenceScore = weightedScore / totalFilters;
        return (matchRatio * 0.7) + (avgConfidenceScore * 0.3);
    }
    calculateEntityQualityScore(entity) {
        let score = 0.5;
        if (entity.avgRating) {
            score += (entity.avgRating / 5) * 0.4;
        }
        if (entity.reviewCount) {
            const reviewScore = Math.min(1, Math.log(entity.reviewCount + 1) / 10);
            score += reviewScore * 0.3;
        }
        const completeness = this.calculateContentCompleteness(entity);
        score += completeness * 0.2;
        if (entity.isVerified) {
            score += 0.1;
        }
        return Math.min(1, score);
    }
    calculateSocialProofScore(entity) {
        let score = 0;
        if (entity.githubStars) {
            score += Math.min(0.3, Math.log(entity.githubStars + 1) / 30);
        }
        if (entity.downloadCount || entity.usageCount) {
            const usage = entity.downloadCount || entity.usageCount;
            score += Math.min(0.3, Math.log(usage + 1) / 25);
        }
        if (entity.communitySize) {
            score += Math.min(0.2, Math.log(entity.communitySize + 1) / 20);
        }
        if (entity.mediaMentions) {
            score += Math.min(0.2, entity.mediaMentions / 50);
        }
        return Math.min(1, score);
    }
    calculateUserPreferenceScore(entity, context) {
        if (!context.userPreferences)
            return 0.5;
        let score = 0.5;
        const prefs = context.userPreferences;
        if (prefs.preferred_categories?.length > 0) {
            const entityCategories = entity.categories?.map((c) => c.category.name) || [];
            const hasPreferredCategory = prefs.preferred_categories.some((pref) => entityCategories.includes(pref));
            if (hasPreferredCategory)
                score += 0.3;
        }
        if (prefs.excluded_categories?.length > 0) {
            const entityCategories = entity.categories?.map((c) => c.category.name) || [];
            const hasExcludedCategory = prefs.excluded_categories.some((excl) => entityCategories.includes(excl));
            if (hasExcludedCategory)
                score -= 0.4;
        }
        if (prefs.technical_level && entity.technicalLevel) {
            const levelMatch = this.calculateTechnicalLevelAlignment(prefs.technical_level, entity.technicalLevel);
            score += levelMatch * 0.2;
        }
        if (prefs.budget && entity.pricing) {
            const budgetMatch = this.calculateBudgetAlignment(prefs.budget, entity.pricing);
            score += budgetMatch * 0.2;
        }
        return Math.max(0, Math.min(1, score));
    }
    calculatePersonalizedRelevanceScore(entity, context) {
        let score = 0.5;
        if (context.userHistory?.viewedEntities) {
            const similarViewed = this.findSimilarViewedEntities(entity, context.userHistory.viewedEntities);
            score += Math.min(0.3, similarViewed.length * 0.1);
        }
        if (context.userHistory?.skillProgression) {
            const progressionMatch = this.calculateSkillProgressionMatch(entity, context.userHistory.skillProgression);
            score += progressionMatch * 0.2;
        }
        if (context.collaborativeSignals) {
            score += this.calculateCollaborativeScore(entity, context.collaborativeSignals) * 0.3;
        }
        return Math.min(1, score);
    }
    calculateRecencyScore(entity) {
        if (!entity.updatedAt && !entity.createdAt)
            return 0.5;
        const entityDate = new Date(entity.updatedAt || entity.createdAt);
        const now = new Date();
        const daysSinceUpdate = (now.getTime() - entityDate.getTime()) / (1000 * 60 * 60 * 24);
        if (daysSinceUpdate < 30)
            return 1.0;
        if (daysSinceUpdate < 90)
            return 0.8;
        if (daysSinceUpdate < 180)
            return 0.6;
        if (daysSinceUpdate < 365)
            return 0.4;
        return 0.2;
    }
    calculatePopularityScore(entity) {
        let score = 0;
        if (entity.recentViews) {
            score += Math.min(0.4, Math.log(entity.recentViews + 1) / 15);
        }
        if (entity.trendingScore) {
            score += Math.min(0.3, entity.trendingScore);
        }
        if (entity.searchFrequency) {
            score += Math.min(0.3, entity.searchFrequency / 100);
        }
        return Math.min(1, score);
    }
    calculateDiversityBonus(entity, context) {
        if (!context.currentResults || context.currentResults.length === 0)
            return 0;
        const entityType = entity.entityType?.slug;
        const entityCategory = entity.categories?.[0]?.category?.name;
        const typeCount = context.currentResults.filter(r => r.entityType?.slug === entityType).length;
        const categoryCount = context.currentResults.filter(r => r.categories?.[0]?.category?.name === entityCategory).length;
        let bonus = 0;
        if (typeCount === 0)
            bonus += 0.3;
        else if (typeCount === 1)
            bonus += 0.1;
        if (categoryCount === 0)
            bonus += 0.2;
        else if (categoryCount === 1)
            bonus += 0.05;
        return Math.min(0.5, bonus);
    }
    calculateTrendingBonus(entity) {
        let bonus = 0;
        if (entity.growthRate && entity.growthRate > 1.2) {
            bonus += Math.min(0.3, (entity.growthRate - 1) * 0.5);
        }
        if (entity.isFeatured)
            bonus += 0.2;
        if (entity.isEditorChoice)
            bonus += 0.15;
        if (entity.isNewRelease)
            bonus += 0.1;
        return Math.min(0.5, bonus);
    }
    calculateWeightedFinalScore(scores, context) {
        const weights = context.customWeights || {
            vectorSimilarity: 0.25,
            filterMatch: 0.15,
            entityQuality: 0.15,
            socialProof: 0.10,
            userPreference: 0.12,
            personalizedRelevance: 0.08,
            recency: 0.05,
            popularity: 0.05,
            diversityBonus: 0.03,
            trendingBonus: 0.02,
        };
        let finalScore = 0;
        Object.entries(weights).forEach(([factor, weight]) => {
            finalScore += (scores[factor] || 0) * weight;
        });
        return Math.max(0, Math.min(1, finalScore));
    }
    calculateContentCompleteness(entity) {
        let completeness = 0;
        const fields = ['name', 'description', 'shortDescription', 'website', 'pricing'];
        fields.forEach(field => {
            if (entity[field] && entity[field].length > 0) {
                completeness += 0.2;
            }
        });
        return completeness;
    }
    entityMatchesFilter(entity, filterKey, filterValue) {
        switch (filterKey) {
            case 'entityTypeIds':
                return Array.isArray(filterValue) && filterValue.includes(entity.entityType?.slug);
            case 'technical_levels':
                return Array.isArray(filterValue) && entity.technicalLevel &&
                    filterValue.includes(entity.technicalLevel);
            case 'has_free_tier':
                return entity.hasFreeTier === filterValue;
            case 'has_api':
                return entity.hasApi === filterValue;
            case 'platforms':
                return Array.isArray(filterValue) && entity.platforms &&
                    filterValue.some(p => entity.platforms.includes(p));
            case 'price_ranges':
                return Array.isArray(filterValue) && entity.priceRange &&
                    filterValue.includes(entity.priceRange);
            default:
                return false;
        }
    }
    calculateTechnicalLevelAlignment(userLevel, entityLevel) {
        const levels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
        const userIndex = levels.indexOf(userLevel);
        const entityIndex = levels.indexOf(entityLevel);
        if (userIndex === -1 || entityIndex === -1)
            return 0;
        const distance = Math.abs(userIndex - entityIndex);
        return Math.max(0, 1 - (distance * 0.3));
    }
    calculateBudgetAlignment(userBudget, entityPricing) {
        if (userBudget === 'free' && entityPricing?.hasFreeTier)
            return 1;
        if (userBudget === 'low' && entityPricing?.priceRange === 'LOW')
            return 1;
        if (userBudget === 'medium' && ['LOW', 'MEDIUM'].includes(entityPricing?.priceRange))
            return 0.8;
        return 0.3;
    }
    findSimilarViewedEntities(entity, viewedEntities) {
        return viewedEntities.filter(viewed => viewed.entityType?.slug === entity.entityType?.slug ||
            viewed.categories?.some((c) => entity.categories?.some((ec) => ec.category.name === c.category.name)));
    }
    calculateSkillProgressionMatch(entity, skillProgression) {
        if (!skillProgression.currentLevel || !entity.technicalLevel)
            return 0;
        return this.calculateTechnicalLevelAlignment(skillProgression.currentLevel, entity.technicalLevel);
    }
    calculateCollaborativeScore(entity, collaborativeSignals) {
        return Math.min(1, (collaborativeSignals.similarUserLikes || 0) / 10);
    }
    generateRankingReason(scores, context) {
        const reasons = [];
        if (scores.vectorSimilarity > 0.8)
            reasons.push('highly relevant to your query');
        if (scores.filterMatch > 0.8)
            reasons.push('matches all your criteria');
        if (scores.entityQuality > 0.8)
            reasons.push('excellent quality and ratings');
        if (scores.userPreference > 0.7)
            reasons.push('aligns with your preferences');
        if (scores.socialProof > 0.7)
            reasons.push('popular in the community');
        if (scores.recency > 0.8)
            reasons.push('recently updated');
        if (scores.diversityBonus > 0.2)
            reasons.push('adds variety to recommendations');
        return reasons.length > 0 ? reasons.join(', ') : 'good overall match';
    }
};
exports.AdvancedEntityRankingService = AdvancedEntityRankingService;
exports.AdvancedEntityRankingService = AdvancedEntityRankingService = AdvancedEntityRankingService_1 = __decorate([
    (0, common_1.Injectable)()
], AdvancedEntityRankingService);
//# sourceMappingURL=advanced-entity-ranking.service.js.map