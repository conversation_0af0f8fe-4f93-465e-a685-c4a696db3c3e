"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminThrottlerGuard = void 0;
const common_1 = require("@nestjs/common");
const throttler_1 = require("@nestjs/throttler");
const core_1 = require("@nestjs/core");
const prisma_service_1 = require("../../prisma/prisma.service");
let AdminThrottlerGuard = class AdminThrottlerGuard extends throttler_1.ThrottlerGuard {
    constructor(options, storageService, reflector, prismaService) {
        super(options, storageService, reflector);
        this.prismaService = prismaService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const isEntitiesEndpoint = request.route?.path?.includes('/entities') &&
            (request.method === 'POST' || request.method === 'PATCH');
        if (isEntitiesEndpoint && request.user) {
            try {
                const user = await this.prismaService.user.findUnique({
                    where: { id: request.user.id },
                    select: { role: true },
                });
                if (user?.role === 'ADMIN') {
                    return true;
                }
            }
            catch (error) {
                console.error('Error checking user role for throttling:', error);
            }
        }
        return super.canActivate(context);
    }
};
exports.AdminThrottlerGuard = AdminThrottlerGuard;
exports.AdminThrottlerGuard = AdminThrottlerGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Object, Object, core_1.Reflector,
        prisma_service_1.PrismaService])
], AdminThrottlerGuard);
//# sourceMappingURL=admin-throttler.guard.js.map