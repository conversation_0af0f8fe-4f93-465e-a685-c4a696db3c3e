"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const logger_service_1 = require("../logger/logger.service");
let GlobalExceptionFilter = class GlobalExceptionFilter {
    constructor(httpAdapterHost, logger) {
        this.httpAdapterHost = httpAdapterHost;
        this.logger = logger;
    }
    catch(exception, host) {
        this.logger.error('--- GLOBAL EXCEPTION FILTER CAUGHT AN ERROR ---');
        this.logger.error('Exception Type:', exception.constructor.name);
        this.logger.error('Exception Details:', JSON.stringify(exception, null, 2));
        this.logger.error('Exception Stack:', exception.stack);
        const { httpAdapter } = this.httpAdapterHost;
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const correlationId = request.correlationId;
        let statusCode = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'An unexpected internal server error occurred.';
        let errorType = 'InternalServerError';
        let errorDetails = {};
        if (exception instanceof common_1.HttpException) {
            statusCode = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'string') {
                message = exceptionResponse;
            }
            else if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
                message = exceptionResponse.message || exception.message;
                errorDetails = exceptionResponse;
            }
            errorType = exception.constructor.name;
            this.logger.error('HttpException caught:');
            this.logger.error(`CorrelationId: ${correlationId}, StatusCode: ${statusCode}, Message: ${message}, ErrorType: ${errorType}`);
            this.logger.error('ErrorDetails: ' + JSON.stringify(errorDetails));
            this.logger.error('OriginalException: ' + JSON.stringify(exception));
        }
        else {
            this.logger.error('Unexpected exception caught:');
            this.logger.error(`CorrelationId: ${correlationId}, ExceptionType: ${exception.constructor.name}, Message: ${exception.message}`);
            this.logger.error('Stack: ' + exception.stack);
            this.logger.error('OriginalException: ' + JSON.stringify(exception));
        }
        const responseBody = {
            statusCode,
            message,
            error: errorType,
            details: errorDetails,
            timestamp: new Date().toISOString(),
            path: request.url,
        };
        httpAdapter.reply(response, responseBody, statusCode);
    }
};
exports.GlobalExceptionFilter = GlobalExceptionFilter;
exports.GlobalExceptionFilter = GlobalExceptionFilter = __decorate([
    (0, common_1.Catch)(),
    __metadata("design:paramtypes", [core_1.HttpAdapterHost,
        logger_service_1.AppLoggerService])
], GlobalExceptionFilter);
//# sourceMappingURL=global-exception.filter.js.map