import { PrismaService } from '../prisma/prisma.service';
export interface UserStatistics {
    bookmarksCount: number;
    reviewsCount: number;
    toolsSubmitted: number;
    toolsApproved: number;
    requestsMade: number;
    requestsFulfilled: number;
    reputationScore: number;
}
export declare class StatisticsCalculatorService {
    private readonly prismaService;
    constructor(prismaService: PrismaService);
    calculateReputationScore(stats: Omit<UserStatistics, 'reputationScore'>): number;
    recalculateUserStatistics(userId: string): Promise<UserStatistics>;
    updateUserStatistics(userId: string, stats: UserStatistics): Promise<void>;
    recalculateAndUpdateUserStatistics(userId: string): Promise<UserStatistics>;
    recalculateAllUserStatistics(): Promise<void>;
    getUserStatistics(userId: string): Promise<UserStatistics | null>;
    getReputationLeaderboard(limit?: number): Promise<Array<{
        id: string;
        username: string | null;
        displayName: string | null;
        reputationScore: number;
        profilePictureUrl: string | null;
    }>>;
    getUserReputationRank(userId: string): Promise<number | null>;
}
