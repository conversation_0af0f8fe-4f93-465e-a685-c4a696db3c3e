import { PrismaService } from '../prisma/prisma.service';
import { UserUpvote } from 'generated/prisma';
import { AppLoggerService } from '../common/logger/logger.service';
export declare class UpvotesService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService, logger: AppLoggerService);
    addUpvote(userId: string, entityId: string): Promise<UserUpvote>;
    removeUpvote(userId: string, entityId: string): Promise<void>;
}
