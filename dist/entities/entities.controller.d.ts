import { EntitiesService } from './entities.service';
import { CreateEntityDto } from './dto/create-entity.dto';
import { UpdateEntityDto } from './dto/update-entity.dto';
import { User as UserModelPrisma } from '../../generated/prisma';
import { ListEntitiesDto } from './dto/list-entities.dto';
import { EntityResponseDto } from './dto/entity-response.dto';
import { PaginatedEntityResponseDto } from './dto/paginated-entity-response.dto';
import { AppLoggerService } from '../common/logger/logger.service';
export declare class VectorSearchDto {
    query: string;
    limit?: number | undefined;
}
export declare class EntitiesController {
    private readonly entitiesService;
    private readonly logger;
    constructor(entitiesService: EntitiesService, logger: AppLoggerService);
    private mapUserToMinimalDto;
    private mapEntityTypeToDto;
    private mapCategoryToDto;
    private mapTagToDto;
    private mapFeatureToDto;
    private mapToolDetailsToDto;
    private mapCourseDetailsToDto;
    private mapDatasetDetailsToDto;
    private mapResearchPaperDetailsToDto;
    private mapSoftwareDetailsToDto;
    private mapModelDetailsToDto;
    private mapProjectReferenceDetailsToDto;
    private mapServiceProviderDetailsToDto;
    private mapInvestorDetailsToDto;
    private mapCommunityDetailsToDto;
    private mapEventDetailsToDto;
    private mapJobDetailsToDto;
    private mapGrantDetailsToDto;
    private mapBountyDetailsToDto;
    private mapHardwareDetailsToDto;
    private mapNewsDetailsToDto;
    private mapBookDetailsToDto;
    private mapPodcastDetailsToDto;
    private mapNewsletterDetailsToDto;
    private mapPlatformDetailsToDto;
    private mapAgencyDetailsToDto;
    private mapContentCreatorDetailsToDto;
    private mapEntityToListItemResponseDto;
    private mapEntityToResponseDto;
    testValidation(createEntityDto: CreateEntityDto): Promise<any>;
    create(createEntityDto: CreateEntityDto, user: UserModelPrisma): Promise<EntityResponseDto>;
    findAll(listEntitiesDto: ListEntitiesDto): Promise<PaginatedEntityResponseDto>;
    vectorSearch(vectorSearchDto: VectorSearchDto): Promise<import("./entities.service").VectorSearchResult[]>;
    findBySlug(slug: string): Promise<EntityResponseDto>;
    findOne(id: string): Promise<EntityResponseDto>;
    update(id: string, updateEntityDto: UpdateEntityDto, user: UserModelPrisma): Promise<EntityResponseDto>;
    remove(id: string, user: UserModelPrisma): Promise<void>;
}
