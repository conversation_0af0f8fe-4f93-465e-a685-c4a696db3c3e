import { PricingModel, PriceRange } from '@generated-prisma';
export declare class SoftwareDetailsResponseDto {
    entityId: string;
    keyFeatures?: any | null;
    useCases?: any | null;
    integrations?: any | null;
    targetAudience?: any | null;
    deploymentOptions?: any | null;
    supportedOs?: any | null;
    mobileSupport?: boolean | null;
    apiAccess?: boolean | null;
    hasFreeTier?: boolean | null;
    pricingModel?: PricingModel | null;
    priceRange?: PriceRange | null;
    pricingDetails?: string | null;
    pricingUrl?: string | null;
    supportChannels?: any | null;
    supportEmail?: string | null;
    hasLiveChat?: boolean | null;
    communityUrl?: string | null;
    repositoryUrl?: string | null;
    licenseType?: string | null;
    programmingLanguages?: any | null;
    platformCompatibility?: any | null;
    currentVersion?: string | null;
    releaseDate?: Date | null;
    openSource?: boolean | null;
    customizationLevel?: string | null;
    demoAvailable?: boolean | null;
    frameworks?: any | null;
    hasApi?: boolean | null;
    libraries?: any | null;
    trialAvailable?: boolean | null;
    createdAt: Date;
    updatedAt: Date;
}
