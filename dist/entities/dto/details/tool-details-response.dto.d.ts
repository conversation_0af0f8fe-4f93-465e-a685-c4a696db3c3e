import { Learning<PERSON>urve, <PERSON><PERSON>ingModel, <PERSON>Range, TechnicalLevel } from '@generated-prisma';
export declare class ToolDetailsResponseDto {
    entityId: string;
    programmingLanguages?: any | null;
    frameworks?: any | null;
    libraries?: any | null;
    integrations?: any | null;
    keyFeatures?: any | null;
    useCases?: any | null;
    targetAudience?: any | null;
    learningCurve?: LearningCurve | null;
    deploymentOptions?: any | null;
    supportedOs?: any | null;
    mobileSupport?: boolean | null;
    apiAccess?: boolean | null;
    customizationLevel?: string | null;
    trialAvailable?: boolean | null;
    demoAvailable?: boolean | null;
    openSource?: boolean | null;
    supportChannels?: any | null;
    hasFreeTier: boolean;
    pricingModel?: PricingModel | null;
    priceRange?: PriceRange | null;
    pricingDetails?: string | null;
    pricingUrl?: string | null;
    supportEmail?: string | null;
    hasLiveChat?: boolean | null;
    communityUrl?: string | null;
    technicalLevel?: TechnicalLevel | null;
    platforms?: any | null;
    hasApi?: boolean | null;
    apiDocumentationUrl?: string | null;
}
