import { PricingModel, PriceRange } from 'generated/prisma';
export declare class CreateSoftwareDetailsDto {
    repository_url?: string;
    license_type?: string;
    programming_languages?: string[];
    platform_compatibility?: string[];
    current_version?: string;
    release_date?: Date;
    open_source?: boolean;
    has_free_tier?: boolean;
    use_cases?: string[];
    pricing_model?: PricingModel;
    price_range?: PriceRange;
    pricing_details?: string;
    pricing_url?: string;
    integrations?: string[];
    support_email?: string;
    has_live_chat?: boolean;
    community_url?: string;
    api_access?: boolean;
    customization_level?: string;
    demo_available?: boolean;
    deployment_options?: string[];
    frameworks?: string[];
    has_api?: boolean;
    key_features?: string[];
    libraries?: string[];
    mobile_support?: boolean;
    support_channels?: string[];
    supported_os?: string[];
    target_audience?: string[];
    trial_available?: boolean;
}
