"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateToolDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const prisma_1 = require("../../../../generated/prisma/index.js");
class CreateToolDetailsDto {
}
exports.CreateToolDetailsDto = CreateToolDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Learning curve for the tool' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateToolDetailsDto.prototype, "learning_curve", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Key features of the tool as a list of strings',
        type: [String],
        example: ['Real-time collaboration', 'Advanced analytics'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateToolDetailsDto.prototype, "key_features", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the tool has a free tier',
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateToolDetailsDto.prototype, "has_free_tier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of use cases for the tool',
        type: [String],
        example: ['Data Analysis', 'Machine Learning'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateToolDetailsDto.prototype, "use_cases", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Pricing model of the tool',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateToolDetailsDto.prototype, "pricing_model", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Price range of the tool',
        enum: prisma_1.PriceRange,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.PriceRange),
    __metadata("design:type", String)
], CreateToolDetailsDto.prototype, "price_range", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Detailed information about pricing' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateToolDetailsDto.prototype, "pricing_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the pricing page' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateToolDetailsDto.prototype, "pricing_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of integrations with other tools/platforms',
        type: [String],
        example: ['Slack', 'Google Drive'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateToolDetailsDto.prototype, "integrations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Email address for support' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreateToolDetailsDto.prototype, "support_email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if live chat support is available',
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateToolDetailsDto.prototype, "has_live_chat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the community forum or page' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateToolDetailsDto.prototype, "community_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Programming languages used or supported', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateToolDetailsDto.prototype, "programming_languages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Frameworks used or supported', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateToolDetailsDto.prototype, "frameworks", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Libraries used or supported', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateToolDetailsDto.prototype, "libraries", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Target audience for the tool', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateToolDetailsDto.prototype, "target_audience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Deployment options available', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateToolDetailsDto.prototype, "deployment_options", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Supported operating systems', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateToolDetailsDto.prototype, "supported_os", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Indicates if mobile support is available' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateToolDetailsDto.prototype, "mobile_support", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Indicates if API access is provided' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateToolDetailsDto.prototype, "api_access", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Level of customization offered (e.g., Low, Medium, High)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateToolDetailsDto.prototype, "customization_level", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Indicates if a free trial is available' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateToolDetailsDto.prototype, "trial_available", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Indicates if a demo is available' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateToolDetailsDto.prototype, "demo_available", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Indicates if the tool is open source' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateToolDetailsDto.prototype, "open_source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Support channels offered', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateToolDetailsDto.prototype, "support_channels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Technical level required to use the tool',
        enum: prisma_1.TechnicalLevel,
        example: prisma_1.TechnicalLevel.INTERMEDIATE
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.TechnicalLevel),
    __metadata("design:type", String)
], CreateToolDetailsDto.prototype, "technical_level", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Platforms supported by the tool',
        type: [String],
        example: ['Web', 'macOS', 'iOS', 'Android']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateToolDetailsDto.prototype, "platforms", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the tool provides an API',
        default: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateToolDetailsDto.prototype, "has_api", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the official API documentation for the tool',
        example: 'https://awesomeaitool.com/docs/api'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateToolDetailsDto.prototype, "api_documentation_url", void 0);
//# sourceMappingURL=create-tool-details.dto.js.map