import { PricingModel, PriceRange, LearningCurve, TechnicalLevel } from 'generated/prisma';
export declare class CreateToolDetailsDto {
    learning_curve?: LearningCurve;
    key_features?: string[];
    has_free_tier?: boolean;
    use_cases?: string[];
    pricing_model?: PricingModel;
    price_range?: PriceRange;
    pricing_details?: string;
    pricing_url?: string;
    integrations?: string[];
    support_email?: string;
    has_live_chat?: boolean;
    community_url?: string;
    programming_languages?: string[];
    frameworks?: string[];
    libraries?: string[];
    target_audience?: string[];
    deployment_options?: string[];
    supported_os?: string[];
    mobile_support?: boolean;
    api_access?: boolean;
    customization_level?: string;
    trial_available?: boolean;
    demo_available?: boolean;
    open_source?: boolean;
    support_channels?: string[];
    technical_level?: TechnicalLevel;
    platforms?: string[];
    has_api?: boolean;
    api_documentation_url?: string;
}
