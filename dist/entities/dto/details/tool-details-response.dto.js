"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const _generated_prisma_1 = require("../../../../generated/prisma/index.js");
class ToolDetailsResponseDto {
}
exports.ToolDetailsResponseDto = ToolDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'd4e5f6g7-h8i9-0123-4567-890123abcdef',
    }),
    __metadata("design:type", String)
], ToolDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Programming languages the tool is built with or primarily supports.',
        type: [String],
        example: ['Python', 'JavaScript'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "programmingLanguages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Frameworks used by or compatible with the tool.',
        type: [String],
        example: ['React', 'Django'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "frameworks", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Libraries used by or compatible with the tool.',
        type: [String],
        example: ['TensorFlow', 'Pandas'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "libraries", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Known integrations with other tools or services.',
        type: [String],
        example: ['Slack', 'Google Drive'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "integrations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Key features of the tool.',
        type: [String],
        example: ['Real-time collaboration', 'Advanced analytics'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "keyFeatures", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Common use cases for the tool.',
        type: [String],
        example: ['Data visualization', 'Project management'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "useCases", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Target audience for the tool (e.g., developers, designers).',
        type: [String],
        example: ['Developers', 'Data Scientists'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "targetAudience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: _generated_prisma_1.LearningCurve,
        description: 'Perceived learning curve for the tool.',
        example: _generated_prisma_1.LearningCurve.MEDIUM,
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "learningCurve", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Options for deploying the tool.',
        type: [String],
        example: ['Cloud', 'On-premise', 'Docker'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "deploymentOptions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Operating systems supported by the tool.',
        type: [String],
        example: ['Windows', 'macOS', 'Linux', 'Web'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "supportedOs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the tool has mobile support (iOS, Android).',
        example: true,
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "mobileSupport", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the tool provides API access.',
        example: true,
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "apiAccess", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Level of customization available.',
        example: 'High',
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "customizationLevel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if a free trial is available.',
        example: true,
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "trialAvailable", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if a demo is available.',
        example: true,
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "demoAvailable", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the tool is open source.',
        example: false,
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "openSource", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Available support channels.',
        type: [String],
        example: ['Email', 'Chat', 'Phone', 'Community Forum'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "supportChannels", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Indicates if the tool has a free tier.',
        example: true,
        default: false,
    }),
    __metadata("design:type", Boolean)
], ToolDetailsResponseDto.prototype, "hasFreeTier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: _generated_prisma_1.PricingModel,
        description: 'Pricing model of the tool.',
        example: _generated_prisma_1.PricingModel.FREEMIUM,
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "pricingModel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: _generated_prisma_1.PriceRange,
        description: 'General price range of the tool.',
        example: _generated_prisma_1.PriceRange.MEDIUM,
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "priceRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Specific details about pricing.',
        example: 'Pro plan at $49/month.',
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "pricingDetails", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the pricing page.',
        example: 'https://example.com/pricing',
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "pricingUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Contact email for support.',
        example: '<EMAIL>',
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "supportEmail", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if live chat support is available.',
        example: true,
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "hasLiveChat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the community forum or Discord.',
        example: 'https://example.com/community',
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "communityUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: _generated_prisma_1.TechnicalLevel,
        description: 'Technical level required to use the tool.',
        example: _generated_prisma_1.TechnicalLevel.INTERMEDIATE,
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "technicalLevel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Platforms supported by the tool.',
        type: [String],
        example: ['Web', 'macOS', 'iOS', 'Android'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "platforms", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the tool provides an API.',
        example: true,
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "hasApi", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the official API documentation for the tool.',
        example: 'https://awesomeaitool.com/docs/api',
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "apiDocumentationUrl", void 0);
//# sourceMappingURL=tool-details-response.dto.js.map