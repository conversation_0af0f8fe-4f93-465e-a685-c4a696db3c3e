"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SoftwareDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const _generated_prisma_1 = require("../../../../generated/prisma/index.js");
class SoftwareDetailsResponseDto {
}
exports.SoftwareDetailsResponseDto = SoftwareDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 't0u1v2w3-x4y5-z6a7-b8c9-d0e1f2g3h4i5',
    }),
    __metadata("design:type", String)
], SoftwareDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Key features of the software.',
        type: 'array',
        items: { type: 'string' },
        example: ['Data analysis', 'Automation', 'Reporting'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "keyFeatures", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Common use cases for the software.',
        type: 'array',
        items: { type: 'string' },
        example: ['Data analysis', 'Automation'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "useCases", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Known integrations with other tools or services.',
        type: 'array',
        items: { type: 'string' },
        example: ['Slack', 'Google Drive'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "integrations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Target audience for the software.',
        type: 'array',
        items: { type: 'string' },
        example: ['Business Analysts', 'Developers'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "targetAudience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Options for deploying the software.',
        type: 'array',
        items: { type: 'string' },
        example: ['Cloud', 'On-premise', 'Desktop'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "deploymentOptions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Operating systems supported by the software.',
        type: 'array',
        items: { type: 'string' },
        example: ['Windows', 'macOS', 'Linux'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "supportedOs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the software has mobile support.',
        example: true,
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "mobileSupport", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the software provides API access.',
        example: false,
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "apiAccess", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the software has a free tier.',
        example: true,
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "hasFreeTier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: _generated_prisma_1.PricingModel,
        description: 'The pricing model of the software.',
        example: _generated_prisma_1.PricingModel.SUBSCRIPTION,
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "pricingModel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: _generated_prisma_1.PriceRange,
        description: 'The price range of the software.',
        example: _generated_prisma_1.PriceRange.LOW,
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "priceRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Specific details about pricing.',
        example: 'Starts at $10/month.',
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "pricingDetails", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the pricing page of the software.',
        example: 'https://example.com/software/pricing',
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "pricingUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Available support channels.',
        type: 'array',
        items: { type: 'string' },
        example: ['Email', 'Helpdesk', 'Community Forum'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "supportChannels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Contact email for support.',
        example: '<EMAIL>',
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "supportEmail", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if live chat support is available.',
        example: true,
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "hasLiveChat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the community forum or Discord.',
        example: 'https://example.com/software/community',
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "communityUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL of the software repository.',
        example: 'https://github.com/company/awesome-software',
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "repositoryUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Type of license.',
        example: 'MIT',
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "licenseType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Programming languages used.',
        type: 'array',
        items: { type: 'string' },
        example: ['Python', 'JavaScript', 'TypeScript'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "programmingLanguages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Compatible platforms.',
        type: 'array',
        items: { type: 'string' },
        example: ['Windows', 'macOS', 'Linux', 'Web'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "platformCompatibility", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Current version of the software.',
        example: '2.1.0',
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "currentVersion", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Release date of the software.',
        example: '2024-01-15T00:00:00.000Z',
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "releaseDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether the software is open source.',
        example: true,
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "openSource", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Level of customization available.',
        example: 'High',
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "customizationLevel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether a demo is available.',
        example: true,
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "demoAvailable", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Frameworks used or supported.',
        type: 'array',
        items: { type: 'string' },
        example: ['React', 'Django'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "frameworks", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether the software has an API.',
        example: true,
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "hasApi", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Libraries used or supported.',
        type: 'array',
        items: { type: 'string' },
        example: ['TensorFlow', 'Pandas'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "libraries", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether a trial is available.',
        example: true,
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "trialAvailable", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the software details were created',
        example: '2025-04-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], SoftwareDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the software details',
        example: '2025-04-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], SoftwareDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=software-details-response.dto.js.map