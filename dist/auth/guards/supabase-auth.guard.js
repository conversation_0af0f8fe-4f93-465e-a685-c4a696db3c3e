"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../supabase/supabase.service");
const prisma_service_1 = require("../../prisma/prisma.service");
let SupabaseAuthGuard = class SupabaseAuthGuard {
    constructor(supabaseService, prismaService) {
        this.supabaseService = supabaseService;
        this.prismaService = prismaService;
    }
    async canActivate(context) {
        console.log('--- SUPABASE AUTH GUARD ACTIVATED ---');
        const request = context.switchToHttp().getRequest();
        const authHeader = request.headers.authorization;
        console.log('Authorization header:', authHeader ? 'Present' : 'Missing');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            console.error('Missing or invalid authorization header');
            throw new common_1.UnauthorizedException('Missing or invalid authorization header.');
        }
        const jwtToken = authHeader.substring(7);
        console.log('JWT Token extracted from header');
        try {
            const supabase = this.supabaseService.getClient();
            const { data: { user }, error } = await supabase.auth.getUser(jwtToken);
            if (error) {
                console.error('[SupabaseAuthGuard] Supabase JWT validation error:', error);
                throw new common_1.UnauthorizedException('Invalid JWT token: ' + error.message);
            }
            if (!user) {
                console.error('[SupabaseAuthGuard] No user returned from Supabase JWT validation');
                throw new common_1.UnauthorizedException('Invalid JWT token: no user found');
            }
            console.log(`[SupabaseAuthGuard] Supabase JWT validation successful for user: ${user.id}`);
            console.log(`[SupabaseAuthGuard] Attempting to find user in database...`);
            const userProfile = await this.prismaService.user.findUnique({
                where: { authUserId: user.id },
            });
            if (userProfile) {
                console.log(`[SupabaseAuthGuard] User profile found in DB for authUserId: ${user.id}, public.users ID: ${userProfile.id}`);
            }
            else {
                console.log(`[SupabaseAuthGuard] User profile NOT found in DB for authUserId: ${user.id}. This is okay for initial sync.`);
            }
            const userObject = {
                authData: user,
                dbProfile: userProfile,
                profileExistsInDb: !!userProfile,
            };
            request.user = userObject;
            console.log('--- SUPABASE AUTH GUARD SUCCESS ---');
            return true;
        }
        catch (error) {
            console.error('--- SUPABASE AUTH GUARD ERROR ---');
            console.error('Error details:', error);
            console.error('Error stack:', error.stack);
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            throw new common_1.UnauthorizedException('Authentication failed: ' + error.message);
        }
    }
};
exports.SupabaseAuthGuard = SupabaseAuthGuard;
exports.SupabaseAuthGuard = SupabaseAuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        prisma_service_1.PrismaService])
], SupabaseAuthGuard);
//# sourceMappingURL=supabase-auth.guard.js.map