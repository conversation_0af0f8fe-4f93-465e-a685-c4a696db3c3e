import { CanActivate, ExecutionContext } from '@nestjs/common';
import { SupabaseService } from '../../supabase/supabase.service';
import { PrismaService } from '../../prisma/prisma.service';
import { User as PublicUserModel } from '../../../generated/prisma';
export interface ReqUserObject {
    authData: any;
    dbProfile: PublicUserModel | null;
    profileExistsInDb: boolean;
}
export declare class SupabaseAuthGuard implements CanActivate {
    private readonly supabaseService;
    private readonly prismaService;
    constructor(supabaseService: SupabaseService, prismaService: PrismaService);
    canActivate(context: ExecutionContext): Promise<boolean>;
}
