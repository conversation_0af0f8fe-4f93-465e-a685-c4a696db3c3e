"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EnhancedIntentClassificationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedIntentClassificationService = void 0;
const common_1 = require("@nestjs/common");
const filter_extraction_service_1 = require("../../recommendations/services/filter-extraction.service");
let EnhancedIntentClassificationService = EnhancedIntentClassificationService_1 = class EnhancedIntentClassificationService {
    constructor(filterExtractionService) {
        this.filterExtractionService = filterExtractionService;
        this.logger = new common_1.Logger(EnhancedIntentClassificationService_1.name);
    }
    async classifyIntentWithFilters(userMessage, context) {
        try {
            const basicIntent = this.classifyBasicIntent(userMessage, context);
            const extractedFilters = await this.filterExtractionService.extractFiltersFromDescription(userMessage);
            const filterConfidence = this.calculateFilterConfidence(extractedFilters, userMessage);
            const missingCriteria = this.identifyMissingCriteria(extractedFilters, context);
            const clarifyingQuestions = this.generateClarifyingQuestions(extractedFilters, missingCriteria, context);
            const enhancedIntent = {
                ...basicIntent,
                extractedFilters,
                filterConfidence,
                missingCriteria,
                clarifyingQuestions,
            };
            this.logger.debug('Enhanced intent classified:', {
                type: enhancedIntent.type,
                confidence: enhancedIntent.confidence,
                extractedFilterKeys: Object.keys(extractedFilters),
                missingCriteriaKeys: Object.keys(missingCriteria),
                clarifyingQuestionsCount: clarifyingQuestions.length,
            });
            return enhancedIntent;
        }
        catch (error) {
            this.logger.error('Error in enhanced intent classification', error.stack);
            return {
                ...this.classifyBasicIntent(userMessage, context),
                extractedFilters: {},
                filterConfidence: {},
                missingCriteria: {},
                clarifyingQuestions: [],
            };
        }
    }
    updateConversationFilters(context, enhancedIntent, messageIndex) {
        const enhancedContext = context;
        if (!enhancedContext.accumulatedFilters) {
            enhancedContext.accumulatedFilters = {
                filters: {},
                confidence: {},
                history: {},
                source: {},
            };
        }
        if (!enhancedContext.enhancedMetadata) {
            enhancedContext.enhancedMetadata = {
                filtersExtracted: 0,
                clarificationQuestions: 0,
                conversationQuality: 0.5,
                readyForRecommendations: false,
            };
        }
        Object.entries(enhancedIntent.extractedFilters).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                enhancedContext.accumulatedFilters.filters[key] = value;
                const newConfidence = enhancedIntent.filterConfidence[key] || 0.7;
                const existingConfidence = enhancedContext.accumulatedFilters.confidence[key] || 0;
                enhancedContext.accumulatedFilters.confidence[key] = Math.max(newConfidence, existingConfidence);
                enhancedContext.accumulatedFilters.history[key] = messageIndex;
                enhancedContext.accumulatedFilters.source[key] = 'extracted';
            }
        });
        enhancedContext.enhancedMetadata.filtersExtracted = Object.keys(enhancedContext.accumulatedFilters.filters).length;
        enhancedContext.enhancedMetadata.conversationQuality = this.calculateConversationQuality(enhancedContext);
        enhancedContext.enhancedMetadata.readyForRecommendations = this.isReadyForRecommendations(enhancedContext);
        return enhancedContext;
    }
    classifyBasicIntent(userMessage, context) {
        const message = userMessage.toLowerCase();
        if (this.matchesAny(message, [
            'need', 'want', 'looking for', 'find', 'search', 'help me', 'recommend'
        ])) {
            return {
                type: 'discovery',
                confidence: 0.8,
                entities: [],
                categories: [],
                features: [],
                constraints: {},
            };
        }
        if (this.matchesAny(message, [
            'compare', 'vs', 'versus', 'difference', 'better', 'which one'
        ])) {
            return {
                type: 'comparison',
                confidence: 0.8,
                entities: [],
                categories: [],
                features: [],
                constraints: {},
            };
        }
        if (this.matchesAny(message, [
            'also', 'but', 'however', 'actually', 'more specific', 'narrow down'
        ])) {
            return {
                type: 'refinement',
                confidence: 0.7,
                entities: [],
                categories: [],
                features: [],
                constraints: {},
            };
        }
        return {
            type: 'discovery',
            confidence: 0.6,
            entities: [],
            categories: [],
            features: [],
            constraints: {},
        };
    }
    calculateFilterConfidence(extractedFilters, userMessage) {
        const confidence = {};
        const message = userMessage.toLowerCase();
        Object.keys(extractedFilters).forEach(key => {
            let score = 0.7;
            if (key === 'entityTypeIds' && this.matchesAny(message, ['tool', 'course', 'job', 'event'])) {
                score = 0.9;
            }
            if (key === 'technical_levels' && this.matchesAny(message, ['beginner', 'advanced', 'expert'])) {
                score = 0.9;
            }
            if (key === 'has_free_tier' && this.matchesAny(message, ['free', 'no cost'])) {
                score = 0.95;
            }
            if (key === 'has_api' && this.matchesAny(message, ['api', 'integration'])) {
                score = 0.9;
            }
            if (key.includes('search') || key.includes('_text')) {
                score = Math.max(0.5, score - 0.2);
            }
            confidence[key] = score;
        });
        return confidence;
    }
    identifyMissingCriteria(extractedFilters, context) {
        const missing = {
            entityTypes: !extractedFilters.entityTypeIds || extractedFilters.entityTypeIds.length === 0,
            technicalLevel: !extractedFilters.technical_levels && !extractedFilters.skill_levels,
            budget: !extractedFilters.has_free_tier && !extractedFilters.price_ranges && !extractedFilters.salary_min,
            useCase: !extractedFilters.use_cases_search && !extractedFilters.key_features_search,
            platform: !extractedFilters.platforms && !extractedFilters.location_types,
            specificRequirements: Object.keys(extractedFilters).length < 3,
        };
        return missing;
    }
    generateClarifyingQuestions(extractedFilters, missingCriteria, context) {
        const questions = [];
        if (missingCriteria.entityTypes) {
            questions.push({
                question: "What type of resource are you looking for?",
                purpose: 'entity_type',
                expectedFilterKeys: ['entityTypeIds'],
                priority: 10,
                suggestedAnswers: ['AI Tools', 'Courses', 'Jobs', 'Events', 'Hardware'],
            });
        }
        if (missingCriteria.technicalLevel) {
            questions.push({
                question: "What's your technical background or experience level?",
                purpose: 'technical_level',
                expectedFilterKeys: ['technical_levels', 'skill_levels', 'experience_levels'],
                priority: 8,
                suggestedAnswers: ['Beginner', 'Intermediate', 'Advanced', 'Expert'],
            });
        }
        if (missingCriteria.budget) {
            questions.push({
                question: "Do you have any budget constraints or preferences?",
                purpose: 'budget',
                expectedFilterKeys: ['has_free_tier', 'price_ranges', 'salary_min', 'salary_max'],
                priority: 7,
                suggestedAnswers: ['Free only', 'Under $50/month', 'Under $200/month', 'No budget limit'],
            });
        }
        if (missingCriteria.useCase) {
            questions.push({
                question: "What specific use case or problem are you trying to solve?",
                purpose: 'use_case',
                expectedFilterKeys: ['use_cases_search', 'key_features_search', 'job_description'],
                priority: 9,
            });
        }
        if (missingCriteria.platform) {
            questions.push({
                question: "Do you have any platform or location preferences?",
                purpose: 'platform',
                expectedFilterKeys: ['platforms', 'location_types', 'is_online'],
                priority: 6,
                suggestedAnswers: ['Windows', 'macOS', 'Linux', 'Web', 'Mobile', 'Remote', 'On-site'],
            });
        }
        return questions.sort((a, b) => b.priority - a.priority).slice(0, 3);
    }
    calculateConversationQuality(context) {
        const filtersCount = Object.keys(context.accumulatedFilters.filters).length;
        const avgConfidence = Object.values(context.accumulatedFilters.confidence).reduce((a, b) => a + b, 0) /
            Math.max(1, Object.values(context.accumulatedFilters.confidence).length);
        const filterScore = Math.min(1, filtersCount / 5);
        const confidenceScore = avgConfidence;
        return (filterScore * 0.6 + confidenceScore * 0.4);
    }
    isReadyForRecommendations(context) {
        const filtersCount = Object.keys(context.accumulatedFilters.filters).length;
        const hasEntityType = context.accumulatedFilters.filters.entityTypeIds;
        const avgConfidence = Object.values(context.accumulatedFilters.confidence).reduce((a, b) => a + b, 0) /
            Math.max(1, Object.values(context.accumulatedFilters.confidence).length);
        return filtersCount >= 3 && hasEntityType && avgConfidence > 0.6;
    }
    matchesAny(text, patterns) {
        return patterns.some(pattern => text.includes(pattern));
    }
};
exports.EnhancedIntentClassificationService = EnhancedIntentClassificationService;
exports.EnhancedIntentClassificationService = EnhancedIntentClassificationService = EnhancedIntentClassificationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [filter_extraction_service_1.FilterExtractionService])
], EnhancedIntentClassificationService);
//# sourceMappingURL=enhanced-intent-classification.service.js.map