"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EnhancedConversationManagerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedConversationManagerService = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
let EnhancedConversationManagerService = EnhancedConversationManagerService_1 = class EnhancedConversationManagerService {
    constructor(conversationStateService) {
        this.conversationStateService = conversationStateService;
        this.logger = new common_1.Logger(EnhancedConversationManagerService_1.name);
    }
    async initializeEnhancedConversation(userId, sessionId) {
        const finalSessionId = sessionId || this.generateSessionId();
        const context = {
            sessionId: finalSessionId,
            userId,
            messages: [],
            discoveredEntities: [],
            userPreferences: {},
            conversationStage: 'greeting',
            metadata: {
                startedAt: new Date(),
                lastActiveAt: new Date(),
                totalMessages: 0,
                entitiesShown: [],
            },
            conversationMemory: this.initializeConversationMemory(),
            discoveryProgress: this.initializeDiscoveryProgress(),
            questionHistory: this.initializeQuestionHistory(),
        };
        await this.conversationStateService.setConversationContext(finalSessionId, context);
        this.logger.log(`Initialized enhanced conversation session ${finalSessionId} for user ${userId}`);
        return context;
    }
    async getOrCreateEnhancedConversation(userId, sessionId) {
        if (sessionId) {
            const existingContext = await this.conversationStateService.getConversationContext(sessionId);
            if (existingContext) {
                if (existingContext.userId !== userId) {
                    this.logger.warn(`Session ${sessionId} belongs to different user. Creating new session.`);
                    return this.initializeEnhancedConversation(userId);
                }
                const enhancedContext = this.ensureEnhancedStructures(existingContext);
                this.logger.debug(`Retrieved existing enhanced conversation session ${sessionId}`);
                return enhancedContext;
            }
        }
        return this.initializeEnhancedConversation(userId, sessionId);
    }
    async addMessageWithEnhancedTracking(sessionId, message, intent, extractedInfo) {
        const context = await this.conversationStateService.getConversationContext(sessionId);
        if (!context) {
            throw new Error(`Conversation session ${sessionId} not found`);
        }
        const chatMessage = {
            id: (0, uuid_1.v4)(),
            timestamp: new Date(),
            ...message,
        };
        context.messages.push(chatMessage);
        context.metadata.lastActiveAt = new Date();
        context.metadata.totalMessages = context.messages.length;
        if (intent) {
            context.currentIntent = intent;
        }
        if (extractedInfo) {
            this.updateConversationMemory(context, extractedInfo);
        }
        this.updateDiscoveryProgress(context, message, intent);
        this.autoUpdateConversationStage(context, intent);
        await this.conversationStateService.setConversationContext(sessionId, context);
        this.logger.debug(`Added ${message.role} message to enhanced session ${sessionId}. Total messages: ${context.messages.length}`);
        return context;
    }
    trackQuestionAsked(context, question, category) {
        if (!context.questionHistory) {
            context.questionHistory = this.initializeQuestionHistory();
        }
        const questionEntry = {
            question,
            timestamp: new Date(),
            category,
            answered: false,
            effectiveness: 0.5,
        };
        context.questionHistory.askedQuestions.push(questionEntry);
        if (!context.questionHistory.questionCategories[category]) {
            context.questionHistory.questionCategories[category] = {
                count: 0,
                lastAsked: new Date(),
                effectiveness: 0.5,
                shouldAvoid: false,
            };
        }
        const categoryData = context.questionHistory.questionCategories[category];
        categoryData.count++;
        categoryData.lastAsked = new Date();
        this.logger.debug(`Tracked question in category ${category}: ${question.substring(0, 50)}...`);
    }
    shouldAvoidQuestionCategory(context, category, minIntervalMinutes = 5) {
        if (!context.questionHistory?.questionCategories[category]) {
            return false;
        }
        const categoryData = context.questionHistory.questionCategories[category];
        if (categoryData.shouldAvoid) {
            return true;
        }
        const timeSinceLastAsked = Date.now() - categoryData.lastAsked.getTime();
        const minInterval = minIntervalMinutes * 60 * 1000;
        if (timeSinceLastAsked < minInterval) {
            return true;
        }
        if (categoryData.count >= 3 && categoryData.effectiveness < 0.3) {
            return true;
        }
        return false;
    }
    getConversationInsights(context) {
        const progress = context.discoveryProgress || this.initializeDiscoveryProgress();
        const memory = context.conversationMemory || this.initializeConversationMemory();
        const infoGathered = progress.informationGathered;
        const gatheredCount = Object.values(infoGathered).filter(Boolean).length;
        const totalInfo = Object.keys(infoGathered).length;
        const readinessForRecommendations = gatheredCount / totalInfo;
        const missingInformation = Object.entries(infoGathered)
            .filter(([_, gathered]) => !gathered)
            .map(([key, _]) => key);
        const messageCount = context.messages.length;
        const progressScore = progress.confidence;
        const conversationEfficiency = messageCount > 0 ? progressScore / Math.log(messageCount + 1) : 0;
        const suggestedNextSteps = this.generateNextSteps(context, missingInformation);
        return {
            readinessForRecommendations,
            missingInformation,
            conversationEfficiency,
            suggestedNextSteps,
        };
    }
    generateSessionId() {
        return `chat_${(0, uuid_1.v4)()}`;
    }
    initializeConversationMemory() {
        return {
            userProfile: {},
            requirements: {
                mustHave: [],
                niceToHave: [],
                dealBreakers: [],
                specificFeatures: [],
                integrationNeeds: [],
                platformPreferences: [],
            },
            discussedTopics: {
                entityTypes: [],
                categories: [],
                features: [],
                useCases: [],
                competitors: [],
                concerns: [],
            },
            insights: {
                primaryGoal: '',
                urgency: 'medium',
                decisionMakers: [],
                evaluationCriteria: [],
                timeline: '',
            },
        };
    }
    initializeDiscoveryProgress() {
        return {
            phase: 'initial',
            completedSteps: [],
            nextSteps: ['understand_use_case', 'identify_requirements'],
            confidence: 0.1,
            readinessScore: 0.0,
            informationGathered: {
                useCase: false,
                industry: false,
                technicalLevel: false,
                budget: false,
                teamSize: false,
                timeline: false,
                specificRequirements: false,
                integrationNeeds: false,
            },
        };
    }
    initializeQuestionHistory() {
        return {
            askedQuestions: [],
            questionCategories: {},
            avoidedTopics: [],
            preferredTopics: [],
        };
    }
    ensureEnhancedStructures(context) {
        if (!context.conversationMemory) {
            context.conversationMemory = this.initializeConversationMemory();
        }
        if (!context.discoveryProgress) {
            context.discoveryProgress = this.initializeDiscoveryProgress();
        }
        if (!context.questionHistory) {
            context.questionHistory = this.initializeQuestionHistory();
        }
        return context;
    }
    updateConversationMemory(context, extractedInfo) {
        if (!context.conversationMemory) {
            context.conversationMemory = this.initializeConversationMemory();
        }
        const memory = context.conversationMemory;
        if (extractedInfo.userProfile) {
            Object.assign(memory.userProfile, extractedInfo.userProfile);
        }
        if (extractedInfo.requirements) {
            Object.keys(extractedInfo.requirements).forEach(key => {
                const value = extractedInfo.requirements[key];
                if (Array.isArray(value)) {
                    const existingArray = memory.requirements[key];
                    value.forEach(item => {
                        if (!existingArray.includes(item)) {
                            existingArray.push(item);
                        }
                    });
                }
            });
        }
        if (extractedInfo.insights) {
            Object.assign(memory.insights, extractedInfo.insights);
        }
    }
    updateDiscoveryProgress(context, message, intent) {
        if (!context.discoveryProgress) {
            context.discoveryProgress = this.initializeDiscoveryProgress();
        }
        const progress = context.discoveryProgress;
        const messageContent = message.content.toLowerCase();
        if (messageContent.includes('education') || messageContent.includes('teaching') || messageContent.includes('school')) {
            progress.informationGathered.industry = true;
            progress.informationGathered.useCase = true;
        }
        if (messageContent.includes('beginner') || messageContent.includes('advanced') || messageContent.includes('expert')) {
            progress.informationGathered.technicalLevel = true;
        }
        if (messageContent.includes('free') || messageContent.includes('budget') || messageContent.includes('cost') || messageContent.includes('price')) {
            progress.informationGathered.budget = true;
        }
        const gatheredCount = Object.values(progress.informationGathered).filter(Boolean).length;
        const totalInfo = Object.keys(progress.informationGathered).length;
        progress.confidence = gatheredCount / totalInfo;
        progress.readinessScore = Math.min(progress.confidence * 1.2, 1.0);
        if (progress.confidence > 0.7) {
            progress.phase = 'evaluation';
        }
        else if (progress.confidence > 0.4) {
            progress.phase = 'refinement';
        }
        else if (progress.confidence > 0.2) {
            progress.phase = 'exploration';
        }
    }
    autoUpdateConversationStage(context, intent) {
        const progress = context.discoveryProgress;
        if (!progress)
            return;
        if (progress.readinessScore > 0.8) {
            context.conversationStage = 'recommendation';
        }
        else if (progress.confidence > 0.5) {
            context.conversationStage = 'refinement';
        }
        else if (context.messages.length > 1) {
            context.conversationStage = 'discovery';
        }
    }
    generateNextSteps(context, missingInformation) {
        const steps = [];
        if (missingInformation.includes('useCase')) {
            steps.push('understand_primary_use_case');
        }
        if (missingInformation.includes('industry')) {
            steps.push('identify_industry_context');
        }
        if (missingInformation.includes('technicalLevel')) {
            steps.push('assess_technical_requirements');
        }
        if (missingInformation.includes('budget')) {
            steps.push('discuss_budget_constraints');
        }
        return steps;
    }
};
exports.EnhancedConversationManagerService = EnhancedConversationManagerService;
exports.EnhancedConversationManagerService = EnhancedConversationManagerService = EnhancedConversationManagerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Object])
], EnhancedConversationManagerService);
//# sourceMappingURL=enhanced-conversation-manager.service.js.map