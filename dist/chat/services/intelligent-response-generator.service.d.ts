import { EnhancedConversationContext, EnhancedUserIntent, SmartFollowUpQuestion } from '../../common/llm/interfaces/llm.service.interface';
import { ConversationAction, FilterCorrectionResult, ConversationOptimization } from './conversation-flow-manager.service';
export declare class IntelligentResponseGeneratorService {
    private readonly logger;
    generateIntelligentResponse(context: EnhancedConversationContext, currentIntent: EnhancedUserIntent, action: ConversationAction, entities: any[], questions: SmartFollowUpQuestion[], correctionResult?: FilterCorrectionResult, optimization?: ConversationOptimization): IntelligentChatResponse;
    private generateCorrectionResponse;
    private generateConflictResolution;
    private buildBaseResponse;
    private generateEntityDiscoveryMessage;
    private generateQuestionMessage;
    private formatDiscoveredEntities;
    private generateConversationGuidance;
    private generateSuggestedActions;
    private humanizeFilterKey;
    private extractUserName;
    private calculateRelevanceScore;
    private getMatchedFilters;
    private entityMatchesFilter;
}
export interface IntelligentChatResponse {
    message: string;
    conversationStage: string;
    metadata: {
        actionType: string;
        actionReason: string;
        filtersAccumulated: number;
        correctionHandled: boolean;
    };
    discoveredEntities: FormattedEntity[];
    strategicQuestions: SmartFollowUpQuestion[];
    suggestedActions: SuggestedAction[];
    readyForRecommendations: boolean;
    conflictResolution?: ConflictResolutionOption[];
    conversationGuidance?: ConversationGuidance;
}
export interface FormattedEntity {
    id: string;
    name: string;
    description: string;
    type: string;
    rating?: number;
    reviewCount?: number;
    relevanceScore: number;
    matchedFilters: string[];
}
export interface SuggestedAction {
    type: 'view_entities' | 'answer_question' | 'get_recommendations' | 'refine_search';
    label: string;
    description: string;
    priority: number;
}
export interface ConflictResolutionOption {
    filterKey: string;
    question: string;
    options: {
        label: string;
        value: any;
        confidence: number;
    }[];
}
export interface ConversationGuidance {
    efficiency: number;
    suggestions: string[];
    nextSteps: string[];
}
