import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';
export declare class IntelligentQuestionGeneratorService {
    private readonly logger;
    generateContextualQuestions(context: ConversationContext, maxQuestions?: number): string[];
    generateTargetedQuestions(context: ConversationContext, targetCategory: string): string[];
    private analyzeConversationGaps;
    private generateInitialDiscoveryQuestions;
    private generateExplorationQuestions;
    private generateRefinementQuestions;
    private generateEvaluationQuestions;
    private generateDecisionQuestions;
    private generateUseCaseQuestions;
    private generateTechnicalLevelQuestions;
    private generateBudgetQuestions;
    private generateTeamSizeQuestions;
    private generateIntegrationQuestions;
    private generateTimelineQuestions;
    private hasAskedRecently;
    private filterQuestions;
    private calculateSimilarity;
    private identifyMissingCriticalInfo;
    private analyzeUserEngagement;
    private assessConversationMomentum;
    private assessReadinessForRecommendations;
    private getFallbackQuestions;
}
