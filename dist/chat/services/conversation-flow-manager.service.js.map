{"version": 3, "file": "conversation-flow-manager.service.js", "sourceRoot": "", "sources": ["../../../src/chat/services/conversation-flow-manager.service.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAoD;AAc7C,IAAM,8BAA8B,sCAApC,MAAM,8BAA8B;IAApC;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,gCAA8B,CAAC,IAAI,CAAC,CAAC;IA6X5E,CAAC;IAxXC,mBAAmB,CACjB,OAAoC,EACpC,aAAiC;QAEjC,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAErE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;YACxD,UAAU,EAAE,MAAM,CAAC,IAAI;YACvB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC,MAAM;SAC5E,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,0BAA0B,CACxB,OAAoC,EACpC,aAAiC;QAEjC,MAAM,SAAS,GAA4B,EAAE,CAAC;QAC9C,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,EAAE,OAAO,IAAI,EAAE,CAAC;QACrE,MAAM,UAAU,GAAG,OAAO,CAAC,kBAAkB,EAAE,UAAU,IAAI,EAAE,CAAC;QAGhE,IAAI,CAAC,kBAAkB,CAAC,aAAa,IAAI,kBAAkB,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvF,SAAS,CAAC,IAAI,CAAC;gBACb,QAAQ,EAAE,4CAA4C;gBACtD,OAAO,EAAE,aAAa;gBACtB,kBAAkB,EAAE,CAAC,eAAe,CAAC;gBACrC,QAAQ,EAAE,EAAE;gBACZ,gBAAgB,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC;gBACxF,eAAe,EAAE,kEAAkE;aACpF,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC9D,CAAC,kBAAkB,CAAC,gBAAgB,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;YAC7E,SAAS,CAAC,IAAI,CAAC;gBACb,QAAQ,EAAE,oDAAoD;gBAC9D,OAAO,EAAE,iBAAiB;gBAC1B,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,cAAc,CAAC;gBACxD,QAAQ,EAAE,CAAC;gBACX,gBAAgB,EAAE,CAAC,aAAa,EAAE,oBAAoB,EAAE,aAAa,EAAE,WAAW,CAAC;gBACnF,eAAe,EAAE,4DAA4D;aAC9E,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,CAAC,kBAAkB,CAAC,aAAa,IAAI,CAAC,kBAAkB,CAAC,YAAY;YACrE,CAAC,kBAAkB,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;YACrE,SAAS,CAAC,IAAI,CAAC;gBACb,QAAQ,EAAE,qCAAqC;gBAC/C,OAAO,EAAE,QAAQ;gBACjB,kBAAkB,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC;gBACjF,QAAQ,EAAE,CAAC;gBACX,gBAAgB,EAAE,CAAC,cAAc,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,aAAa,CAAC;gBAC9F,eAAe,EAAE,qEAAqE;aACvF,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,IAAI,CAAC,kBAAkB,CAAC,mBAAmB;YAC/E,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;YACrC,SAAS,CAAC,IAAI,CAAC;gBACb,QAAQ,EAAE,gDAAgD;gBAC1D,OAAO,EAAE,UAAU;gBACnB,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,qBAAqB,CAAC;gBAC/D,QAAQ,EAAE,CAAC;gBACX,eAAe,EAAE,4EAA4E;aAC9F,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAChE,CAAC,kBAAkB,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;YACtE,SAAS,CAAC,IAAI,CAAC;gBACb,QAAQ,EAAE,iDAAiD;gBAC3D,OAAO,EAAE,UAAU;gBACnB,kBAAkB,EAAE,CAAC,WAAW,EAAE,wBAAwB,CAAC;gBAC3D,QAAQ,EAAE,CAAC;gBACX,gBAAgB,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC;gBAC/E,eAAe,EAAE,yEAAyE;aAC3F,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACrD,IAAI,CAAC,kBAAkB,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;gBAC3E,SAAS,CAAC,IAAI,CAAC;oBACb,QAAQ,EAAE,0DAA0D;oBACpE,OAAO,EAAE,UAAU;oBACnB,kBAAkB,EAAE,CAAC,gBAAgB,CAAC;oBACtC,QAAQ,EAAE,CAAC;oBACX,gBAAgB,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC;iBAC1E,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;gBAChF,SAAS,CAAC,IAAI,CAAC;oBACb,QAAQ,EAAE,6CAA6C;oBACvD,OAAO,EAAE,iBAAiB;oBAC1B,kBAAkB,EAAE,CAAC,mBAAmB,CAAC;oBACzC,QAAQ,EAAE,CAAC;oBACX,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,WAAW,EAAE,eAAe,CAAC;iBACtF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAGD,IAAI,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;YACxD,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,IAAI,CAAC,UAAU,CAAC,aAAa,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;gBACvF,SAAS,CAAC,IAAI,CAAC;oBACb,QAAQ,EAAE,4CAA4C;oBACtD,OAAO,EAAE,cAAc;oBACvB,kBAAkB,EAAE,CAAC,uBAAuB,CAAC;oBAC7C,QAAQ,EAAE,CAAC;oBACX,gBAAgB,EAAE,CAAC,2BAA2B,EAAE,yBAAyB,CAAC;iBAC3E,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,kBAAkB,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;gBAC1E,SAAS,CAAC,IAAI,CAAC;oBACb,QAAQ,EAAE,6CAA6C;oBACvD,OAAO,EAAE,cAAc;oBACvB,kBAAkB,EAAE,CAAC,eAAe,CAAC;oBACrC,QAAQ,EAAE,CAAC;oBACX,gBAAgB,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE,kBAAkB,CAAC;iBACvF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAGD,IAAI,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,kBAAkB,CAAC,SAAS,IAAI,CAAC,kBAAkB,CAAC,QAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;gBACtG,SAAS,CAAC,IAAI,CAAC;oBACb,QAAQ,EAAE,2CAA2C;oBACrD,OAAO,EAAE,UAAU;oBACnB,kBAAkB,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC;oBAC7C,QAAQ,EAAE,CAAC;oBACX,gBAAgB,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,WAAW,CAAC;iBAC7D,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,kBAAkB,CAAC,WAAW,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;gBAC1E,SAAS,CAAC,IAAI,CAAC;oBACb,QAAQ,EAAE,wCAAwC;oBAClD,OAAO,EAAE,YAAY;oBACrB,kBAAkB,EAAE,CAAC,aAAa,CAAC;oBACnC,QAAQ,EAAE,CAAC;oBACX,gBAAgB,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,CAAC;iBAC/E,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAGD,OAAO,SAAS;aACb,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;aACvC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC;IAKD,sBAAsB,CACpB,OAAoC,EACpC,gBAAoC,EACpC,WAAmB;QAEnB,MAAM,WAAW,GAAuB,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAqB,EAAE,CAAC;QAGvC,MAAM,oBAAoB,GAAG;YAC3B,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS;SAC/E,CAAC;QAEF,MAAM,YAAY,GAAG,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CACzD,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAC9C,CAAC;QAEF,IAAI,YAAY,EAAE,CAAC;YAEjB,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,EAAE;gBAC5E,MAAM,QAAQ,GAAG,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;gBAE1D,IAAI,QAAQ,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBACtC,WAAW,CAAC,IAAI,CAAC;wBACf,SAAS,EAAE,GAAG;wBACd,QAAQ;wBACR,QAAQ;wBACR,UAAU,EAAE,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,GAAG;wBACzD,MAAM,EAAE,iBAAiB;qBAC1B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,gBAAgB,EAAE,WAAW,CAAC,MAAM;gBACpC,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,EAAE;YAC5E,MAAM,aAAa,GAAG,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;YAC/D,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC5E,MAAM,aAAa,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;YAEpE,IAAI,aAAa,IAAI,aAAa,KAAK,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;gBACjE,SAAS,CAAC,IAAI,CAAC;oBACb,SAAS,EAAE,GAAG;oBACd,aAAa;oBACb,QAAQ;oBACR,kBAAkB;oBAClB,aAAa;oBACb,kBAAkB,EAAE,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,EAAE,aAAa,CAAC;iBACxF,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,YAAY;YACZ,WAAW;YACX,SAAS;YACT,iBAAiB,EAAE,IAAI,CAAC,0BAA0B,CAAC,WAAW,EAAE,SAAS,CAAC;SAC3E,CAAC;IACJ,CAAC;IAKD,wBAAwB,CACtB,OAAoC,EACpC,aAAiC;QAEjC,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC;QACnD,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QACnF,MAAM,aAAa,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAG/D,MAAM,UAAU,GAAG,IAAI,CAAC,+BAA+B,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;QAGnG,MAAM,aAAa,GAAa,EAAE,CAAC;QAEnC,IAAI,UAAU,GAAG,GAAG,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACzC,aAAa,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACzC,aAAa,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,aAAa,GAAG,GAAG,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;QAC/E,CAAC;QAED,OAAO;YACL,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,aAAa;YACb,aAAa;YACb,uBAAuB,EAAE,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;SACjE,CAAC;IACJ,CAAC;IAGO,wBAAwB,CAC9B,OAAoC,EACpC,aAAiC;QAEjC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QACnF,MAAM,aAAa,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAC/D,MAAM,aAAa,GAAG,OAAO,CAAC,kBAAkB,EAAE,OAAO,EAAE,aAAa,CAAC;QAEzE,IAAI,YAAY,IAAI,CAAC,IAAI,aAAa,GAAG,GAAG,IAAI,aAAa,EAAE,CAAC;YAC9D,OAAO;gBACL,IAAI,EAAE,yBAAyB;gBAC/B,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE,6DAA6D;aACtE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO;gBACL,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,uDAAuD;aAChE,CAAC;QACJ,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO;gBACL,IAAI,EAAE,kBAAkB;gBACxB,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,gDAAgD;aACzD,CAAC;QACJ,CAAC;QAED,IAAI,aAAa,GAAG,GAAG,EAAE,CAAC;YACxB,OAAO;gBACL,IAAI,EAAE,2BAA2B;gBACjC,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,sDAAsD;aAC/D,CAAC;QACJ,CAAC;QAED,OAAO;YACL,IAAI,EAAE,oBAAoB;YAC1B,QAAQ,EAAE,CAAC;YACX,MAAM,EAAE,2DAA2D;SACpE,CAAC;IACJ,CAAC;IAEO,cAAc,CAAC,OAAY,EAAE,KAAe;QAClD,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,IAAI,EAAE,CAAC;QAChD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IACxD,CAAC;IAEO,0BAA0B,CAAC,OAAoC;QACrE,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,UAAU,IAAI,EAAE,CAAC,CAAC;QACrF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAC5C,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC;IACzF,CAAC;IAEO,+BAA+B,CACrC,YAAoB,EACpB,YAAoB,EACpB,aAAqB;QAErB,MAAM,UAAU,GAAG,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;QAC5D,MAAM,gBAAgB,GAAG,aAAa,CAAC;QACvC,OAAO,CAAC,UAAU,GAAG,GAAG,GAAG,gBAAgB,GAAG,GAAG,CAAC,CAAC;IACrD,CAAC;IAEO,yBAAyB,CAAC,OAAoC;QACpE,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QACnF,MAAM,aAAa,GAAG,OAAO,CAAC,kBAAkB,EAAE,OAAO,EAAE,aAAa,CAAC;QACzE,MAAM,aAAa,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAE/D,OAAO,YAAY,IAAI,CAAC,IAAI,aAAa,IAAI,aAAa,GAAG,GAAG,CAAC;IACnE,CAAC;IAEO,2BAA2B,CACjC,kBAA0B,EAC1B,aAAqB;QAErB,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,GAAG,aAAa,CAAC,CAAC;QAEpE,IAAI,cAAc,GAAG,GAAG,EAAE,CAAC;YACzB,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAED,OAAO,aAAa,GAAG,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC;IAC1E,CAAC;IAEO,0BAA0B,CAChC,WAA+B,EAC/B,SAA2B;QAE3B,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAED,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC7B,CAAC;CACF,CAAA;AA9XY,wEAA8B;yCAA9B,8BAA8B;IAD1C,IAAA,mBAAU,GAAE;GACA,8BAA8B,CA8X1C"}