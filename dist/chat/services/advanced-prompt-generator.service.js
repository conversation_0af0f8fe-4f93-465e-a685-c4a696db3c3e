"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AdvancedPromptGeneratorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedPromptGeneratorService = void 0;
const common_1 = require("@nestjs/common");
let AdvancedPromptGeneratorService = AdvancedPromptGeneratorService_1 = class AdvancedPromptGeneratorService {
    constructor() {
        this.logger = new common_1.Logger(AdvancedPromptGeneratorService_1.name);
    }
    generateAdvancedChatPrompt(userMessage, context, intent, candidateEntities, suggestedQuestions) {
        const memory = context.conversationMemory;
        const progress = context.discoveryProgress;
        const questionHistory = context.questionHistory;
        const systemContext = this.buildSystemContext();
        const conversationAnalysis = this.buildConversationAnalysis(context, memory, progress);
        const userProfileSection = this.buildUserProfileSection(memory);
        const conversationHistorySection = this.buildConversationHistorySection(context);
        const entitiesSection = this.buildEntitiesSection(candidateEntities);
        const guidanceSection = this.buildGuidanceSection(context, intent, progress, questionHistory);
        const responseFormat = this.buildResponseFormat();
        return `${systemContext}

${conversationAnalysis}

${userProfileSection}

${conversationHistorySection}

**Current User Message:**
"${userMessage}"

${entitiesSection}

${guidanceSection}

${responseFormat}`;
    }
    generateAdvancedIntentPrompt(userMessage, context) {
        const recentMessages = context.messages
            .slice(-3)
            .map(msg => `${msg.role}: ${msg.content}`)
            .join('\n');
        const memory = context.conversationMemory;
        const progress = context.discoveryProgress;
        return `You are an expert AI assistant specializing in understanding user intent for AI tool discovery conversations.

**Conversation Context:**
- Phase: ${progress?.phase || 'initial'}
- Confidence: ${progress?.confidence || 0.1}
- Messages: ${context.messages.length}
- Known Industry: ${memory?.userProfile.industry || 'Unknown'}
- Known Use Case: ${memory?.userProfile.primaryUseCases?.join(', ') || 'Unknown'}

**Recent Conversation:**
${recentMessages}

**Current User Message:**
"${userMessage}"

**Intent Classification Guidelines:**
1. Consider the conversation phase and what information we already have
2. Look for signals about user's readiness to move forward
3. Identify specific entities, features, or requirements mentioned
4. Assess if user is providing new information or asking for clarification
5. Determine if they're ready for recommendations or need more discovery

**Intent Types:**
- discovery: User is exploring what AI tools exist for their needs
- comparison: User wants to compare specific tools or categories  
- specific_tool: User is asking about a particular tool
- general_question: User has general questions about AI tools
- refinement: User is narrowing down their requirements
- ready_for_recommendations: User seems ready for specific suggestions

**Response Format (JSON):**
{
  "type": "discovery|comparison|specific_tool|general_question|refinement|ready_for_recommendations",
  "confidence": 0.85,
  "entities": ["mentioned entity names"],
  "categories": ["mentioned categories"],
  "features": ["mentioned features"],
  "constraints": {
    "budget": "free|low|medium|high",
    "technical_level": "beginner|intermediate|advanced",
    "use_case": "specific use case if mentioned",
    "urgency": "low|medium|high"
  },
  "progression_signals": {
    "new_information_provided": true/false,
    "ready_to_advance": true/false,
    "needs_clarification": true/false
  }
}`;
    }
    buildSystemContext() {
        return `You are an expert AI tool discovery assistant with deep knowledge of AI tools, their capabilities, and use cases. Your mission is to help users find the PERFECT AI tool for their specific needs through intelligent conversation.

**Your Expertise:**
- Comprehensive knowledge of AI tools across all categories (productivity, creative, development, business, etc.)
- Understanding of different user types (beginners to experts, individuals to enterprises)
- Ability to match tools to specific use cases and requirements
- Knowledge of pricing, integrations, and technical requirements

**Your Personality:**
- Conversational and approachable, not robotic
- Genuinely helpful and focused on user success
- Asks smart, targeted questions that build on previous answers
- Avoids repetitive or generic responses
- Provides valuable insights and recommendations

**Core Principles:**
1. NEVER ask the same question twice or in different ways
2. Build on what you already know about the user
3. Ask progressively more specific questions as you learn more
4. Provide value in every response, not just questions
5. Guide users toward the best possible tool match`;
    }
    buildConversationAnalysis(context, memory, progress) {
        const messageCount = context.messages.length;
        const phase = progress?.phase || 'initial';
        const confidence = progress?.confidence || 0.1;
        const readinessScore = progress?.readinessScore || 0.0;
        return `**Conversation Analysis:**
- Current Phase: ${phase}
- Messages Exchanged: ${messageCount}
- Understanding Confidence: ${Math.round(confidence * 100)}%
- Readiness for Recommendations: ${Math.round(readinessScore * 100)}%
- Conversation Stage: ${context.conversationStage}
- Last Active: ${context.metadata.lastActiveAt.toISOString()}`;
    }
    buildUserProfileSection(memory) {
        if (!memory) {
            return `**User Profile:** Not yet established - this is early in the conversation.`;
        }
        const profile = memory.userProfile;
        const requirements = memory.requirements;
        const insights = memory.insights;
        return `**What We Know About the User:**
- Industry/Context: ${profile.industry || profile.workContext || 'Not specified'}
- Experience Level: ${profile.experienceLevel || 'Not specified'}
- Team Size: ${profile.teamSize || 'Not specified'}
- Budget Preference: ${profile.budget || 'Not specified'}
- Primary Use Cases: ${profile.primaryUseCases?.join(', ') || 'Not specified'}
- Technical Skills: ${profile.technicalSkills?.join(', ') || 'Not specified'}

**Requirements Identified:**
- Must Have: ${requirements.mustHave.join(', ') || 'None specified'}
- Nice to Have: ${requirements.niceToHave.join(', ') || 'None specified'}
- Deal Breakers: ${requirements.dealBreakers.join(', ') || 'None specified'}
- Integration Needs: ${requirements.integrationNeeds.join(', ') || 'None specified'}

**Insights:**
- Primary Goal: ${insights.primaryGoal || 'Not yet clear'}
- Urgency: ${insights.urgency}
- Timeline: ${insights.timeline || 'Not specified'}`;
    }
    buildConversationHistorySection(context) {
        const recentMessages = context.messages
            .slice(-4)
            .map(msg => `${msg.role}: ${msg.content}`)
            .join('\n');
        return `**Recent Conversation History:**
${recentMessages || 'No previous messages'}`;
    }
    buildEntitiesSection(candidateEntities) {
        if (!candidateEntities || candidateEntities.length === 0) {
            return `**Available Tools:** No specific tools identified yet - focus on understanding needs first.`;
        }
        const entitiesText = candidateEntities
            .slice(0, 5)
            .map((entity, index) => {
            const categories = entity.categories?.map(c => c.category.name).join(', ') || 'General';
            const features = entity.features?.map(f => f.feature.name).slice(0, 3).join(', ') || 'Various features';
            const pricing = entity.has_free_tier ? 'Has free tier' : 'Paid tool';
            return `${index + 1}. **${entity.name}**
   - ${entity.shortDescription || entity.description || 'AI tool'}
   - Categories: ${categories}
   - Key Features: ${features}
   - Pricing: ${pricing}
   - Rating: ${entity.avgRating ? `${entity.avgRating}/5` : 'Not rated'}`;
        })
            .join('\n\n');
        return `**Relevant AI Tools Found:**
${entitiesText}

**Important:** Only mention these tools if they're truly relevant to what the user just said. Don't force recommendations if they're not ready.`;
    }
    buildGuidanceSection(context, intent, progress, questionHistory) {
        const phase = progress?.phase || 'initial';
        const confidence = progress?.confidence || 0.1;
        const messageCount = context.messages.length;
        let guidance = `**Response Guidance for Current Situation:**\n`;
        switch (phase) {
            case 'initial':
                guidance += `- This is early discovery - focus on understanding their primary use case and context
- Ask ONE specific question that builds on what they just said
- Provide some initial value or insight to show your expertise
- Don't overwhelm with multiple questions`;
                break;
            case 'exploration':
                guidance += `- You have basic context - now explore specific requirements
- Ask about technical level, budget, or team needs if not yet known
- Start connecting their needs to potential tool categories
- Share relevant insights about tools in their area of interest`;
                break;
            case 'refinement':
                guidance += `- You understand their needs well - now refine the details
- Ask about specific features, integrations, or constraints
- Start narrowing down to specific tool types or categories
- Provide more targeted insights and comparisons`;
                break;
            case 'evaluation':
                guidance += `- They're ready to evaluate options - provide specific recommendations
- Compare 2-3 tools that match their criteria
- Explain why each tool fits their specific situation
- Ask about evaluation criteria or decision factors`;
                break;
            case 'decision':
                guidance += `- Help them make the final decision
- Provide clear recommendations with reasoning
- Address any remaining concerns or questions
- Guide them toward next steps`;
                break;
        }
        guidance += `\n\n**Based on Intent (${intent.type}):**\n`;
        switch (intent.type) {
            case 'discovery':
                guidance += `- They're exploring - help them understand what's possible
- Ask smart questions to narrow down their needs
- Provide educational value about AI tool categories`;
                break;
            case 'comparison':
                guidance += `- They want to compare options - provide clear comparisons
- Focus on differences that matter for their use case
- Help them understand trade-offs`;
                break;
            case 'specific_tool':
                guidance += `- They're asking about a specific tool - provide detailed info
- Compare it to alternatives if relevant
- Address their specific questions about the tool`;
                break;
            case 'refinement':
                guidance += `- They're narrowing down - help them refine criteria
- Ask about specific requirements or constraints
- Guide them toward more targeted options`;
                break;
        }
        guidance += `\n\n**Conversation Flow Guidelines:**\n`;
        if (messageCount < 3) {
            guidance += `- Early conversation - focus on building rapport and understanding
- Ask ONE good question, don't overwhelm
- Provide some initial value or insight`;
        }
        else if (confidence < 0.3) {
            guidance += `- Still learning about their needs - ask more targeted questions
- Build on what you already know
- Avoid repeating previous questions`;
        }
        else if (confidence < 0.7) {
            guidance += `- Good understanding developing - start providing more specific guidance
- Mention relevant tool categories or types
- Ask about remaining unknowns`;
        }
        else {
            guidance += `- Strong understanding - provide specific recommendations
- Compare specific tools that match their criteria
- Help them evaluate and decide`;
        }
        return guidance;
    }
    buildResponseFormat() {
        return `**Response Format (JSON):**
{
  "message": "Your conversational response here - natural, helpful, and building on the conversation",
  "discoveredEntities": [
    {
      "id": "entity-id",
      "name": "Tool Name", 
      "relevanceScore": 0.9,
      "reason": "Specific reason why this tool matches their needs"
    }
  ],
  "followUpQuestions": ["ONE specific question that builds on their response"],
  "suggestedActions": [
    {
      "type": "ask_question|show_entities|refine_search|get_recommendations",
      "label": "Action label",
      "data": {}
    }
  ],
  "shouldTransitionToRecommendations": false,
  "conversationStage": "greeting|discovery|refinement|recommendation|comparison",
  "insights": {
    "userProfile": {
      "industry": "if mentioned",
      "experienceLevel": "if indicated",
      "budget": "if discussed"
    },
    "requirements": {
      "mustHave": ["any critical requirements mentioned"],
      "niceToHave": ["any preferences mentioned"]
    },
    "nextSteps": ["what should happen next in the conversation"]
  }
}

**Critical Instructions:**
1. Make your message conversational and helpful, not robotic
2. Only ask ONE follow-up question maximum
3. Build on what you already know - don't repeat previous questions
4. Provide value in every response, not just questions
5. Only mention tools if they're truly relevant to what the user just said
6. Focus on understanding their needs before pushing recommendations
7. Be genuinely helpful and insightful, not just a question-asking bot`;
    }
};
exports.AdvancedPromptGeneratorService = AdvancedPromptGeneratorService;
exports.AdvancedPromptGeneratorService = AdvancedPromptGeneratorService = AdvancedPromptGeneratorService_1 = __decorate([
    (0, common_1.Injectable)()
], AdvancedPromptGeneratorService);
//# sourceMappingURL=advanced-prompt-generator.service.js.map