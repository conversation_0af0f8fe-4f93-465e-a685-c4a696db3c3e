import { EnhancedUserIntent, ConversationContext, EnhancedConversationContext } from '../../common/llm/interfaces/llm.service.interface';
import { FilterExtractionService } from '../../recommendations/services/filter-extraction.service';
export declare class EnhancedIntentClassificationService {
    private readonly filterExtractionService;
    private readonly logger;
    constructor(filterExtractionService: FilterExtractionService);
    classifyIntentWithFilters(userMessage: string, context: ConversationContext): Promise<EnhancedUserIntent>;
    updateConversationFilters(context: ConversationContext, enhancedIntent: EnhancedUserIntent, messageIndex: number): EnhancedConversationContext;
    private classifyBasicIntent;
    private calculateFilterConfidence;
    private identifyMissingCriteria;
    private generateClarifyingQuestions;
    private calculateConversationQuality;
    private isReadyForRecommendations;
    private matchesAny;
}
