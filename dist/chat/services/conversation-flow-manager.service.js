"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var ConversationFlowManagerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationFlowManagerService = void 0;
const common_1 = require("@nestjs/common");
let ConversationFlowManagerService = ConversationFlowManagerService_1 = class ConversationFlowManagerService {
    constructor() {
        this.logger = new common_1.Logger(ConversationFlowManagerService_1.name);
    }
    determineNextAction(context, currentIntent) {
        const action = this.analyzeConversationState(context, currentIntent);
        this.logger.debug('Determined next conversation action:', {
            actionType: action.type,
            priority: action.priority,
            sessionId: context.sessionId,
            filtersCount: Object.keys(context.accumulatedFilters?.filters || {}).length,
        });
        return action;
    }
    generateStrategicQuestions(context, currentIntent) {
        const questions = [];
        const accumulatedFilters = context.accumulatedFilters?.filters || {};
        const confidence = context.accumulatedFilters?.confidence || {};
        if (!accumulatedFilters.entityTypeIds || accumulatedFilters.entityTypeIds.length === 0) {
            questions.push({
                question: "What type of resource are you looking for?",
                purpose: 'entity_type',
                expectedFilterKeys: ['entityTypeIds'],
                priority: 10,
                suggestedAnswers: ['🛠️ AI Tools', '📚 Courses', '💼 Jobs', '🎪 Events', '🖥️ Hardware'],
                followUpContext: 'This helps me understand what category of resources to focus on.',
            });
        }
        if (this.hasEntityTypes(accumulatedFilters, ['ai-tool', 'course']) &&
            !accumulatedFilters.technical_levels && !accumulatedFilters.skill_levels) {
            questions.push({
                question: "What's your experience level with this technology?",
                purpose: 'technical_level',
                expectedFilterKeys: ['technical_levels', 'skill_levels'],
                priority: 9,
                suggestedAnswers: ['👶 Beginner', '🔧 Some experience', '🚀 Advanced', '🎯 Expert'],
                followUpContext: 'This helps me recommend tools that match your skill level.',
            });
        }
        if (!accumulatedFilters.has_free_tier && !accumulatedFilters.price_ranges &&
            !accumulatedFilters.salary_min && (confidence.budget || 0) < 0.7) {
            questions.push({
                question: "Do you have any budget preferences?",
                purpose: 'budget',
                expectedFilterKeys: ['has_free_tier', 'price_ranges', 'salary_min', 'salary_max'],
                priority: 8,
                suggestedAnswers: ['🆓 Free only', '💰 Under $50/month', '💳 Under $200/month', '🏢 No limit'],
                followUpContext: 'Budget helps me filter options that fit your financial constraints.',
            });
        }
        if (!accumulatedFilters.use_cases_search && !accumulatedFilters.key_features_search &&
            (confidence.use_case || 0) < 0.8) {
            questions.push({
                question: "What specific problem are you trying to solve?",
                purpose: 'use_case',
                expectedFilterKeys: ['use_cases_search', 'key_features_search'],
                priority: 7,
                followUpContext: 'Understanding your specific needs helps me find the most relevant options.',
            });
        }
        if (this.hasEntityTypes(accumulatedFilters, ['ai-tool', 'software']) &&
            !accumulatedFilters.platforms && (confidence.platform || 0) < 0.6) {
            questions.push({
                question: "What platform or environment will you be using?",
                purpose: 'platform',
                expectedFilterKeys: ['platforms', 'platform_compatibility'],
                priority: 6,
                suggestedAnswers: ['🪟 Windows', '🍎 macOS', '🐧 Linux', '🌐 Web', '📱 Mobile'],
                followUpContext: 'Platform compatibility ensures the tools will work in your environment.',
            });
        }
        if (this.hasEntityTypes(accumulatedFilters, ['job'])) {
            if (!accumulatedFilters.location_types && (confidence.location || 0) < 0.7) {
                questions.push({
                    question: "Do you prefer remote work, on-site, or are you flexible?",
                    purpose: 'platform',
                    expectedFilterKeys: ['location_types'],
                    priority: 8,
                    suggestedAnswers: ['🏠 Remote', '🏢 On-site', '🔄 Hybrid', '🌍 Flexible'],
                });
            }
            if (!accumulatedFilters.experience_levels && (confidence.experience || 0) < 0.7) {
                questions.push({
                    question: "What level of position are you looking for?",
                    purpose: 'technical_level',
                    expectedFilterKeys: ['experience_levels'],
                    priority: 7,
                    suggestedAnswers: ['🌱 Entry level', '👨‍💼 Mid-level', '🎯 Senior', '👑 Leadership'],
                });
            }
        }
        if (this.hasEntityTypes(accumulatedFilters, ['course'])) {
            if (!accumulatedFilters.certificate_available && (confidence.certification || 0) < 0.6) {
                questions.push({
                    question: "Do you need a certificate upon completion?",
                    purpose: 'requirements',
                    expectedFilterKeys: ['certificate_available'],
                    priority: 6,
                    suggestedAnswers: ['✅ Yes, certificate needed', '❌ No certificate needed'],
                });
            }
            if (!accumulatedFilters.duration_text && (confidence.duration || 0) < 0.5) {
                questions.push({
                    question: "How much time can you dedicate to learning?",
                    purpose: 'requirements',
                    expectedFilterKeys: ['duration_text'],
                    priority: 5,
                    suggestedAnswers: ['⚡ Few hours', '📅 Few weeks', '📆 Few months', '🎓 Comprehensive'],
                });
            }
        }
        if (this.hasEntityTypes(accumulatedFilters, ['event'])) {
            if (!accumulatedFilters.is_online && !accumulatedFilters.location && (confidence.location || 0) < 0.7) {
                questions.push({
                    question: "Do you prefer online or in-person events?",
                    purpose: 'platform',
                    expectedFilterKeys: ['is_online', 'location'],
                    priority: 7,
                    suggestedAnswers: ['💻 Online', '🏢 In-person', '🔄 Either'],
                });
            }
            if (!accumulatedFilters.event_types && (confidence.event_type || 0) < 0.6) {
                questions.push({
                    question: "What type of event interests you most?",
                    purpose: 'refinement',
                    expectedFilterKeys: ['event_types'],
                    priority: 6,
                    suggestedAnswers: ['🎤 Conference', '🛠️ Workshop', '🌐 Webinar', '🤝 Meetup'],
                });
            }
        }
        return questions
            .sort((a, b) => b.priority - a.priority)
            .slice(0, 2);
    }
    handleFilterCorrection(context, correctionIntent, userMessage) {
        const corrections = [];
        const conflicts = [];
        const correctionIndicators = [
            'actually', 'no', 'not', 'instead', 'rather', 'correction', 'wrong', 'mistake'
        ];
        const isCorrection = correctionIndicators.some(indicator => userMessage.toLowerCase().includes(indicator));
        if (isCorrection) {
            Object.entries(correctionIntent.extractedFilters).forEach(([key, newValue]) => {
                const oldValue = context.accumulatedFilters?.filters[key];
                if (oldValue && oldValue !== newValue) {
                    corrections.push({
                        filterKey: key,
                        oldValue,
                        newValue,
                        confidence: correctionIntent.filterConfidence[key] || 0.9,
                        reason: 'user_correction',
                    });
                }
            });
            this.logger.debug('Detected filter corrections:', {
                correctionsCount: corrections.length,
                sessionId: context.sessionId,
            });
        }
        Object.entries(correctionIntent.extractedFilters).forEach(([key, newValue]) => {
            const existingValue = context.accumulatedFilters?.filters[key];
            const existingConfidence = context.accumulatedFilters?.confidence[key] || 0;
            const newConfidence = correctionIntent.filterConfidence[key] || 0.7;
            if (existingValue && existingValue !== newValue && !isCorrection) {
                conflicts.push({
                    filterKey: key,
                    existingValue,
                    newValue,
                    existingConfidence,
                    newConfidence,
                    resolutionStrategy: this.determineConflictResolution(existingConfidence, newConfidence),
                });
            }
        });
        return {
            isCorrection,
            corrections,
            conflicts,
            recommendedAction: this.determineRecommendedAction(corrections, conflicts),
        };
    }
    optimizeConversationFlow(context, currentIntent) {
        const messageCount = context.messages?.length || 0;
        const filtersCount = Object.keys(context.accumulatedFilters?.filters || {}).length;
        const avgConfidence = this.calculateAverageConfidence(context);
        const efficiency = this.calculateConversationEfficiency(messageCount, filtersCount, avgConfidence);
        const optimizations = [];
        if (efficiency < 0.5 && messageCount > 3) {
            optimizations.push('Ask more direct questions to gather information faster');
        }
        if (filtersCount < 3 && messageCount > 2) {
            optimizations.push('Focus on gathering essential criteria first');
        }
        if (avgConfidence < 0.6) {
            optimizations.push('Ask clarifying questions to increase filter confidence');
        }
        return {
            efficiency,
            messageCount,
            filtersCount,
            avgConfidence,
            optimizations,
            readyForRecommendations: this.isReadyForRecommendations(context),
        };
    }
    analyzeConversationState(context, currentIntent) {
        const filtersCount = Object.keys(context.accumulatedFilters?.filters || {}).length;
        const avgConfidence = this.calculateAverageConfidence(context);
        const hasEntityType = context.accumulatedFilters?.filters?.entityTypeIds;
        if (filtersCount >= 4 && avgConfidence > 0.7 && hasEntityType) {
            return {
                type: 'provide_recommendations',
                priority: 10,
                reason: 'Sufficient information gathered for quality recommendations',
            };
        }
        if (!hasEntityType) {
            return {
                type: 'ask_entity_type',
                priority: 9,
                reason: 'Entity type is essential for targeted recommendations',
            };
        }
        if (filtersCount < 3) {
            return {
                type: 'gather_more_info',
                priority: 8,
                reason: 'Need more criteria for precise recommendations',
            };
        }
        if (avgConfidence < 0.6) {
            return {
                type: 'clarify_uncertain_filters',
                priority: 7,
                reason: 'Some criteria need clarification for better accuracy',
            };
        }
        return {
            type: 'continue_discovery',
            priority: 6,
            reason: 'Continue gathering information to improve recommendations',
        };
    }
    hasEntityTypes(filters, types) {
        const entityTypes = filters.entityTypeIds || [];
        return types.some(type => entityTypes.includes(type));
    }
    calculateAverageConfidence(context) {
        const confidenceValues = Object.values(context.accumulatedFilters?.confidence || {});
        if (confidenceValues.length === 0)
            return 0;
        return confidenceValues.reduce((sum, conf) => sum + conf, 0) / confidenceValues.length;
    }
    calculateConversationEfficiency(messageCount, filtersCount, avgConfidence) {
        const filterRate = filtersCount / Math.max(1, messageCount);
        const confidenceWeight = avgConfidence;
        return (filterRate * 0.6 + confidenceWeight * 0.4);
    }
    isReadyForRecommendations(context) {
        const filtersCount = Object.keys(context.accumulatedFilters?.filters || {}).length;
        const hasEntityType = context.accumulatedFilters?.filters?.entityTypeIds;
        const avgConfidence = this.calculateAverageConfidence(context);
        return filtersCount >= 3 && hasEntityType && avgConfidence > 0.6;
    }
    determineConflictResolution(existingConfidence, newConfidence) {
        const confidenceDiff = Math.abs(existingConfidence - newConfidence);
        if (confidenceDiff < 0.2) {
            return 'ask_clarification';
        }
        return newConfidence > existingConfidence ? 'use_new' : 'keep_existing';
    }
    determineRecommendedAction(corrections, conflicts) {
        if (corrections.length > 0) {
            return 'apply_corrections';
        }
        if (conflicts.length > 0) {
            return 'resolve_conflicts';
        }
        return 'continue_normally';
    }
};
exports.ConversationFlowManagerService = ConversationFlowManagerService;
exports.ConversationFlowManagerService = ConversationFlowManagerService = ConversationFlowManagerService_1 = __decorate([
    (0, common_1.Injectable)()
], ConversationFlowManagerService);
//# sourceMappingURL=conversation-flow-manager.service.js.map