{"version": 3, "file": "enhanced-conversation-manager.service.js", "sourceRoot": "", "sources": ["../../../src/chat/services/enhanced-conversation-manager.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AAUpD,+BAAoC;AAS7B,IAAM,kCAAkC,0CAAxC,MAAM,kCAAkC;IAG7C,YACmB,wBAAmD;QAAnD,6BAAwB,GAAxB,wBAAwB,CAA2B;QAHrD,WAAM,GAAG,IAAI,eAAM,CAAC,oCAAkC,CAAC,IAAI,CAAC,CAAC;IAI3E,CAAC;IAKJ,KAAK,CAAC,8BAA8B,CAClC,MAAc,EACd,SAAkB;QAElB,MAAM,cAAc,GAAG,SAAS,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE7D,MAAM,OAAO,GAAwB;YACnC,SAAS,EAAE,cAAc;YACzB,MAAM;YACN,QAAQ,EAAE,EAAE;YACZ,kBAAkB,EAAE,EAAE;YACtB,eAAe,EAAE,EAAE;YACnB,iBAAiB,EAAE,UAAU;YAC7B,QAAQ,EAAE;gBACR,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,aAAa,EAAE,CAAC;gBAChB,aAAa,EAAE,EAAE;aAClB;YAED,kBAAkB,EAAE,IAAI,CAAC,4BAA4B,EAAE;YACvD,iBAAiB,EAAE,IAAI,CAAC,2BAA2B,EAAE;YACrD,eAAe,EAAE,IAAI,CAAC,yBAAyB,EAAE;SAClD,CAAC;QAEF,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAEpF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6CAA6C,cAAc,aAAa,MAAM,EAAE,CAAC,CAAC;QAClG,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,+BAA+B,CACnC,MAAc,EACd,SAAkB;QAElB,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAE9F,IAAI,eAAe,EAAE,CAAC;gBACpB,IAAI,eAAe,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;oBACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,SAAS,mDAAmD,CAAC,CAAC;oBAC1F,OAAO,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;gBACrD,CAAC;gBAGD,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,CAAC;gBAEvE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oDAAoD,SAAS,EAAE,CAAC,CAAC;gBACnF,OAAO,eAAe,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,8BAA8B,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IAChE,CAAC;IAKD,KAAK,CAAC,8BAA8B,CAClC,SAAiB,EACjB,OAA8C,EAC9C,MAAmB,EACnB,aAIC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QAEtF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,SAAS,YAAY,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,WAAW,GAAgB;YAC/B,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,GAAG,OAAO;SACX,CAAC;QAGF,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAGnC,OAAO,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3C,OAAO,CAAC,QAAQ,CAAC,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;QAGzD,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,aAAa,GAAG,MAAM,CAAC;QACjC,CAAC;QAGD,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QACxD,CAAC;QAGD,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAGvD,IAAI,CAAC,2BAA2B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAElD,MAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE/E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,OAAO,CAAC,IAAI,gCAAgC,SAAS,qBAAqB,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAChI,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,kBAAkB,CAChB,OAA4B,EAC5B,QAAgB,EAChB,QAAgB;QAEhB,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC7B,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC7D,CAAC;QAED,MAAM,aAAa,GAAG;YACpB,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ;YACR,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,GAAG;SACnB,CAAC;QAEF,OAAO,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAG3D,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1D,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG;gBACrD,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,aAAa,EAAE,GAAG;gBAClB,WAAW,EAAE,KAAK;aACnB,CAAC;QACJ,CAAC;QAED,MAAM,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAC1E,YAAY,CAAC,KAAK,EAAE,CAAC;QACrB,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAEpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,QAAQ,KAAK,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IACjG,CAAC;IAKD,2BAA2B,CACzB,OAA4B,EAC5B,QAAgB,EAChB,qBAA6B,CAAC;QAE9B,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAG1E,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzE,MAAM,WAAW,GAAG,kBAAkB,GAAG,EAAE,GAAG,IAAI,CAAC;QAEnD,IAAI,kBAAkB,GAAG,WAAW,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,YAAY,CAAC,KAAK,IAAI,CAAC,IAAI,YAAY,CAAC,aAAa,GAAG,GAAG,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,uBAAuB,CAAC,OAA4B;QAMlD,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACjF,MAAM,MAAM,GAAG,OAAO,CAAC,kBAAkB,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAGjF,MAAM,YAAY,GAAG,QAAQ,CAAC,mBAAmB,CAAC;QAClD,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QACzE,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC;QACnD,MAAM,2BAA2B,GAAG,aAAa,GAAG,SAAS,CAAC;QAG9D,MAAM,kBAAkB,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;aACpD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC;aACpC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;QAG1B,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC7C,MAAM,aAAa,GAAG,QAAQ,CAAC,UAAU,CAAC;QAC1C,MAAM,sBAAsB,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAGjG,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QAE/E,OAAO;YACL,2BAA2B;YAC3B,kBAAkB;YAClB,sBAAsB;YACtB,kBAAkB;SACnB,CAAC;IACJ,CAAC;IAGO,iBAAiB;QACvB,OAAO,QAAQ,IAAA,SAAM,GAAE,EAAE,CAAC;IAC5B,CAAC;IAEO,4BAA4B;QAClC,OAAO;YACL,WAAW,EAAE,EAAE;YACf,YAAY,EAAE;gBACZ,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,EAAE;gBACd,YAAY,EAAE,EAAE;gBAChB,gBAAgB,EAAE,EAAE;gBACpB,gBAAgB,EAAE,EAAE;gBACpB,mBAAmB,EAAE,EAAE;aACxB;YACD,eAAe,EAAE;gBACf,WAAW,EAAE,EAAE;gBACf,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,EAAE;gBACZ,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,EAAE;aACb;YACD,QAAQ,EAAE;gBACR,WAAW,EAAE,EAAE;gBACf,OAAO,EAAE,QAAQ;gBACjB,cAAc,EAAE,EAAE;gBAClB,kBAAkB,EAAE,EAAE;gBACtB,QAAQ,EAAE,EAAE;aACb;SACF,CAAC;IACJ,CAAC;IAEO,2BAA2B;QACjC,OAAO;YACL,KAAK,EAAE,SAAS;YAChB,cAAc,EAAE,EAAE;YAClB,SAAS,EAAE,CAAC,qBAAqB,EAAE,uBAAuB,CAAC;YAC3D,UAAU,EAAE,GAAG;YACf,cAAc,EAAE,GAAG;YACnB,mBAAmB,EAAE;gBACnB,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,KAAK;gBACf,cAAc,EAAE,KAAK;gBACrB,MAAM,EAAE,KAAK;gBACb,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,KAAK;gBACf,oBAAoB,EAAE,KAAK;gBAC3B,gBAAgB,EAAE,KAAK;aACxB;SACF,CAAC;IACJ,CAAC;IAEO,yBAAyB;QAC/B,OAAO;YACL,cAAc,EAAE,EAAE;YAClB,kBAAkB,EAAE,EAAE;YACtB,aAAa,EAAE,EAAE;YACjB,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAAC,OAA4B;QAC3D,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAChC,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACnE,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC/B,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACjE,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC7B,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC7D,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,wBAAwB,CAC9B,OAA4B,EAC5B,aAIC;QAED,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAChC,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACnE,CAAC;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC;QAG1C,IAAI,aAAa,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC;QAC/D,CAAC;QAGD,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACpD,MAAM,KAAK,GAAG,aAAa,CAAC,YAAa,CAAC,GAA+C,CAAC,CAAC;gBAC3F,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzB,MAAM,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC,GAA+C,CAAa,CAAC;oBACvG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACnB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;4BAClC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC3B,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC3B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,uBAAuB,CAC7B,OAA4B,EAC5B,OAA8C,EAC9C,MAAmB;QAEnB,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC/B,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACjE,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC;QAC3C,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAGrD,IAAI,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrH,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC7C,QAAQ,CAAC,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC;QAC9C,CAAC;QAED,IAAI,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpH,QAAQ,CAAC,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC;QACrD,CAAC;QAED,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAChJ,QAAQ,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC;QAC7C,CAAC;QAGD,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QACzF,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC;QACnE,QAAQ,CAAC,UAAU,GAAG,aAAa,GAAG,SAAS,CAAC;QAGhD,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;QAGnE,IAAI,QAAQ,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;YAC9B,QAAQ,CAAC,KAAK,GAAG,YAAY,CAAC;QAChC,CAAC;aAAM,IAAI,QAAQ,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;YACrC,QAAQ,CAAC,KAAK,GAAG,YAAY,CAAC;QAChC,CAAC;aAAM,IAAI,QAAQ,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;YACrC,QAAQ,CAAC,KAAK,GAAG,aAAa,CAAC;QACjC,CAAC;IACH,CAAC;IAEO,2BAA2B,CAAC,OAA4B,EAAE,MAAmB;QACnF,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC;QAE3C,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,IAAI,QAAQ,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;YAClC,OAAO,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC/C,CAAC;aAAM,IAAI,QAAQ,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;YACrC,OAAO,CAAC,iBAAiB,GAAG,YAAY,CAAC;QAC3C,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,iBAAiB,GAAG,WAAW,CAAC;QAC1C,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,OAA4B,EAAE,kBAA4B;QAClF,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,IAAI,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3C,KAAK,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,KAAK,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,kBAAkB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAClD,KAAK,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC9C,CAAC;QACD,IAAI,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,KAAK,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AA3aY,gFAAkC;6CAAlC,kCAAkC;IAD9C,IAAA,mBAAU,GAAE;;GACA,kCAAkC,CA2a9C"}