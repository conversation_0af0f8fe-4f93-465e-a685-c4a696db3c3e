"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var QuestionSuggestionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionSuggestionService = void 0;
const common_1 = require("@nestjs/common");
let QuestionSuggestionService = QuestionSuggestionService_1 = class QuestionSuggestionService {
    constructor() {
        this.logger = new common_1.Logger(QuestionSuggestionService_1.name);
    }
    generateFollowUpQuestions(context, userMessage, maxQuestions = 2) {
        try {
            if (!context) {
                this.logger.warn('Context is null, returning empty questions array');
                return [];
            }
            const questions = [];
            const messageCount = context.messages?.length || 0;
            const userPrefs = context.userPreferences || {};
            if (messageCount > 0 && context.messages) {
                const conversationAnalysis = this.analyzeConversation(context);
                const userMessageAnalysis = this.analyzeUserMessage(userMessage);
                this.logger.debug(`Enhanced question generation analysis:`, {
                    discussedTopics: Array.from(conversationAnalysis.discussedTopics),
                    askedQuestions: conversationAnalysis.askedQuestions.length,
                    userIntent: userMessageAnalysis.intent,
                    specificity: userMessageAnalysis.specificity,
                    conversationPhase: conversationAnalysis.conversationPhase
                });
                const missingInfo = this.identifyMissingInformation(conversationAnalysis, userMessageAnalysis);
                for (const infoType of missingInfo) {
                    const newQuestions = this.generateQuestionsForInfoType(infoType, userMessageAnalysis, conversationAnalysis);
                    questions.push(...newQuestions);
                    if (questions.length >= maxQuestions)
                        break;
                }
                if (questions.length === 0) {
                    const progressiveQuestions = this.generateProgressiveQuestions(messageCount, conversationAnalysis, userMessageAnalysis);
                    questions.push(...progressiveQuestions);
                }
            }
            else {
                if (messageCount <= 2) {
                    questions.push(...this.getInitialQuestions(userMessage, userPrefs));
                }
                else if (messageCount <= 6) {
                    questions.push(...this.getExplorationQuestions(userMessage, userPrefs));
                }
                else {
                    questions.push(...this.getRefinementQuestions(userMessage, userPrefs));
                }
            }
            if (questions.length === 0) {
                if (messageCount <= 2) {
                    questions.push(...this.getInitialQuestions(userMessage, userPrefs));
                }
                else if (messageCount <= 6) {
                    questions.push(...this.getExplorationQuestions(userMessage, userPrefs));
                }
                else {
                    questions.push(...this.getRefinementQuestions(userMessage, userPrefs));
                }
            }
            return this.filterAndRankQuestions(questions, context, maxQuestions);
        }
        catch (error) {
            this.logger.error('Error generating follow-up questions', error);
            return [];
        }
    }
    getInitialQuestions(userMessage, userPrefs) {
        const questions = [];
        const message = userMessage.toLowerCase();
        if (!userPrefs.industry && !userPrefs.work_context) {
            if (message.includes('work') || message.includes('business') || message.includes('company')) {
                questions.push("What industry do you work in?");
            }
            else {
                questions.push("What kind of work or projects are you looking to enhance with AI tools?");
            }
        }
        if (message.includes('help') || message.includes('need') || message.includes('looking')) {
            questions.push("What specific task or challenge are you trying to solve?");
        }
        if (!userPrefs.technical_level) {
            questions.push("How comfortable are you with learning new AI tools?");
        }
        return questions;
    }
    getExplorationQuestions(userMessage, userPrefs) {
        const questions = [];
        const message = userMessage.toLowerCase();
        if (userPrefs.industry === 'education' || userPrefs.work_context === 'education') {
            questions.push("Are you looking for tools to help with lesson planning, student engagement, or assessment?");
        }
        else if (userPrefs.industry === 'marketing') {
            questions.push("Are you focusing on content creation, analytics, or customer engagement?");
        }
        else if (userPrefs.industry === 'healthcare') {
            questions.push("Are you looking for tools for patient care, research, or administrative tasks?");
        }
        if (!userPrefs.budget) {
            questions.push("Do you have a budget in mind, or are you open to both free and paid options?");
        }
        if (!userPrefs.team_size) {
            questions.push("Will this be for personal use, or do you need something that works for a team?");
        }
        if (message.includes('video') || message.includes('image') || message.includes('content')) {
            questions.push("What type of content are you primarily working with?");
        }
        return questions;
    }
    getRefinementQuestions(userMessage, userPrefs) {
        const questions = [];
        questions.push("Do you need the tool to integrate with any existing software you're already using?");
        questions.push("How soon are you looking to implement this solution?");
        questions.push("Are there any features or limitations that would be deal-breakers for you?");
        questions.push("What's the most important feature you need this tool to have?");
        return questions;
    }
    filterAndRankQuestions(questions, context, maxQuestions) {
        const uniqueQuestions = [...new Set(questions)];
        const assistantMessages = context.messages?.filter(m => m.role === 'assistant') || [];
        const allAssistantContent = assistantMessages.map(m => m.content.toLowerCase()).join(' ');
        const rankedQuestions = uniqueQuestions
            .map(question => {
            const questionLower = question.toLowerCase();
            const similarityPenalty = this.calculateQuestionSimilarity(questionLower, allAssistantContent);
            const recentMessages = context.messages?.slice(-4) || [];
            const recentContent = recentMessages.map(m => m.content.toLowerCase()).join(' ');
            const relevanceScore = this.calculateRelevance(question, recentContent);
            const specificityBonus = this.calculateSpecificityBonus(questionLower);
            const stageBonus = this.calculateStageAppropriatenessBonus(questionLower, context);
            const finalScore = relevanceScore + specificityBonus + stageBonus - similarityPenalty;
            return {
                question,
                score: finalScore,
                debug: {
                    relevance: relevanceScore,
                    specificity: specificityBonus,
                    stage: stageBonus,
                    similarity: similarityPenalty
                }
            };
        })
            .sort((a, b) => b.score - a.score)
            .slice(0, maxQuestions);
        this.logger.debug('Question ranking results:', rankedQuestions.map(q => ({
            question: q.question.substring(0, 50) + '...',
            score: q.score,
            debug: q.debug
        })));
        return rankedQuestions.map(item => item.question);
    }
    calculateRelevance(question, recentContent) {
        let score = 1.0;
        const questionWords = question.toLowerCase().split(' ');
        const keyWords = questionWords.filter(word => word.length > 3 && !['what', 'how', 'are', 'you', 'the', 'for', 'with'].includes(word));
        for (const word of keyWords) {
            if (recentContent.includes(word)) {
                score -= 0.3;
            }
        }
        return Math.max(score, 0.1);
    }
    calculateQuestionSimilarity(question, allAssistantContent) {
        const questionKeywords = question.split(' ').filter(word => word.length > 3);
        let similarityScore = 0;
        questionKeywords.forEach(keyword => {
            if (allAssistantContent.includes(keyword)) {
                similarityScore += 0.3;
            }
        });
        const semanticPatterns = [
            { pattern: /what.*language/, keywords: ['programming', 'code', 'language'] },
            { pattern: /what.*industry/, keywords: ['work', 'industry', 'business'] },
            { pattern: /what.*budget/, keywords: ['budget', 'cost', 'price'] },
            { pattern: /what.*level/, keywords: ['level', 'experience', 'technical'] },
        ];
        semanticPatterns.forEach(({ pattern, keywords }) => {
            if (pattern.test(question) && keywords.some(keyword => allAssistantContent.includes(keyword))) {
                similarityScore += 0.5;
            }
        });
        return Math.min(similarityScore, 2.0);
    }
    calculateSpecificityBonus(question) {
        const specificityIndicators = [
            'specific', 'exactly', 'particular', 'which', 'how many', 'what type',
            'programming language', 'grade level', 'industry', 'budget range'
        ];
        let bonus = 0;
        specificityIndicators.forEach(indicator => {
            if (question.includes(indicator)) {
                bonus += 0.2;
            }
        });
        return Math.min(bonus, 1.0);
    }
    calculateStageAppropriatenessBonus(question, context) {
        const messageCount = context.messages?.length || 0;
        let bonus = 0;
        if (messageCount <= 3) {
            const broadQuestions = ['what', 'industry', 'work', 'field', 'type'];
            if (broadQuestions.some(keyword => question.includes(keyword))) {
                bonus += 0.3;
            }
        }
        else if (messageCount <= 6) {
            const specificQuestions = ['specific', 'exactly', 'particular', 'features'];
            if (specificQuestions.some(keyword => question.includes(keyword))) {
                bonus += 0.3;
            }
        }
        else {
            const actionQuestions = ['ready', 'show', 'recommend', 'compare', 'help'];
            if (actionQuestions.some(keyword => question.includes(keyword))) {
                bonus += 0.3;
            }
        }
        return bonus;
    }
    isQuestionCategoryRecent(context, category) {
        const recentMessages = context.messages?.slice(-6) || [];
        const recentContent = recentMessages.map(m => m.content.toLowerCase()).join(' ');
        const categoryKeywords = {
            industry: ['industry', 'work', 'business', 'company'],
            budget: ['budget', 'cost', 'price', 'free', 'paid'],
            team: ['team', 'personal', 'individual', 'group'],
            technical: ['comfortable', 'experience', 'technical', 'beginner'],
            timeline: ['soon', 'timeline', 'when', 'deadline'],
            integration: ['integrate', 'software', 'existing', 'api'],
        };
        const keywords = categoryKeywords[category] || [];
        return keywords.some((keyword) => recentContent.includes(keyword));
    }
    generateTopicQuestions(topic, context) {
        const topicQuestions = {
            budget: [
                "What's your budget range for AI tools?",
                "Are you looking for free options, or are you open to paid solutions?",
                "Do you need enterprise-level features, or would basic plans work?",
            ],
            industry: [
                "What industry do you work in?",
                "What type of business or organization are you with?",
                "What's your primary work focus?",
            ],
            technical_level: [
                "How comfortable are you with learning new technology?",
                "Do you prefer simple, user-friendly tools or are you okay with more complex solutions?",
                "What's your experience level with AI tools?",
            ],
            team_size: [
                "Is this for personal use or for a team?",
                "How many people would be using this tool?",
                "Do you need collaboration features?",
            ],
            use_case: [
                "What specific task are you trying to accomplish?",
                "What's the main challenge you're facing?",
                "What would success look like for you?",
            ],
        };
        return topicQuestions[topic] || [];
    }
    extractPreviousTopics(messages) {
        const topics = [];
        messages.forEach(message => {
            if (message.content && typeof message.content === 'string') {
                topics.push(message.content.toLowerCase());
            }
        });
        return topics;
    }
    hasDiscussedTopic(previousMessages, keywords) {
        const allText = previousMessages.join(' ').toLowerCase();
        return keywords.some(keyword => allText.includes(keyword));
    }
    analyzeConversation(context) {
        const messages = context.messages || [];
        const discussedTopics = new Set();
        const askedQuestions = [];
        const userNeeds = {};
        messages.forEach(msg => {
            const content = msg.content.toLowerCase();
            const topicKeywords = {
                'industry': ['work', 'job', 'industry', 'business', 'company', 'profession'],
                'technical_level': ['beginner', 'intermediate', 'advanced', 'expert', 'technical'],
                'budget': ['budget', 'cost', 'price', 'expensive', 'cheap', 'free', 'paid'],
                'programming': ['code', 'coding', 'programming', 'development', 'developer'],
                'education': ['education', 'teaching', 'learning', 'student', 'school'],
                'content_creation': ['content', 'writing', 'video', 'image', 'design'],
                'automation': ['automation', 'automate', 'workflow', 'process'],
                'data_analysis': ['data', 'analysis', 'analytics', 'insights'],
                'specific_tools': ['specific', 'particular', 'exactly', 'precisely'],
                'use_case': ['use', 'case', 'scenario', 'situation', 'need'],
            };
            Object.entries(topicKeywords).forEach(([topic, keywords]) => {
                if (keywords.some(keyword => content.includes(keyword))) {
                    discussedTopics.add(topic);
                }
            });
            if (msg.role === 'assistant' && content.includes('?')) {
                const questions = content.match(/[^.!]*\?/g);
                if (questions) {
                    askedQuestions.push(...questions.map(q => q.trim()));
                }
            }
            if (msg.role === 'user') {
                if (content.includes('need') || content.includes('want') || content.includes('looking for')) {
                    userNeeds.hasExpressedNeed = true;
                }
                if (content.includes('budget') || content.includes('cost')) {
                    userNeeds.hasBudgetConcern = true;
                }
                if (content.includes('beginner') || content.includes('advanced')) {
                    userNeeds.hasExpressedLevel = true;
                }
            }
        });
        let conversationPhase = 'initial';
        if (messages.length > 6)
            conversationPhase = 'recommendation';
        else if (messages.length > 4)
            conversationPhase = 'refinement';
        else if (messages.length > 2)
            conversationPhase = 'discovery';
        return {
            discussedTopics,
            askedQuestions,
            userNeeds,
            conversationPhase
        };
    }
    analyzeUserMessage(userMessage) {
        const content = userMessage.toLowerCase();
        const topics = [];
        const topicPatterns = {
            'programming': ['code', 'coding', 'programming', 'development'],
            'education': ['education', 'teaching', 'learning', 'student'],
            'business': ['business', 'work', 'company', 'industry'],
            'content': ['content', 'writing', 'video', 'image'],
            'data': ['data', 'analysis', 'analytics'],
        };
        Object.entries(topicPatterns).forEach(([topic, keywords]) => {
            if (keywords.some(keyword => content.includes(keyword))) {
                topics.push(topic);
            }
        });
        let intent = 'vague';
        if (content.includes('specifically') || content.includes('exactly') || topics.length > 0) {
            intent = 'specific';
        }
        else if (content.includes('what') || content.includes('how') || content.includes('?')) {
            intent = 'clarification';
        }
        else if (topics.length > 0) {
            intent = 'new_topic';
        }
        const specificityIndicators = ['specific', 'exactly', 'particular', 'need', 'want', 'looking for'];
        const specificity = specificityIndicators.filter(indicator => content.includes(indicator)).length / specificityIndicators.length;
        return { intent, topics, specificity };
    }
    identifyMissingInformation(conversationAnalysis, userMessageAnalysis) {
        const missing = [];
        const { discussedTopics, userNeeds } = conversationAnalysis;
        if (!discussedTopics.has('industry') && !userNeeds.hasExpressedNeed) {
            missing.push('industry_context');
        }
        if (!discussedTopics.has('specific_tools') && userMessageAnalysis.specificity < 0.3) {
            missing.push('specific_needs');
        }
        if (!discussedTopics.has('technical_level') && !userNeeds.hasExpressedLevel) {
            missing.push('technical_level');
        }
        if (!discussedTopics.has('budget') && !userNeeds.hasBudgetConcern) {
            missing.push('budget_constraints');
        }
        if (!discussedTopics.has('use_case') && userMessageAnalysis.intent === 'vague') {
            missing.push('use_case');
        }
        return missing;
    }
    generateQuestionsForInfoType(infoType, userMessageAnalysis, conversationAnalysis) {
        const questions = [];
        switch (infoType) {
            case 'industry_context':
                if (userMessageAnalysis.topics.includes('education')) {
                    questions.push("What grade level or subject area do you teach?");
                    questions.push("Are you looking for tools to help with lesson planning, grading, or student engagement?");
                }
                else if (userMessageAnalysis.topics.includes('programming')) {
                    questions.push("What programming language are you primarily working with?");
                    questions.push("Are you looking for tools to help with debugging, testing, or code generation?");
                }
                else if (userMessageAnalysis.topics.includes('business')) {
                    questions.push("What industry does your business operate in?");
                    questions.push("Are you looking to automate specific business processes?");
                }
                else {
                    questions.push("What field or industry are you working in?");
                }
                break;
            case 'specific_needs':
                if (userMessageAnalysis.topics.includes('education')) {
                    questions.push("What specific teaching challenges are you trying to solve?");
                    questions.push("Are you looking for tools for lesson planning, grading, or student engagement?");
                }
                else if (userMessageAnalysis.topics.includes('programming')) {
                    questions.push("What specific coding challenges are you facing?");
                    questions.push("Are you looking for tools for code review, documentation, or testing?");
                }
                else {
                    questions.push("What specific challenge are you trying to solve?");
                    questions.push("What would be the ideal outcome for you?");
                }
                break;
            case 'technical_level':
                questions.push("How would you describe your technical expertise level?");
                break;
            case 'budget_constraints':
                questions.push("Do you have any budget considerations for AI tools?");
                break;
            case 'use_case':
                if (userMessageAnalysis.topics.includes('education')) {
                    questions.push("What specific educational tasks are you looking to enhance?");
                }
                else if (userMessageAnalysis.topics.includes('programming')) {
                    questions.push("What type of development tasks are you working on?");
                }
                else {
                    questions.push("What type of tasks are you looking to accomplish?");
                }
                break;
        }
        return questions;
    }
    generateProgressiveQuestions(messageCount, conversationAnalysis, userMessageAnalysis) {
        const questions = [];
        const { conversationPhase, discussedTopics } = conversationAnalysis;
        switch (conversationPhase) {
            case 'initial':
                questions.push("What brings you here today? What are you hoping to accomplish?");
                break;
            case 'discovery':
                if (!discussedTopics.has('specific_tools')) {
                    questions.push("Are there any specific features that are must-haves for you?");
                }
                break;
            case 'refinement':
                questions.push("Would you like me to show you some specific recommendations based on what we've discussed?");
                questions.push("Are you ready to see some AI tools that match your needs?");
                break;
            case 'recommendation':
                questions.push("Would you like to compare a few options, or do you need help with implementation?");
                questions.push("Should I show you some specific tools that match your requirements?");
                break;
        }
        if (messageCount > 6) {
            questions.push("Are you ready to see some recommendations?");
            questions.push("Would you like me to show you some specific options?");
            questions.push("Should we compare a few tools that might work for you?");
        }
        return questions;
    }
};
exports.QuestionSuggestionService = QuestionSuggestionService;
exports.QuestionSuggestionService = QuestionSuggestionService = QuestionSuggestionService_1 = __decorate([
    (0, common_1.Injectable)()
], QuestionSuggestionService);
//# sourceMappingURL=question-suggestion.service.js.map