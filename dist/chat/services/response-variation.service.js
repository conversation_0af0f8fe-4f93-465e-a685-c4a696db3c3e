"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var ResponseVariationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseVariationService = void 0;
const common_1 = require("@nestjs/common");
let ResponseVariationService = ResponseVariationService_1 = class ResponseVariationService {
    constructor() {
        this.logger = new common_1.Logger(ResponseVariationService_1.name);
    }
    addVariationToResponse(response, userMessage, context) {
        try {
            const isRepeatedQuestion = this.isRepeatedQuestion(userMessage, context);
            if (isRepeatedQuestion) {
                this.logger.log(`Detected repeated question: "${userMessage.substring(0, 50)}..."`);
                return this.varyResponseForRepeatedQuestion(response, userMessage, context);
            }
            const isSimilarResponse = this.isSimilarToPreviousResponses(response.message, context);
            if (isSimilarResponse) {
                this.logger.log('Detected similar response, adding variation');
                return this.addResponseVariation(response, context);
            }
            return response;
        }
        catch (error) {
            this.logger.error('Error adding response variation', error);
            return response;
        }
    }
    isRepeatedQuestion(userMessage, context) {
        const userMessages = context.messages?.filter(m => m.role === 'user') || [];
        const currentMessageLower = userMessage.toLowerCase().trim();
        return userMessages.some(msg => {
            const previousMessageLower = msg.content.toLowerCase().trim();
            return previousMessageLower === currentMessageLower && msg.content !== userMessage;
        });
    }
    isSimilarToPreviousResponses(responseMessage, context) {
        const assistantMessages = context.messages?.filter(m => m.role === 'assistant') || [];
        const currentResponseLower = responseMessage.toLowerCase();
        return assistantMessages.some(msg => {
            const similarity = this.calculateTextSimilarity(currentResponseLower, msg.content.toLowerCase());
            return similarity > 0.7;
        });
    }
    calculateTextSimilarity(text1, text2) {
        const words1 = text1.split(/\s+/).filter(w => w.length > 3);
        const words2 = text2.split(/\s+/).filter(w => w.length > 3);
        if (words1.length === 0 || words2.length === 0)
            return 0;
        const commonWords = words1.filter(word => words2.includes(word));
        return commonWords.length / Math.max(words1.length, words2.length);
    }
    varyResponseForRepeatedQuestion(response, userMessage, context) {
        this.logger.log('Repeated question detected but not adding variation prefix to avoid unnatural responses');
        return {
            ...response,
            followUpQuestions: this.generateAlternativeQuestions(response.followUpQuestions || [], context),
        };
    }
    addResponseVariation(response, context) {
        this.logger.log('Similar response detected but not adding variation phrase to avoid unnatural responses');
        return {
            ...response,
            followUpQuestions: this.generateAlternativeQuestions(response.followUpQuestions || [], context),
        };
    }
    generateAlternativeQuestions(originalQuestions, context) {
        const messageCount = context.messages?.length || 0;
        const alternativeQuestions = {
            early: [
                "What specific outcome are you hoping to achieve?",
                "What's driving your interest in AI tools right now?",
                "What challenges are you currently facing that AI might help with?",
            ],
            mid: [
                "What features would make the biggest difference for you?",
                "Are there any deal-breakers or must-have requirements?",
                "How do you envision integrating this into your workflow?",
            ],
            late: [
                "Would you like to see some specific recommendations?",
                "Should we compare a few options that might work for you?",
                "Are you ready to explore some concrete solutions?",
            ]
        };
        let questionSet;
        if (messageCount <= 3) {
            questionSet = alternativeQuestions.early;
        }
        else if (messageCount <= 6) {
            questionSet = alternativeQuestions.mid;
        }
        else {
            questionSet = alternativeQuestions.late;
        }
        const shuffled = [...questionSet].sort(() => Math.random() - 0.5);
        return shuffled.slice(0, 2);
    }
    addContextualVariation(response, context) {
        const userMessages = context.messages?.filter(m => m.role === 'user') || [];
        const isVeryBrief = userMessages.some(m => m.content.length < 20);
        const isDetailed = userMessages.some(m => m.content.length > 100);
        const mentionsSpecifics = userMessages.some(m => m.content.includes('specific') || m.content.includes('exactly') || m.content.includes('particular'));
        let styleAdjustment = '';
        if (isVeryBrief) {
            styleAdjustment = ' I\'ll keep this concise since you prefer brief responses.';
        }
        else if (isDetailed) {
            styleAdjustment = ' Let me provide some detailed insights since you appreciate thorough information.';
        }
        else if (mentionsSpecifics) {
            styleAdjustment = ' I\'ll focus on specific, actionable recommendations.';
        }
        if (styleAdjustment) {
            return {
                ...response,
                message: response.message + styleAdjustment,
            };
        }
        return response;
    }
    ensureResponseUniqueness(response, context) {
        const assistantMessages = context.messages?.filter(m => m.role === 'assistant') || [];
        const similarResponseCount = assistantMessages.filter(msg => {
            const similarity = this.calculateTextSimilarity(response.message.toLowerCase(), msg.content.toLowerCase());
            return similarity > 0.5;
        }).length;
        if (similarResponseCount > 1) {
            this.logger.warn(`Detected ${similarResponseCount} similar responses, forcing variation`);
            const forceVariationPrefixes = [
                "Let me try a completely different approach:",
                "Here's a fresh perspective on your question:",
                "Taking a step back, let me reframe this:",
                "Let me tackle this from a new angle:",
            ];
            const randomPrefix = forceVariationPrefixes[Math.floor(Math.random() * forceVariationPrefixes.length)];
            return {
                ...response,
                message: `${randomPrefix} ${response.message}`,
                followUpQuestions: this.generateAlternativeQuestions(response.followUpQuestions || [], context),
            };
        }
        return response;
    }
};
exports.ResponseVariationService = ResponseVariationService;
exports.ResponseVariationService = ResponseVariationService = ResponseVariationService_1 = __decorate([
    (0, common_1.Injectable)()
], ResponseVariationService);
//# sourceMappingURL=response-variation.service.js.map