import { EnhancedConversationContext, EnhancedUserIntent, SmartFollowUpQuestion } from '../../common/llm/interfaces/llm.service.interface';
export declare class ConversationFlowManagerService {
    private readonly logger;
    determineNextAction(context: EnhancedConversationContext, currentIntent: EnhancedUserIntent): ConversationAction;
    generateStrategicQuestions(context: EnhancedConversationContext, currentIntent: EnhancedUserIntent): SmartFollowUpQuestion[];
    handleFilterCorrection(context: EnhancedConversationContext, correctionIntent: EnhancedUserIntent, userMessage: string): FilterCorrectionResult;
    optimizeConversationFlow(context: EnhancedConversationContext, currentIntent: EnhancedUserIntent): ConversationOptimization;
    private analyzeConversationState;
    private hasEntityTypes;
    private calculateAverageConfidence;
    private calculateConversationEfficiency;
    private isReadyForRecommendations;
    private determineConflictResolution;
    private determineRecommendedAction;
}
export interface ConversationAction {
    type: 'provide_recommendations' | 'ask_entity_type' | 'gather_more_info' | 'clarify_uncertain_filters' | 'continue_discovery';
    priority: number;
    reason: string;
}
export interface FilterCorrection {
    filterKey: string;
    oldValue: any;
    newValue: any;
    confidence: number;
    reason: string;
}
export interface FilterConflict {
    filterKey: string;
    existingValue: any;
    newValue: any;
    existingConfidence: number;
    newConfidence: number;
    resolutionStrategy: 'keep_existing' | 'use_new' | 'ask_clarification';
}
export interface FilterCorrectionResult {
    isCorrection: boolean;
    corrections: FilterCorrection[];
    conflicts: FilterConflict[];
    recommendedAction: 'apply_corrections' | 'resolve_conflicts' | 'continue_normally';
}
export interface ConversationOptimization {
    efficiency: number;
    messageCount: number;
    filtersCount: number;
    avgConfidence: number;
    optimizations: string[];
    readyForRecommendations: boolean;
}
