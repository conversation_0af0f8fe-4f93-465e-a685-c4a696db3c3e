import { ConversationContext, ChatMessage, UserIntent, ConversationMemory } from '../../common/llm/interfaces/llm.service.interface';
import { IConversationStateService } from '../interfaces/conversation-state.interface';
export declare class EnhancedConversationManagerService {
    private readonly conversationStateService;
    private readonly logger;
    constructor(conversationStateService: IConversationStateService);
    initializeEnhancedConversation(userId: string, sessionId?: string): Promise<ConversationContext>;
    getOrCreateEnhancedConversation(userId: string, sessionId?: string): Promise<ConversationContext>;
    addMessageWithEnhancedTracking(sessionId: string, message: Omit<ChatMessage, 'id' | 'timestamp'>, intent?: UserIntent, extractedInfo?: {
        userProfile?: Partial<ConversationMemory['userProfile']>;
        requirements?: Partial<ConversationMemory['requirements']>;
        insights?: Partial<ConversationMemory['insights']>;
    }): Promise<ConversationContext>;
    trackQuestionAsked(context: ConversationContext, question: string, category: string): void;
    shouldAvoidQuestionCategory(context: ConversationContext, category: string, minIntervalMinutes?: number): boolean;
    getConversationInsights(context: ConversationContext): {
        readinessForRecommendations: number;
        missingInformation: string[];
        conversationEfficiency: number;
        suggestedNextSteps: string[];
    };
    private generateSessionId;
    private initializeConversationMemory;
    private initializeDiscoveryProgress;
    private initializeQuestionHistory;
    private ensureEnhancedStructures;
    private updateConversationMemory;
    private updateDiscoveryProgress;
    private autoUpdateConversationStage;
    private generateNextSteps;
}
