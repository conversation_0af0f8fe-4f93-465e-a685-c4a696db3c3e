import { ConversationContext, ChatResponse } from '../../common/llm/interfaces/llm.service.interface';
export declare class ResponseVariationService {
    private readonly logger;
    addVariationToResponse(response: ChatResponse, userMessage: string, context: ConversationContext): ChatResponse;
    private isRepeatedQuestion;
    private isSimilarToPreviousResponses;
    private calculateTextSimilarity;
    private varyResponseForRepeatedQuestion;
    private addResponseVariation;
    private generateAlternativeQuestions;
    addContextualVariation(response: ChatResponse, context: ConversationContext): ChatResponse;
    ensureResponseUniqueness(response: ChatResponse, context: ConversationContext): ChatResponse;
}
