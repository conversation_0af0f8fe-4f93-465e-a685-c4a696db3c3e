import { PrismaService } from '../../prisma/prisma.service';
import { IConversationStateService } from '../interfaces/conversation-state.interface';
import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';
export declare class DatabaseConversationStateService implements IConversationStateService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    setConversationContext(sessionId: string, context: ConversationContext, ttlSeconds?: number): Promise<void>;
    getConversationContext(sessionId: string): Promise<ConversationContext | null>;
    deleteConversationContext(sessionId: string): Promise<void>;
    getUserActiveSessions(userId: string): Promise<string[]>;
    hasConversationContext(sessionId: string): Promise<boolean>;
    cleanupExpiredConversations(): Promise<number>;
    getStats(): Promise<{
        totalSessions: number;
        activeSessions: number;
        memoryUsage?: number;
    }>;
}
