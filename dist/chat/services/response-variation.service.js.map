{"version": 3, "file": "response-variation.service.js", "sourceRoot": "", "sources": ["../../../src/chat/services/response-variation.service.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAoD;AAO7C,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAA9B;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;IAqNtE,CAAC;IAhNC,sBAAsB,CACpB,QAAsB,EACtB,WAAmB,EACnB,OAA4B;QAE5B,IAAI,CAAC;YAEH,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAEzE,IAAI,kBAAkB,EAAE,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;gBACpF,OAAO,IAAI,CAAC,+BAA+B,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YAC9E,CAAC;YAGD,MAAM,iBAAiB,GAAG,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEvF,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;gBAC/D,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACtD,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,QAAQ,CAAC;QAClB,CAAC;IACH,CAAC;IAKO,kBAAkB,CAAC,WAAmB,EAAE,OAA4B;QAC1E,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC;QAC5E,MAAM,mBAAmB,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAE7D,OAAO,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAC7B,MAAM,oBAAoB,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;YAC9D,OAAO,oBAAoB,KAAK,mBAAmB,IAAI,GAAG,CAAC,OAAO,KAAK,WAAW,CAAC;QACrF,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,4BAA4B,CAAC,eAAuB,EAAE,OAA4B;QACxF,MAAM,iBAAiB,GAAG,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC;QACtF,MAAM,oBAAoB,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;QAG3D,OAAO,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAClC,MAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;YACjG,OAAO,UAAU,GAAG,GAAG,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,uBAAuB,CAAC,KAAa,EAAE,KAAa;QAC1D,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5D,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE5D,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEzD,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QACjE,OAAO,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACrE,CAAC;IAKO,+BAA+B,CACrC,QAAsB,EACtB,WAAmB,EACnB,OAA4B;QAI5B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yFAAyF,CAAC,CAAC;QAE3G,OAAO;YACL,GAAG,QAAQ;YACX,iBAAiB,EAAE,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,iBAAiB,IAAI,EAAE,EAAE,OAAO,CAAC;SAChG,CAAC;IACJ,CAAC;IAKO,oBAAoB,CAAC,QAAsB,EAAE,OAA4B;QAE/E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wFAAwF,CAAC,CAAC;QAE1G,OAAO;YACL,GAAG,QAAQ;YACX,iBAAiB,EAAE,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,iBAAiB,IAAI,EAAE,EAAE,OAAO,CAAC;SAChG,CAAC;IACJ,CAAC;IAKO,4BAA4B,CAAC,iBAA2B,EAAE,OAA4B;QAC5F,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC;QAGnD,MAAM,oBAAoB,GAAG;YAC3B,KAAK,EAAE;gBACL,kDAAkD;gBAClD,qDAAqD;gBACrD,mEAAmE;aACpE;YACD,GAAG,EAAE;gBACH,0DAA0D;gBAC1D,wDAAwD;gBACxD,0DAA0D;aAC3D;YACD,IAAI,EAAE;gBACJ,sDAAsD;gBACtD,0DAA0D;gBAC1D,mDAAmD;aACpD;SACF,CAAC;QAEF,IAAI,WAAqB,CAAC;QAC1B,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;YACtB,WAAW,GAAG,oBAAoB,CAAC,KAAK,CAAC;QAC3C,CAAC;aAAM,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;YAC7B,WAAW,GAAG,oBAAoB,CAAC,GAAG,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,WAAW,GAAG,oBAAoB,CAAC,IAAI,CAAC;QAC1C,CAAC;QAGD,MAAM,QAAQ,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QAClE,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9B,CAAC;IAKD,sBAAsB,CAAC,QAAsB,EAAE,OAA4B;QACzE,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC;QAG5E,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QAClE,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;QAClE,MAAM,iBAAiB,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAC9C,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CACpG,CAAC;QAEF,IAAI,eAAe,GAAG,EAAE,CAAC;QAEzB,IAAI,WAAW,EAAE,CAAC;YAChB,eAAe,GAAG,4DAA4D,CAAC;QACjF,CAAC;aAAM,IAAI,UAAU,EAAE,CAAC;YACtB,eAAe,GAAG,mFAAmF,CAAC;QACxG,CAAC;aAAM,IAAI,iBAAiB,EAAE,CAAC;YAC7B,eAAe,GAAG,uDAAuD,CAAC;QAC5E,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,GAAG,QAAQ;gBACX,OAAO,EAAE,QAAQ,CAAC,OAAO,GAAG,eAAe;aAC5C,CAAC;QACJ,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,wBAAwB,CAAC,QAAsB,EAAE,OAA4B;QAC3E,MAAM,iBAAiB,GAAG,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC;QAGtF,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAC7C,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,EAC9B,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAC1B,CAAC;YACF,OAAO,UAAU,GAAG,GAAG,CAAC;QAC1B,CAAC,CAAC,CAAC,MAAM,CAAC;QAEV,IAAI,oBAAoB,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,oBAAoB,uCAAuC,CAAC,CAAC;YAE1F,MAAM,sBAAsB,GAAG;gBAC7B,6CAA6C;gBAC7C,8CAA8C;gBAC9C,0CAA0C;gBAC1C,sCAAsC;aACvC,CAAC;YAEF,MAAM,YAAY,GAAG,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,sBAAsB,CAAC,MAAM,CAAC,CAAC,CAAC;YAEvG,OAAO;gBACL,GAAG,QAAQ;gBACX,OAAO,EAAE,GAAG,YAAY,IAAI,QAAQ,CAAC,OAAO,EAAE;gBAC9C,iBAAiB,EAAE,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,iBAAiB,IAAI,EAAE,EAAE,OAAO,CAAC;aAChG,CAAC;QACJ,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AAtNY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;GACA,wBAAwB,CAsNpC"}