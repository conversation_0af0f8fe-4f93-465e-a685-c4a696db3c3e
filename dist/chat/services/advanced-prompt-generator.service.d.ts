import { ConversationContext, UserIntent } from '../../common/llm/interfaces/llm.service.interface';
import { CandidateEntity } from '../../entities/interfaces/entities.interface';
export declare class AdvancedPromptGeneratorService {
    private readonly logger;
    generateAdvancedChatPrompt(userMessage: string, context: ConversationContext, intent: UserIntent, candidateEntities?: CandidateEntity[], suggestedQuestions?: string[]): string;
    generateAdvancedIntentPrompt(userMessage: string, context: ConversationContext): string;
    private buildSystemContext;
    private buildConversationAnalysis;
    private buildUserProfileSection;
    private buildConversationHistorySection;
    private buildEntitiesSection;
    private buildGuidanceSection;
    private buildResponseFormat;
}
