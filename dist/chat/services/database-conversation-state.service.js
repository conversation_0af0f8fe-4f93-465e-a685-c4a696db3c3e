"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DatabaseConversationStateService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseConversationStateService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
let DatabaseConversationStateService = DatabaseConversationStateService_1 = class DatabaseConversationStateService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(DatabaseConversationStateService_1.name);
    }
    async setConversationContext(sessionId, context, ttlSeconds) {
        try {
            const now = new Date();
            const expiresAt = new Date(now.getTime() + (ttlSeconds || 3600) * 1000);
            await this.prisma.conversationSession.upsert({
                where: { sessionId: sessionId },
                update: {
                    contextData: context,
                    updatedAt: now,
                    expiresAt: expiresAt,
                },
                create: {
                    sessionId: sessionId,
                    userId: context.userId,
                    contextData: context,
                    createdAt: now,
                    updatedAt: now,
                    expiresAt: expiresAt,
                },
            });
            this.logger.debug(`Stored conversation context for session ${sessionId}, user ${context.userId}, expires at ${expiresAt.toISOString()}`);
        }
        catch (error) {
            this.logger.error(`Failed to store conversation context for session ${sessionId}`, error);
            throw error;
        }
    }
    async getConversationContext(sessionId) {
        try {
            const session = await this.prisma.conversationSession.findFirst({
                where: {
                    sessionId: sessionId,
                    expiresAt: {
                        gt: new Date()
                    }
                }
            });
            if (!session) {
                this.logger.debug(`No conversation context found for session ${sessionId}`);
                return null;
            }
            await this.prisma.conversationSession.update({
                where: { sessionId: sessionId },
                data: { updatedAt: new Date() }
            });
            this.logger.debug(`Retrieved conversation context for session ${sessionId}`);
            return session.contextData;
        }
        catch (error) {
            this.logger.error(`Failed to retrieve conversation context for session ${sessionId}`, error);
            return null;
        }
    }
    async deleteConversationContext(sessionId) {
        try {
            await this.prisma.$executeRaw `
        DELETE FROM conversation_sessions 
        WHERE session_id = ${sessionId}
      `;
            this.logger.debug(`Deleted conversation context for session ${sessionId}`);
        }
        catch (error) {
            this.logger.error(`Failed to delete conversation context for session ${sessionId}`, error);
            throw error;
        }
    }
    async getUserActiveSessions(userId) {
        try {
            const sessions = await this.prisma.conversationSession.findMany({
                where: {
                    userId: userId,
                    expiresAt: {
                        gt: new Date()
                    }
                },
                orderBy: {
                    updatedAt: 'desc'
                },
                select: {
                    sessionId: true
                }
            });
            return sessions.map(session => session.sessionId);
        }
        catch (error) {
            this.logger.error(`Failed to get active sessions for user ${userId}`, error);
            return [];
        }
    }
    async hasConversationContext(sessionId) {
        try {
            const session = await this.prisma.conversationSession.findFirst({
                where: {
                    sessionId: sessionId,
                    expiresAt: {
                        gt: new Date()
                    }
                }
            });
            return session !== null;
        }
        catch (error) {
            this.logger.error(`Failed to check if conversation context exists for session ${sessionId}`, error);
            return false;
        }
    }
    async cleanupExpiredConversations() {
        try {
            const result = await this.prisma.conversationSession.deleteMany({
                where: {
                    expiresAt: {
                        lte: new Date()
                    }
                }
            });
            this.logger.debug(`Cleaned up ${result.count} expired conversation sessions`);
            return result.count;
        }
        catch (error) {
            this.logger.error('Failed to cleanup expired conversations', error);
            return 0;
        }
    }
    async getStats() {
        try {
            const [totalResult, activeResult] = await Promise.all([
                this.prisma.$queryRaw `
          SELECT COUNT(*) as count FROM conversation_sessions
        `,
                this.prisma.$queryRaw `
          SELECT COUNT(*) as count FROM conversation_sessions WHERE expires_at > NOW()
        `,
            ]);
            return {
                totalSessions: Number(totalResult[0]?.count || 0),
                activeSessions: Number(activeResult[0]?.count || 0),
            };
        }
        catch (error) {
            this.logger.error('Failed to get conversation stats', error);
            return { totalSessions: 0, activeSessions: 0 };
        }
    }
};
exports.DatabaseConversationStateService = DatabaseConversationStateService;
exports.DatabaseConversationStateService = DatabaseConversationStateService = DatabaseConversationStateService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DatabaseConversationStateService);
//# sourceMappingURL=database-conversation-state.service.js.map