import { ConversationManagerService } from './services/conversation-manager.service';
import { IConversationStateService } from './interfaces/conversation-state.interface';
export declare class DebugConversationController {
    private readonly conversationManager;
    private readonly conversationState;
    private readonly logger;
    constructor(conversationManager: ConversationManagerService, conversationState: IConversationStateService);
    getConversationContext(sessionId: string): Promise<{
        sessionId: string;
        exists: boolean;
        messageCount: number;
        messages: {
            index: number;
            id: string;
            role: "user" | "system" | "assistant";
            content: string;
            timestamp: Date;
        }[];
        conversationStage: "discovery" | "comparison" | "refinement" | "greeting" | "recommendation";
        totalMessages: number;
        lastActiveAt: Date;
        startedAt: Date;
        error?: undefined;
    } | {
        error: any;
        sessionId: string;
        exists: boolean;
        messageCount?: undefined;
        messages?: undefined;
        conversationStage?: undefined;
        totalMessages?: undefined;
        lastActiveAt?: undefined;
        startedAt?: undefined;
    }>;
    getAllSessions(): Promise<{
        message: string;
        totalSessions: string;
        sessions: never[];
        error?: undefined;
    } | {
        error: any;
        totalSessions: number;
        sessions: never[];
        message?: undefined;
    }>;
    clearSession(sessionId: string): Promise<{
        sessionId: string;
        existed: boolean;
        cleared: boolean;
        message: string;
        error?: undefined;
    } | {
        error: any;
        sessionId: string;
        cleared: boolean;
        existed?: undefined;
        message?: undefined;
    }>;
}
