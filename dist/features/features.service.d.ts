import { PrismaService } from '../prisma/prisma.service';
import { CreateFeatureDto } from './dto/create-feature.dto';
import { UpdateFeatureDto } from './dto/update-feature.dto';
import { Feature } from '@generated-prisma';
export declare class FeaturesService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    private getUniqueSlug;
    create(createFeatureDto: CreateFeatureDto): Promise<Feature>;
    findAll(): Promise<Feature[]>;
    findOne(id: string): Promise<Feature>;
    findBySlug(slug: string): Promise<Feature>;
    update(id: string, updateFeatureDto: UpdateFeatureDto): Promise<Feature>;
    remove(id: string): Promise<Feature>;
}
