import { FeaturesService } from './features.service';
import { CreateFeatureDto } from './dto/create-feature.dto';
import { UpdateFeatureDto } from './dto/update-feature.dto';
import { FeatureResponseDto } from './dto/feature-response.dto';
export declare class FeaturesController {
    private readonly featuresService;
    constructor(featuresService: FeaturesService);
    findAllPublic(): Promise<FeatureResponseDto[]>;
    createAdmin(createFeatureDto: CreateFeatureDto): Promise<FeatureResponseDto>;
    findAllAdmin(): Promise<FeatureResponseDto[]>;
    findOneAdmin(idOrSlug: string): Promise<FeatureResponseDto>;
    updateAdmin(id: string, updateFeatureDto: UpdateFeatureDto): Promise<FeatureResponseDto>;
    removeAdmin(id: string): Promise<void>;
    private mapToResponseDto;
    private isUUID;
}
