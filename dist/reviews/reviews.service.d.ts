import { PrismaService } from '../prisma/prisma.service';
import { CreateReviewDto } from './dto/create-review.dto';
import { ListReviewsDto } from './dto/list-reviews.dto';
import { Review, ReviewStatus, User as UserModel } from 'generated/prisma';
import { UpdateReviewDto } from './dto/update-review.dto';
import { ListAdminReviewsDto } from '../admin/reviews/dto/list-admin-reviews.dto';
import { PaginatedResponse } from '../common/interfaces/paginated-response.interface';
import { ActivityLoggerService } from '../common/activity-logger.service';
import { ValidationService } from '../common/validation.service';
export declare class ReviewsService {
    private readonly prisma;
    private readonly activityLogger;
    private readonly validationService;
    private readonly logger;
    constructor(prisma: PrismaService, activityLogger: ActivityLoggerService, validationService: ValidationService);
    createReview(userId: string, entityId: string, createReviewDto: CreateReviewDto): Promise<Review>;
    updateUserReview(reviewId: string, userId: string, updateReviewDto: UpdateReviewDto): Promise<Review>;
    deleteUserReview(reviewId: string, userId: string): Promise<void>;
    getApprovedReviewsForEntity(entityId: string, listReviewsDto: ListReviewsDto): Promise<{
        data: Partial<Review & {
            user: {
                username: string | null;
                profilePictureUrl: string | null;
                id: string;
            };
        }>[];
        total: number;
        page: number;
        limit: number;
    }>;
    updateReviewStatus(reviewId: string, newStatus: ReviewStatus, adminUser: UserModel): Promise<Review>;
    adminDeleteReview(reviewId: string, adminUser: UserModel): Promise<void>;
    adminFindAll(dto: ListAdminReviewsDto): Promise<PaginatedResponse<Review & {
        user: {
            id: string;
            username: string | null;
            email: string;
        };
        entity: {
            id: string;
            name: string;
        };
    }>>;
}
