"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntityTypesController = void 0;
const common_1 = require("@nestjs/common");
const entity_types_service_1 = require("./entity-types.service");
const swagger_1 = require("@nestjs/swagger");
const entity_type_response_dto_1 = require("../entity-types/dto/entity-type-response.dto");
let EntityTypesController = class EntityTypesController {
    constructor(entityTypesService) {
        this.entityTypesService = entityTypesService;
    }
    mapToResponseDto(entityType) {
        return {
            id: entityType.id,
            name: entityType.name,
            slug: entityType.slug,
            description: entityType.description,
            iconUrl: entityType.iconUrl,
            createdAt: entityType.createdAt,
            updatedAt: entityType.updatedAt,
        };
    }
    async findAllPublic() {
        const entityTypes = await this.entityTypesService.findAll();
        if (entityTypes && Array.isArray(entityTypes)) {
            return entityTypes.map((et) => this.mapToResponseDto(et));
        }
        console.warn('[EntityTypesController] entityTypesService.findAll() returned an unexpected structure or no data:', entityTypes);
        return [];
    }
};
exports.EntityTypesController = EntityTypesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all public entity types' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of all public entity types.', type: [entity_type_response_dto_1.EntityTypeResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EntityTypesController.prototype, "findAllPublic", null);
exports.EntityTypesController = EntityTypesController = __decorate([
    (0, swagger_1.ApiTags)('Public - Entity Types'),
    (0, common_1.Controller)('entity-types'),
    __metadata("design:paramtypes", [entity_types_service_1.EntityTypesService])
], EntityTypesController);
//# sourceMappingURL=entity-types.controller.js.map