"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const entity_list_item_response_dto_1 = require("../../entities/dto/entity-list-item-response.dto");
class RecommendationResponseDto {
}
exports.RecommendationResponseDto = RecommendationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of recommended entities',
        type: [entity_list_item_response_dto_1.EntityListItemResponseDto],
    }),
    __metadata("design:type", Array)
], RecommendationResponseDto.prototype, "recommended_entities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'AI-generated explanation of why these entities were recommended',
        example: 'Based on your need for automated code documentation, I recommend: 1) CodePal AI because it offers advanced code analysis and documentation generation features...',
    }),
    __metadata("design:type", String)
], RecommendationResponseDto.prototype, "explanation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The problem description that was analyzed',
        example: 'I need an AI tool to help me generate code documentation automatically',
    }),
    __metadata("design:type", String)
], RecommendationResponseDto.prototype, "problem_description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of candidate entities that were analyzed',
        example: 15,
    }),
    __metadata("design:type", Number)
], RecommendationResponseDto.prototype, "candidates_analyzed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The LLM provider that generated the recommendation',
        example: 'OPENAI',
    }),
    __metadata("design:type", String)
], RecommendationResponseDto.prototype, "llm_provider", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the recommendation was generated',
        example: '2024-06-20T01:00:00.000Z',
    }),
    __metadata("design:type", Date)
], RecommendationResponseDto.prototype, "generated_at", void 0);
//# sourceMappingURL=recommendation-response.dto.js.map