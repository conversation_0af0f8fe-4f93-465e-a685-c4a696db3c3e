"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const list_entities_dto_1 = require("../../entities/dto/list-entities.dto");
class RecommendationFiltersDto extends (0, swagger_1.OmitType)(list_entities_dto_1.ListEntitiesDto, [
    'page',
    'limit',
    'sortBy',
    'sortOrder'
]) {
    constructor() {
        super(...arguments);
        this.max_candidates = 20;
    }
}
exports.RecommendationFiltersDto = RecommendationFiltersDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Maximum number of candidate entities to consider for LLM analysis',
        example: 20,
        minimum: 5,
        maximum: 50,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(5),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Number)
], RecommendationFiltersDto.prototype, "max_candidates", void 0);
//# sourceMappingURL=recommendation-filters.dto.js.map