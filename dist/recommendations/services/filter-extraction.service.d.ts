import { RecommendationFiltersDto } from '../dto/recommendation-filters.dto';
export declare class FilterExtractionService {
    private readonly logger;
    extractFiltersFromDescription(description: string): Promise<Partial<RecommendationFiltersDto>>;
    private extractEntityTypes;
    private extractTechnicalLevels;
    private extractBudgetFilters;
    private extractPlatforms;
    private extractFrameworks;
    private extractUseCases;
    private extractKeyFeatures;
    private extractJobFilters;
    private extractCourseFilters;
    private extractEventFilters;
    private extractHardwareFilters;
    private matchesAny;
}
