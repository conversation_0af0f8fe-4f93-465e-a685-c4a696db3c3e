/**
 * Diagnostic script to check if the chat service is using the database correctly
 * This helps identify if the application is using the new DatabaseConversationStateService
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TEST_SESSION_ID = 'chat_test_new_session_123';

// Your JWT token
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZDsMWgPjk388pQEoLFt7MeuNrPFYgaT6rGsoefU6SJA';

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testChatEndpoint() {
  console.log('🔍 Diagnosing Chat Service...\n');
  
  try {
    // Test 1: Send a message with context
    console.log('📝 Test 1: Sending message with context...');
    const response1 = await axios.post(
      `${BASE_URL}/chat`,
      {
        message: 'My name is <PERSON> and I work in education. Remember this information.',
        session_id: TEST_SESSION_ID
      },
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log(`✅ Response received: "${response1.data.response.substring(0, 100)}..."`);
    console.log(`📋 Session ID: ${response1.data.session_id}`);
    
    await sleep(2000);
    
    // Test 2: Send follow-up message to test memory
    console.log('\n📝 Test 2: Testing conversation memory...');
    const response2 = await axios.post(
      `${BASE_URL}/chat`,
      {
        message: 'What is my name and what field do I work in?',
        session_id: TEST_SESSION_ID
      },
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log(`✅ Response received: "${response2.data.response.substring(0, 200)}..."`);
    
    // Analyze the response
    const response = response2.data.response.toLowerCase();
    const remembersName = response.includes('john');
    const remembersField = response.includes('education');
    const isGenericResponse = response.includes('trouble accessing') || response.includes('start fresh');
    
    console.log('\n📊 Memory Test Results:');
    console.log(`   Remembers name (John): ${remembersName ? '✅' : '❌'}`);
    console.log(`   Remembers field (education): ${remembersField ? '✅' : '❌'}`);
    console.log(`   Generic/fallback response: ${isGenericResponse ? '❌' : '✅'}`);
    
    if (isGenericResponse) {
      console.log('\n⚠️  ISSUE DETECTED: Application is giving generic fallback responses');
      console.log('   This suggests the DatabaseConversationStateService is not working properly');
    } else if (remembersName && remembersField) {
      console.log('\n🎉 SUCCESS: Conversation memory is working!');
    } else {
      console.log('\n⚠️  PARTIAL SUCCESS: Some memory issues detected');
    }
    
    // Test 3: Try to get conversation history
    console.log('\n📝 Test 3: Testing conversation history endpoint...');
    try {
      const historyResponse = await axios.get(
        `${BASE_URL}/chat/${TEST_SESSION_ID}/history`,
        {
          headers: {
            'Authorization': `Bearer ${JWT_TOKEN}`
          }
        }
      );
      
      console.log(`✅ History retrieved: ${historyResponse.data.messages.length} messages`);
      console.log(`📋 Total messages: ${historyResponse.data.total_messages}`);
      console.log(`🎯 Conversation stage: ${historyResponse.data.conversation_stage}`);
      
    } catch (historyError) {
      console.log(`❌ History endpoint failed: ${historyError.response?.status} - ${historyError.response?.data?.message || historyError.message}`);
    }
    
    console.log('\n🔍 DIAGNOSIS COMPLETE');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

async function checkDatabaseDirectly() {
  console.log('\n🗄️  Checking database directly...');
  
  // Note: This would require database access, which we can't do from this script
  // But we can suggest manual checks
  console.log('📋 Manual database checks you can perform:');
  console.log('1. Check if conversation_sessions table has any records');
  console.log('2. Look for any database connection errors in application logs');
  console.log('3. Verify the application is using DatabaseConversationStateService');
}

async function suggestSolutions() {
  console.log('\n💡 SUGGESTED SOLUTIONS:');
  console.log('');
  console.log('1. 🔄 RESTART APPLICATION:');
  console.log('   - Stop your current application (Ctrl+C)');
  console.log('   - Run: npm run start:dev');
  console.log('   - Wait for "Prisma Client connected successfully" message');
  console.log('');
  console.log('2. 🔍 CHECK APPLICATION LOGS:');
  console.log('   - Look for database connection errors');
  console.log('   - Look for "DatabaseConversationStateService" initialization');
  console.log('   - Check for any Prisma-related errors');
  console.log('');
  console.log('3. 🧪 VERIFY COMPILATION:');
  console.log('   - Run: npm run build');
  console.log('   - Check for TypeScript compilation errors');
  console.log('');
  console.log('4. 🗄️ CHECK DATABASE:');
  console.log('   - Verify conversation_sessions table exists');
  console.log('   - Check database connection string in .env');
  console.log('');
  console.log('5. 🔧 MANUAL VERIFICATION:');
  console.log('   - Check src/chat/chat.module.ts uses DatabaseConversationStateService');
  console.log('   - Verify import paths are correct');
}

// Run the diagnostic
if (require.main === module) {
  testChatEndpoint()
    .then(() => {
      checkDatabaseDirectly();
      suggestSolutions();
      console.log('\n🏁 Diagnostic complete. Follow the suggested solutions above.');
    })
    .catch((error) => {
      console.error('\n💥 Diagnostic failed:', error);
      suggestSolutions();
    });
}

module.exports = { testChatEndpoint };
